import { Adjustment, SaveAdjustmentResponse, Week } from "src/graphql/__generated__/gql-ts-types";
import { ContextValue } from "../../../context";
import { SaveAdjustmentProvider } from "../providers/saveAdjustment.provider";

export const SaveAdjustmentResolver = async (
    parent: any,
    args: { adjustment: Adjustment },
    context: ContextValue
): Promise<SaveAdjustmentResponse> => {
    const ForecastService = context?.injector?.get(SaveAdjustmentProvider.provide);
    await ForecastService?.SaveAdjustment(createSaveAdjustmentRequestBody(args.adjustment));
    const calculateAdjustmentRes = await ForecastService?.CalculateAdjustment(createCalculateAdjustmentRequestBody(args.adjustment));
    const saveCategoryAdjustmentRes = await ForecastService?.SaveCategoryAdjustment({ categoriesAdjustments: calculateAdjustmentRes.data.data });
    const updateForecastSnapshotRes = await ForecastService?.UpdateForecastSnapshot(createPayloadForUpdateForecastSnapshot(args.adjustment));
    return saveCategoryAdjustmentRes.data;
};

interface SnapShotUpdateBody {
    divisionIds: string[];
    keyAttributeNames: string[];
    keyAttributeValues: string[];
    fiscalWeeks: string[];
    updatedBy: string;
}

const getAllowances = (newAggregatedData: any, previousAggregatedData: any) => {
    const allowances = {
        line7RetailsAllowancesNbr: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesNbr: 0
    };
    if (newAggregatedData?.line7RetailsAllowancesNbr === null || newAggregatedData?.line7RetailsAllowancesNbr === undefined) {
        allowances.line7RetailsAllowancesNbr = previousAggregatedData.line7RetailsAllowancesNbr;
        allowances.line7RetailsSellingAllowancesNbr = previousAggregatedData.line7RetailsSellingAllowancesNbr;
        allowances.line7RetailsNonSellingAllowancesNbr = previousAggregatedData.line7RetailsNonSellingAllowancesNbr;
    } else {
        allowances.line7RetailsAllowancesNbr = newAggregatedData.line7RetailsAllowancesNbr;
        allowances.line7RetailsSellingAllowancesNbr = newAggregatedData.line7RetailsSellingAllowancesNbr;
        allowances.line7RetailsNonSellingAllowancesNbr = newAggregatedData.line7RetailsNonSellingAllowancesNbr;
    }
    return allowances;
}

const getSuppliesPackaging = (newAggregatedData: any, previousAggregatedData: any) => {
    if (newAggregatedData?.line6SuppliesPackagingNbr === null || newAggregatedData?.line6SuppliesPackagingNbr === undefined) {
        return previousAggregatedData.line6SuppliesPackagingNbr;
    } else {
        return newAggregatedData.line6SuppliesPackagingNbr;
    }
}

const createPayloadForUpdateForecastSnapshot = (data: any): SnapShotUpdateBody => {
    const snapShotBody = {
        divisionIds: data.divisionIds,
        keyAttributeNames: [data?.deptId ? "Department" : "Desk"],
        keyAttributeValues: [data.deptId || data.deskId],
        fiscalWeeks: data.weeks.map((item: any) => item.fiscalWeekNbr.toString()),
        updatedBy: data.updatedBy
    };
    return snapShotBody;
}

const createSaveAdjustmentRequestBody = (data: any) => {

    const weekItem = (item: Week, entityId: string) => {
        const newAggregatedData = item.newAggregatedData;
        const previousAggregatedData = item.previousAggregatedData;
        const allowances = getAllowances(newAggregatedData, previousAggregatedData);
        const suppliesPackaging = getSuppliesPackaging(newAggregatedData, previousAggregatedData);
        return {
            entityId,
            fiscalWeekNbr: item?.fiscalWeekNbr,
            line1SalesToPublicNbr: newAggregatedData?.line1PublicToSalesNbr,
            line1SalesToPublicPct: newAggregatedData?.line1PublicToSalesPct,
            line5BookGrossProfitNbr: newAggregatedData?.line5BookGrossProfitNbr,
            line5BookGrossProfitPct: newAggregatedData?.line5BookGrossProfitPct,
            line5MarkDownsNbr: newAggregatedData?.line5MarkDownsNbr,
            line5MarkDownsPct: newAggregatedData?.line5MarkDownsPct,
            line5ShrinkNbr: newAggregatedData?.line5ShrinkNbr,
            line5ShrinkPct: newAggregatedData?.line5ShrinkPct,
            line6SuppliesPackagingNbr: suppliesPackaging,
            line7RetailsAllowancesNbr: allowances.line7RetailsAllowancesNbr,
            line7RetailsSellingAllowancesNbr: allowances.line7RetailsSellingAllowancesNbr,
            line7RetailsNonSellingAllowancesNbr: allowances.line7RetailsNonSellingAllowancesNbr,
            reason: item?.reason,
            comment: item?.comment,
            editedColumns: item?.editedColumns
        };
    }


    const weeks = data?.weeks.map((item: Week) => {
        return weekItem(item, data.deptId || data.deskId);
    })

    return {
        entityType: data?.deptId ? "Department" : "Desk",
        updatedBy: data?.updatedBy,
        adjustments: [{
            divisionId: data?.divisionIds[0],
            adjustmentData: weeks
        }]
    }

}

const createCalculateAdjustmentRequestBody = (data: any) => {

    const weekItem = (item: any) => {
        const newAggregatedData = item?.newAggregatedData;
        const oldAggregatedData = item?.previousAggregatedData;
        const allowances = getAllowances(newAggregatedData, oldAggregatedData);
        const suppliesPackaging = getSuppliesPackaging(newAggregatedData, oldAggregatedData);
        if (data?.isReset) {
            return {
                fiscalWeekNbr: item?.fiscalWeekNbr,
                line1PublicToSalesNbr: newAggregatedData?.line1PublicToSalesNbr,
                line5BookGrossProfitNbr: newAggregatedData?.line5BookGrossProfitNbr,
                line5MarkDownsNbr: newAggregatedData?.line5MarkDownsNbr,
                line5ShrinkNbr: newAggregatedData?.line5ShrinkNbr,
                line6SuppliesPackagingNbr: newAggregatedData?.line6SuppliesPackagingNbr,
                line7RetailsAllowancesNbr: newAggregatedData?.line7RetailsAllowancesNbr,
                line7RetailsSellingAllowancesNbr: newAggregatedData?.line7RetailsSellingAllowancesNbr,
                line7RetailsNonSellingAllowancesNbr: newAggregatedData?.line7RetailsNonSellingAllowancesNbr,
                reason: item?.reason,
                comment: item?.comment
            }
        }
        return {
            fiscalWeekNbr: item?.fiscalWeekNbr,
            line1PublicToSalesNbr: newAggregatedData?.line1PublicToSalesNbr - oldAggregatedData?.line1PublicToSalesNbr,
            line5BookGrossProfitNbr: newAggregatedData?.line5BookGrossProfitNbr - oldAggregatedData?.line5BookGrossProfitNbr,
            line5MarkDownsNbr: newAggregatedData?.line5MarkDownsNbr - oldAggregatedData?.line5MarkDownsNbr,
            line5ShrinkNbr: newAggregatedData?.line5ShrinkNbr - oldAggregatedData?.line5ShrinkNbr,
            line6SuppliesPackagingNbr: suppliesPackaging,
            line7RetailsAllowancesNbr: allowances.line7RetailsAllowancesNbr,
            line7RetailsSellingAllowancesNbr: allowances.line7RetailsSellingAllowancesNbr,
            line7RetailsNonSellingAllowancesNbr: allowances.line7RetailsNonSellingAllowancesNbr,
            reason: item?.reason,
            comment: item?.comment
        };
    }

    const weeks = data?.weeks.map((item: any) => {
        return weekItem(item);
    })

    return {
        smicCategoryIds: data?.smicCategoryIds,
        userId: data?.updatedBy,
        reset: data?.isReset,
        divisionData: [{
            divisionId: data?.divisionIds[0],
            weekData: weeks
        }]
    }

}