input AdjustmentWorksheetReq {
    currentFiscalYearNbr: Int
    quarterNbr: Int
    currentFiscalPeriodNbr: Int
    currentFiscalWeekNbr: Int
    smicCategoryIds: [String]
    deptIds: [String]
    divisionIds: [String]
    quarterEndingDate:String
}
type AdjustmentWorksheetAggregated {
    aggregatedLevel: String
    subRow: String
    mainRow: String
    line1PublicToSalesNbr: Float
    line1PublicToSalesPct: Float
    line5BookGrossProfitNbr: Float
    line5BookGrossProfitPct: Float
    line5MarkDownsNbr: Float
    line5MarkDownsPct: Float
    line5ShrinkNbr: Float
    line5ShrinkPct: Float
    line5RealGrossProfitNbr: Float
    line5RealGrossProfitPct: Float
    line6SuppliesPackagingNbr: Float
    line7RetailsAllowancesNbr: Float
    line7RetailsAllowancesPct: Float
    line7RetailsSellingAllowancesNbr: Float
    line7RetailsSellingAllowancesPct: Float
    line7RetailsNonSellingAllowancesNbr: Float
    line7RetailsNonSellingAllowancesPct: Float
    line8RealGrossProfitNbr: Float
    line8RealGrossProfitPct: Float
    fiscalYearNbr: Int
    fiscalQuarterNbr: Int
    fiscalPeriodNbr: Int
    fiscalWeekNbr: Int
    fiscalWeekEnding: String
    comment: String
    forecastType: String
    lastUpdatedUserRole: String
    reason: String
    state: String
    versionNbr: Int
    createdTs: String
    updatedTs: String
    sourceTs:String
    createdBy: String
    updatedBy: String
}

type nonAggregatedForecastData {
    fiscalYearNbr: Int
    fiscalQuarterNbr: Int
    fiscalPeriodNbr: Int
    fiscalWeekNbr: Int
    smicCategoryId: Int
    deptId: Int
    divisionId: Int
    line1PublicToSalesNbr: Float
    line5BookGrossProfitNbr: Float
    line5BookGrossProfitPct: Float
    line1BillOutGrossPct: Float
    line5MarkDownsNbr: Float
    line5MarkDownsPct: Float
    line5ShrinkNbr: Float
    line5ShrinkPct: Float
    line4CostOfSalesNbr: Float
    line5RealGrossProfitNbr: Float
    line5RealGrossProfitPct: Float
    line6SuppliesPackagingNbr: Float
    line7RetailsAllowancesNbr: Float
    line7RetailsAllowancesPct: Float
    line7RetailsSellingAllowancesNbr: Float
    line7RetailsSellingAllowancesPct: Float
    line7RetailsNonSellingAllowancesNbr: Float
    line7RetailsNonSellingAllowancesPct: Float
    line8RealGrossProfitNbr: Float
    line8RealGrossProfitPct: Float
    comment: String
    forecastType: String
    lastUpdatedUserRole: String
    reason: String
    state: String
    versionNbr: Int
    createdTs: String
    updatedTs: String
    sourceTs:String
    createdBy: String
    updatedBy: String
}

type AdjustmentWorksheetRes {
    adjustmentWorksheetData: [AdjustmentWorksheetAggregated]
    forecastData: [nonAggregatedForecastData]
}

type Query {
    getAdjustmentWorksheetData(
        adjustmentWorksheetReq: AdjustmentWorksheetReq
    ): AdjustmentWorksheetRes
}
