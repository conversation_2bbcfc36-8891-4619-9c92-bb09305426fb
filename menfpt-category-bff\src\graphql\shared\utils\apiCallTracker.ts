import logger from "./logger";

export interface ApiCall {
    bffMethod: string;
    apiMethod: string;
    apiUrl: string;
}

export class ApiCallTracker {
    private calls: ApiCall[] = [];

    public track(bffMethod: string, apiMethod: string, apiUrl: string) {
        if (!bffMethod) {
            return;
        }
        this.calls.push({ bffMethod, apiMethod, apiUrl });
    }

    public logSummary() {
        if (this.calls.length === 0) {
            return;
        }

        const summary: { [bffMethod: string]: { [api: string]: number } } = {};

        for (const call of this.calls) {
            if (!summary[call.bffMethod]) {
                summary[call.bffMethod] = {};
            }
            const apiKey = `${call.apiMethod} ${call.apiUrl}`;
            if (!summary[call.bffMethod][apiKey]) {
                summary[call.bffMethod][apiKey] = 0;
            }
            summary[call.bffMethod][apiKey]++;
        }

        const mainSeparator =
            "==================================================";
        const methodSeparator =
            "--------------------------------------------------";

        let logMessage = `\n\n${mainSeparator}\n           BFF API Call Summary\n${mainSeparator}`;

        const bffMethods = Object.keys(summary);
        bffMethods.forEach((bffMethod, index) => {
            logMessage += `\n\nBFF Method: ${bffMethod}\n`;
            for (const api in summary[bffMethod]) {
                logMessage += `  - Called API: ${api} (${summary[bffMethod][api]} time(s))\n`;
            }

            if (index < bffMethods.length - 1) {
                logMessage += `\n${methodSeparator}`;
            }
        });

        logMessage += `\n${mainSeparator}\n`;
        logger.info(logMessage);
    }
}
