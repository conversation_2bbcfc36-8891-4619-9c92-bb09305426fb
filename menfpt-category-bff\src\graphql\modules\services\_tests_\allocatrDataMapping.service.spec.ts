import "reflect-metadata";
import { AllocatrDataMappingService } from "../allocatrDataMapping.service";
import { GraphQLError } from "graphql";
import { MSP_DATA_POST_FAILURE } from "../../../shared/constants/errorCodes";

jest.mock("axios");
jest.mock("../../../shared/classes/BaseAPI.service");

const mockForecastData = {
    message: "Records found",
    data: {
        data: [
            {
                divisionId: "20",
                deptId: "301",
                smicCategoryId: 101,
                fiscalWeekNbr: 202501,
                line1PublicToSalesNbr: 1000000,
                line1PublicToSalesPct: 0.25,
                line5BookGrossProfitNbr: 250000,
                line5BookGrossProfitPct: 0.25
            },
            {
                divisionId: "21",
                deptId: "302",
                smicCategoryId: 102,
                fiscalWeekNbr: 202502,
                line1PublicToSalesNbr: 1500000,
                line1PublicToSalesPct: 0.3,
                line5BookGrossProfitNbr: 450000,
                line5BookGrossProfitPct: 0.3
            }
        ]
    },
    timestamp: "2025-02-23T12:57:03.303Z"
};

const mockProjectionData = {
    message: "Records found",
    data: {
        data: [
            {
                divisionId: "20",
                deptId: "301",
                smicCategoryId: 101,
                fiscalWeekNbr: 202501,
                projectedSales: 1200000,
                projectedProfit: 300000
            }
        ]
    },
    timestamp: "2025-02-23T12:57:03.303Z"
};

const mockActualData = {
    message: "Records found",
    data: {
        data: [
            {
                divisionId: "20",
                deptId: "301",
                smicCategoryId: 101,
                fiscalWeekNbr: 202401,
                actualSales: 950000,
                actualProfit: 237500
            },
            {
                divisionId: "20",
                deptId: "301",
                smicCategoryId: 101,
                fiscalWeekNbr: 202501,
                actualSales: 1000000,
                actualProfit: 250000
            }
        ]
    },
    timestamp: "2025-02-23T12:57:03.303Z"
};

describe("AllocatrDataMappingService", () => {
    let service: AllocatrDataMappingService;

    beforeEach(() => {
        service = new AllocatrDataMappingService();
    });

    describe("getForecastDataForDashboard", () => {
        it("should return forecast data successfully", async () => {
            const response = {
                data: mockForecastData
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20", "21"],
                deptIds: ["301", "302"],
                smicCategoryIds: [101, 102],
                fiscalWeekNbrs: [202501, 202502]
            };

            const result = await service.getForecastDataForDashboard(payload);

            expect(result).toEqual(mockForecastData.data);
            expect(service.post).toHaveBeenCalledWith("aggregate/search/v2", {
                body: { ...payload, fetchAll: true }
            });
        });

        it("should return empty array when response.data is missing", async () => {
            const response = {
                data: null
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"]
            };

            const result = await service.getForecastDataForDashboard(payload);

            expect(result).toEqual([]);
        });

        it("should return empty array when response.data.data is missing", async () => {
            const response = {
                data: {
                    message: "No data found"
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"]
            };

            const result = await service.getForecastDataForDashboard(payload);

            expect(result).toBeUndefined();
        });

        it("should throw GraphQLError when API call fails", async () => {
            const error = new Error("API Error");
            const mockErrorPayload = {
                httpStatus: 500,
                errorCode: "API_ERROR",
                name: "APIError",
                message: "Internal server error",
                url: "aggregate/search/v2"
            };

            jest.spyOn(service, "createErrorPayload").mockReturnValue(
                mockErrorPayload
            );
            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"]
            };

            try {
                await service.getForecastDataForDashboard(payload);
            } catch (err) {
                const graphQLError = err as GraphQLError;
                expect(graphQLError).toBeInstanceOf(GraphQLError);
                expect(graphQLError.message).toBe("Error while fetching data");
                expect(graphQLError.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });

    describe("getProjectionDataForDashboard", () => {
        it("should return projection data successfully", async () => {
            const response = {
                data: mockProjectionData
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"],
                smicCategoryIds: [101],
                fiscalWeekNbrs: [202501]
            };

            const result = await service.getProjectionDataForDashboard(payload);

            expect(result).toEqual(mockProjectionData.data);
            expect(service.post).toHaveBeenCalledWith(
                "projection/aggregate/search",
                {
                    body: { ...payload, fetchAll: true }
                }
            );
        });

        it("should return undefined when response.data.data is missing", async () => {
            const response = {
                data: {
                    message: "No data found"
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"]
            };

            const result = await service.getProjectionDataForDashboard(payload);

            expect(result).toBeUndefined();
        });

        it("should throw GraphQLError when API call fails", async () => {
            const error = new Error("API Error");
            const mockErrorPayload = {
                httpStatus: 500,
                errorCode: "API_ERROR",
                name: "APIError",
                message: "Internal server error",
                url: "projection/aggregate/search"
            };

            jest.spyOn(service, "createErrorPayload").mockReturnValue(
                mockErrorPayload
            );
            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"]
            };

            try {
                await service.getProjectionDataForDashboard(payload);
            } catch (err) {
                const graphQLError = err as GraphQLError;
                expect(graphQLError).toBeInstanceOf(GraphQLError);
                expect(graphQLError.message).toBe("Error while fetching data");
                expect(graphQLError.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });

    describe("getActualDataForDashboard", () => {
        it("should return actual data successfully with previous year periods", async () => {
            const response = {
                data: mockActualData
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"],
                smicCategoryIds: [101],
                fiscalPeriodNbrs: [202501, 202502]
            };

            const result = await service.getActualDataForDashboard(payload);

            expect(result).toEqual(mockActualData.data);
            expect(service.post).toHaveBeenCalledWith(
                "actuals/aggregate/search",
                {
                    body: {
                        ...payload,
                        fiscalPeriodNbrs: [202501, 202502, 202401, 202402]
                    }
                }
            );
        });

        it("should handle empty fiscalPeriodNbrs array", async () => {
            const response = {
                data: mockActualData
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"],
                smicCategoryIds: [101],
                fiscalPeriodNbrs: []
            };

            const result = await service.getActualDataForDashboard(payload);

            expect(result).toEqual(mockActualData.data);
            expect(service.post).toHaveBeenCalledWith(
                "actuals/aggregate/search",
                {
                    body: {
                        ...payload,
                        fiscalPeriodNbrs: []
                    }
                }
            );
        });

        it("should handle non-array fiscalPeriodNbrs", async () => {
            const response = {
                data: mockActualData
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"],
                smicCategoryIds: [101],
                fiscalPeriodNbrs: "202501"
            };

            const result = await service.getActualDataForDashboard(payload);

            expect(result).toEqual(mockActualData.data);
            expect(service.post).toHaveBeenCalledWith(
                "actuals/aggregate/search",
                {
                    body: {
                        ...payload,
                        fiscalPeriodNbrs: "202501"
                    }
                }
            );
        });

        it("should handle single fiscal period number", async () => {
            const response = {
                data: mockActualData
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"],
                smicCategoryIds: [101],
                fiscalPeriodNbrs: [202501]
            };

            const result = await service.getActualDataForDashboard(payload);

            expect(result).toEqual(mockActualData.data);
            expect(service.post).toHaveBeenCalledWith(
                "actuals/aggregate/search",
                {
                    body: {
                        ...payload,
                        fiscalPeriodNbrs: [202501, 202401]
                    }
                }
            );
        });

        it("should filter out duplicate previous year periods", async () => {
            const response = {
                data: mockActualData
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"],
                smicCategoryIds: [101],
                fiscalPeriodNbrs: [202501, 202401] // 202401 is already a previous year period
            };

            const result = await service.getActualDataForDashboard(payload);

            expect(result).toEqual(mockActualData.data);
            expect(service.post).toHaveBeenCalledWith(
                "actuals/aggregate/search",
                {
                    body: {
                        ...payload,
                        fiscalPeriodNbrs: [202501, 202401, 202301] // Include all previous year periods
                    }
                }
            );
        });

        it("should return undefined when response.data.data is missing", async () => {
            const response = {
                data: {
                    message: "No data found"
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"],
                fiscalPeriodNbrs: [202501]
            };

            const result = await service.getActualDataForDashboard(payload);

            expect(result).toBeUndefined();
        });

        it("should throw GraphQLError when API call fails", async () => {
            const error = new Error("API Error");
            const mockErrorPayload = {
                httpStatus: 500,
                errorCode: "API_ERROR",
                name: "APIError",
                message: "Internal server error",
                url: "actuals/aggregate/search"
            };

            jest.spyOn(service, "createErrorPayload").mockReturnValue(
                mockErrorPayload
            );
            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            const payload = {
                divisionIds: ["20"],
                deptIds: ["301"],
                fiscalPeriodNbrs: [202501]
            };

            try {
                await service.getActualDataForDashboard(payload);
            } catch (err) {
                const graphQLError = err as GraphQLError;
                expect(graphQLError).toBeInstanceOf(GraphQLError);
                expect(graphQLError.message).toBe("Error while fetching data");
                expect(graphQLError.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });
});
