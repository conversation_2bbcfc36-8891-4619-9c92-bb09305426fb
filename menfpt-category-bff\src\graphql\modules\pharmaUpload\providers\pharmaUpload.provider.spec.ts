import "reflect-metadata";
import { PharmaUploadProvider } from "./pharmaUpload.provider";
import { PharmaUploadService } from "../services/pharmaUpload.service";

describe("PharmaUploadProvider", () => {
    describe("Provider Configuration", () => {
        it("Should have correct provider structure", () => {
            expect(PharmaUploadProvider).toHaveProperty('provide');
            expect(PharmaUploadProvider).toHaveProperty('useClass');
        });

        it("Should provide PharmaUploadService", () => {
            expect(PharmaUploadProvider.provide).toBe(PharmaUploadService);
        });

        it("Should use PharmaUploadService class", () => {
            expect(PharmaUploadProvider.useClass).toBe(PharmaUploadService);
        });

        it("Should have provide property as a function", () => {
            expect(typeof PharmaUploadProvider.provide).toBe('function');
        });

        it("Should have useClass property as a function", () => {
            expect(typeof PharmaUploadProvider.useClass).toBe('function');
        });
    });

    describe("Dependency Injection", () => {
        it("Should be able to instantiate service through provider", () => {
            const serviceInstance = new PharmaUploadProvider.useClass();
            expect(serviceInstance).toBeInstanceOf(PharmaUploadService);
        });

        it("Should provide the same service class as useClass", () => {
            expect(PharmaUploadProvider.provide).toBe(PharmaUploadProvider.useClass);
        });

        it("Should have provider token that matches service class", () => {
            const serviceClass = PharmaUploadProvider.provide;
            const useClass = PharmaUploadProvider.useClass;
            expect(serviceClass).toBe(useClass);
        });
    });

    describe("Provider Integration", () => {
        it("Should work with dependency injection container", () => {
            const mockContainer = {
                get: jest.fn().mockReturnValue(new PharmaUploadService())
            };

            const service = mockContainer.get(PharmaUploadProvider.provide);
            expect(service).toBeInstanceOf(PharmaUploadService);
            expect(mockContainer.get).toHaveBeenCalledWith(PharmaUploadProvider.provide);
        });

        it("Should provide service with all required methods", () => {
            const service = new PharmaUploadProvider.useClass();
            
            expect(typeof service.getUploadedDocuments).toBe('function');
        });

    });

    describe("Provider Export", () => {
        it("Should be exported as a named export", () => {
            expect(PharmaUploadProvider).toBeDefined();
            expect(typeof PharmaUploadProvider).toBe('object');
        });

        it("Should not be null or undefined", () => {
            expect(PharmaUploadProvider).not.toBeNull();
            expect(PharmaUploadProvider).not.toBeUndefined();
        });
    });

    describe("Provider Consistency", () => {
        it("Should maintain consistent provider configuration", () => {
            const provider1 = PharmaUploadProvider;
            const provider2 = PharmaUploadProvider;
            
            expect(provider1.provide).toBe(provider2.provide);
            expect(provider1.useClass).toBe(provider2.useClass);
        });

        it("Should not allow modification of provider properties", () => {
            const originalProvide = PharmaUploadProvider.provide;
            const originalUseClass = PharmaUploadProvider.useClass;
            
            PharmaUploadProvider.provide = null as any;
            PharmaUploadProvider.useClass = null as any;
            
            expect(PharmaUploadProvider.provide).toBeDefined();
            expect(PharmaUploadProvider.useClass).toBeDefined();
        });
    });
}); 