import "reflect-metadata";
import { ForecastChangeLogResolver } from "./forecastChangeLog.resolver";
import { ForecastChangeLogReq } from "src/graphql/__generated__/gql-ts-types";

const mockGetForecastChangeLog = jest.fn();
const mockForecastChangeLogService = {
    getForecastChangeLog: mockGetForecastChangeLog
};
const mockInjector = {
    get: jest.fn(() => mockForecastChangeLogService)
};
const baseContext = {
    injector: mockInjector,
    apiCallTracker: {}
};

describe("ForecastChangeLogResolver", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("returns formatted data for valid request", async () => {
        const req: ForecastChangeLogReq = {
            keyAttributeName: "division",
            keyAttributeValue: ["A"],
            divisionId: ["1"]
        };
        const auditHistory = [
            {
                date: "2024-01-01T00:00:00.000Z",
                weeks: [
                    {
                        fiscalWeekNbr: 1,
                        divisionId: "1",
                        keyAttributeName: "division",
                        keyAttributeValue: "A",
                        after: {
                            updated_by: "user1",
                            reason: "r",
                            comment: "c",
                            edited_columns: "col",
                            value: "new"
                        },
                        before: {
                            updated_by: "user1",
                            reason: "r",
                            comment: "c",
                            edited_columns: "col",
                            value: "old"
                        }
                    }
                ]
            }
        ];
        mockGetForecastChangeLog.mockResolvedValueOnce({ auditHistory });
        const args = { forecastChangeLogReqest: req };
        const result = await ForecastChangeLogResolver(
            null,
            args,
            baseContext as any
        );
        expect(result).toBeInstanceOf(Array);
        expect(result[0]).toHaveProperty("updatedTimestamp");
        expect(result[0]).toHaveProperty("updatedBy");
        expect(result[0]).toHaveProperty("editedColumns");
        expect(result[0]).toHaveProperty("updatedMetrics");
        expect(mockGetForecastChangeLog).toHaveBeenCalledWith(req);
    });

    it("throws error if payload is empty", async () => {
        const args = { forecastChangeLogReqest: {} as ForecastChangeLogReq };
        await expect(
            ForecastChangeLogResolver(null, args, baseContext as any)
        ).rejects.toThrow("Invalid request: Payload cannot be empty");
    });

    it("throws error if auditHistory is missing", async () => {
        mockGetForecastChangeLog.mockResolvedValueOnce({});
        const req: ForecastChangeLogReq = {
            keyAttributeName: "division",
            keyAttributeValue: ["A"],
            divisionId: ["1"]
        };
        const args = { forecastChangeLogReqest: req };
        await expect(
            ForecastChangeLogResolver(null, args, baseContext as any)
        ).rejects.toThrow("Invalid or missing auditHistory data");
    });

    it("throws error if auditHistory is not an array", async () => {
        mockGetForecastChangeLog.mockResolvedValueOnce({
            auditHistory: "not-an-array"
        });
        const req: ForecastChangeLogReq = {
            keyAttributeName: "division",
            keyAttributeValue: ["A"],
            divisionId: ["1"]
        };
        const args = { forecastChangeLogReqest: req };
        await expect(
            ForecastChangeLogResolver(null, args, baseContext as any)
        ).rejects.toThrow("Invalid or missing auditHistory data");
    });

    it("returns empty array if auditHistory is empty", async () => {
        mockGetForecastChangeLog.mockResolvedValueOnce({ auditHistory: [] });
        const req: ForecastChangeLogReq = {
            keyAttributeName: "division",
            keyAttributeValue: ["A"],
            divisionId: ["1"]
        };
        const args = { forecastChangeLogReqest: req };
        const result = await ForecastChangeLogResolver(
            null,
            args,
            baseContext as any
        );
        expect(result).toEqual([]);
    });

    it("handles error thrown by service", async () => {
        mockGetForecastChangeLog.mockRejectedValueOnce(
            new Error("Service error")
        );
        const req: ForecastChangeLogReq = {
            keyAttributeName: "division",
            keyAttributeValue: ["A"],
            divisionId: ["1"]
        };
        const args = { forecastChangeLogReqest: req };
        await expect(
            ForecastChangeLogResolver(null, args, baseContext as any)
        ).rejects.toThrow("Invalid or missing auditHistory data");
    });

    it("handles weeks with missing fields gracefully", async () => {
        const auditHistory = [
            {
                date: "2024-01-01T00:00:00.000Z",
                weeks: [
                    {
                        // missing most fields
                        after: {},
                        before: {}
                    }
                ]
            }
        ];
        mockGetForecastChangeLog.mockResolvedValueOnce({ auditHistory });
        const req: ForecastChangeLogReq = {
            keyAttributeName: "division",
            keyAttributeValue: ["A"],
            divisionId: ["1"]
        };
        const args = { forecastChangeLogReqest: req };
        const result = await ForecastChangeLogResolver(
            null,
            args,
            baseContext as any
        );
        expect(result).toBeInstanceOf(Array);
    });
});
