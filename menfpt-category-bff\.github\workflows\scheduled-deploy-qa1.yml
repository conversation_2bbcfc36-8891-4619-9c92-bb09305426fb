name: scheduled-deploy-qa1
on:
  schedule:
    - cron: '0 14 * * 1-5'  # 6am PST Monday to Friday (14:00 UTC)

jobs:
  Extract_branch:
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/extractbranch.yml@v2
  call-ci-workflow:
    needs: [Extract_branch]
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-node-ci-nonprod.yml@v4
    with:
      ACTIONSFILE: "Actionsfile/qa1"
      TAG: "${{ needs.Extract_branch.outputs.branch_name }}-boc-${{ github.run_number }}"
      VERACODE_APPNAME: "menfpt-category-bff"
      branch_name: "${{ needs.Extract_branch.outputs.branch_name }}"
      npm_build_command: "npm run build"
      npm_install_command: "npm cache clean --force && npm install"
      npm_sonar_command: "npm ci && npm run build && npm run test"

    secrets:
      SONAR_CONTINUEONERROR_NONPROD: "true"
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      TL_USERNAME: ${{ secrets.TL_USERNAME }}
      TL_PASSWORD: ${{ secrets.TL_PASSWORD }}
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }}
      VERACODEID: ${{ secrets.VERACODEID }}
      VERACODEKEY: ${{ secrets.VERACODEKEY }}
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

  qa1-deploy:
    needs: [Extract_branch, call-ci-workflow]
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/oneclick-deploy-aks-helm.yml@v2
    with:
      ENVIRONMENT: qa1
      ACTIONSFILE: "Actionsfile/qa1"
      TAG: "${{ needs.Extract_branch.outputs.branch_name }}-boc-${{ github.run_number }}"
    secrets:
      REGISTRY_USER: ${{ secrets.ACR_USER }}
      REGISTRY_PWD: ${{ secrets.ACR_PWD }}
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
  Deploy-Status-check-qa1:
    needs: qa1-deploy
    if: success()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Deploy-Status-check.yml@v2
    with:
      ACTIONSFILE: "Actionsfile/qa1"
    secrets:
      KUBECONFIG: ${{ secrets.ESCO_AKSBA2_NONPROD_WESTUS_CLUSTER_01 }}