import { ContextValue } from "../../../context";
import { FileUploadPharmaProvider } from "../providers/fileUploadPharma.provider";

export const FileUploadPharmaResolver = {
    fileUploadToBlob: async (
        parent: any,
        args: { fileUploadToBlob: any },
        context: ContextValue
    ): Promise<any> => {

        const FileUploadPharmaService = context?.injector?.get(
            FileUploadPharmaProvider.provide
        );
        if (!FileUploadPharmaService) {
            throw new Error(
                "FileUploadPharmaService is not available in the context injector."
            );
        }
        const req = args.fileUploadToBlob || {};
        try {
            const response = await FileUploadPharmaService.fileUploadToBlob(req);
            return response;
        } catch (error) {
            throw error;
        }
    }
};
