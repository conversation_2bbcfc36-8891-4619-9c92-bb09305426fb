import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";

import { createModule } from "graphql-modules";
import { TimeRangeInYearProvider } from "./providers/TimeRangeInYear.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));
const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const TimeRangeYearModule = createModule({
    id: "QuartersInYear",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [TimeRangeInYearProvider]
});

export default TimeRangeYearModule;
