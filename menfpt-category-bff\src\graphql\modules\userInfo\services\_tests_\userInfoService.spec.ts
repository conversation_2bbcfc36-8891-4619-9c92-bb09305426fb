import "reflect-metadata";
import { UserInfoService } from "../userInfo.service";
import { GraphQLError } from "graphql";
import { MSP_DATA_GET_FAILURE } from "../../../../shared/constants/errorCodes";
import jwt, { SignOptions } from "jsonwebtoken";

jest.mock("axios");
jest.mock("../../../../shared/classes/BaseAPI.service");

const secretKey = "your-secret-key"; // Replace with your actual secret key

function generateMockToken(
    payload: object,
    expiresIn: string | number = "1h"
): string {
    const options: SignOptions = {
        expiresIn: expiresIn as any
    };
    return jwt.sign(payload, secretKey, options);
}

describe("UserInfoService", () => {
    let service: UserInfoService;
    beforeEach(() => {
        service = new UserInfoService();
    });

    describe("To get user info details", () => {
        it("should return user info details divisionmanager", async () => {
            const mockPayload = {
                name: "test-user",
                preferred_username: "<EMAIL>",
                upn: "<EMAIL>",
                groups: ["az-menfpt-nonprod-divisionmanager"]
            };

            const mockToken = generateMockToken(mockPayload);

            const userInfo: any = await service.fetchUserInfoDetails(
                `${"Bearer" + " " + mockToken}`
            );
            expect(userInfo).toEqual({
                userEmail: "<EMAIL>",
                userName: "test-user",
                userRole: "DivisionalManager",
                userId: "test-user",
                userPermissions: {
                    canView: true,
                    canEdit: true
                }
            });
        });

        it("should return user info details nationalmanager", async () => {
            const mockPayload = {
                name: "test-user",
                preferred_username: "<EMAIL>",
                upn: "<EMAIL>",
                groups: ["az-memsp-nonprod-ncd"]
            };

            const mockToken = generateMockToken(mockPayload);

            const userInfo: any = await service.fetchUserInfoDetails(
                `${"Bearer" + " " + mockToken}`
            );
            expect(userInfo).toEqual({
                userEmail: "<EMAIL>",
                userName: "test-user",
                userRole: "NationalManager",
                userId: "test-user",
                userPermissions: {
                    canView: true,
                    canEdit: true
                }
            });
        });

        it("should return user info details fpa", async () => {
            const mockPayload = {
                name: "test-user",
                preferred_username: "<EMAIL>",
                upn: "<EMAIL>",
                groups: ["az-menfpt-nonprod-fpa"]
            };

            const mockToken = generateMockToken(mockPayload);

            const userInfo: any = await service.fetchUserInfoDetails(
                `${"Bearer" + " " + mockToken}`
            );
            expect(userInfo).toEqual({
                userEmail: "<EMAIL>",
                userName: "test-user",
                userRole: "FPA",
                userId: "test-user",
                userPermissions: {
                    canView: true,
                    canEdit: false
                }
            });
        });

        it("should return empty object for invalid token", async () => {
            const userInfo: any = await service.fetchUserInfoDetails("");
            expect(userInfo).toEqual({});
        });

        it("should throw an error with the correct extensions when get request fails", async () => {
            const error = new Error("Error while fetching user info data");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "SOME_ERROR_CODE",
                name: "SOME_NAME",
                message: "Could not fetch data",
                url: ""
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(
                mockErrorPayload
            );

            (
                service.get as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            try {
                await service.fetchUserInfoDetails("token");
            } catch (err) {
                const error = err as GraphQLError;
                expect(err).toBeInstanceOf(GraphQLError);
                expect(error.extensions).toEqual({
                    errorType: MSP_DATA_GET_FAILURE,
                    ...mockErrorPayload
                });
            }
        });

        it("should return default entitlement data when request fails", async () => {
            const error = new Error("Error while fetching entitlements data");
            const fetchNewEntitlementDetailsMock = jest
                .spyOn(service, "fetchUserInfoDetails")
                .mockRejectedValueOnce(error);
            const token = "your-token-here"; // replace with a real token or a mock token

            try {
                await service.fetchUserInfoDetails(token);
            } catch (e) {
                // handle error
            }

            expect(fetchNewEntitlementDetailsMock).toHaveBeenCalledTimes(1);
        });

        it("should get user id from upn", async () => {
            const mockPayload = {
                name: "test-user",
                preferred_username: "<EMAIL>",
                upn: "<EMAIL>",
                groups: ["az-menfpt-nonprod-fpa"]
            };

            const mockToken = generateMockToken(mockPayload);

            const userInfo: any = await service.fetchUserInfoDetails(
                `${"Bearer" + " " + mockToken}`
            );
            expect(userInfo).toEqual({
                userEmail: "<EMAIL>",
                userName: "test-user",
                userRole: "FPA",
                userId: "test-user",
                userPermissions: {
                    canView: true,
                    canEdit: false
                }
            });
        });
    });
});
