import { PharmaDownloadProvider } from './pharmaDownload.provider';
import { PharmaDownloadService } from '../services/pharmaDownload.service';

// Mock the PharmaDownloadService
jest.mock('../services/pharmaDownload.service');

describe('PharmaDownloadProvider', () => {
    let provider: PharmaDownloadProvider;
    let mockPharmaDownloadService: jest.Mocked<PharmaDownloadService>;

    beforeEach(() => {
        // Clear all mocks
        jest.clearAllMocks();

        // Create mocked service instance
        mockPharmaDownloadService = new PharmaDownloadService() as jest.Mocked<PharmaDownloadService>;

        // Create provider instance with mocked service
        provider = new PharmaDownloadProvider(mockPharmaDownloadService);
    });

    describe('listPharmacyFiles', () => {
        it('should return success response with formatted files', async () => {
            const mockFiles = [
                {
                    fileName: 'pharmacy_file_1.xlsx',
                    fullPath: 'pharmacy/pharmacy_file_1.xlsx',
                    size: 1024,
                    lastModified: new Date('2025-08-05T10:00:00Z')
                },
                {
                    fileName: 'pharmacy_file_2.xlsx',
                    fullPath: 'pharmacy/pharmacy_file_2.xlsx',
                    size: 2048,
                    lastModified: new Date('2025-08-05T11:00:00Z')
                }
            ];

            mockPharmaDownloadService.listPharmacyFiles.mockResolvedValue(mockFiles);

            const result = await provider.listPharmacyFiles();

            expect(result).toEqual({
                success: true,
                files: [
                    {
                        fileName: 'pharmacy_file_1.xlsx',
                        fullPath: 'pharmacy/pharmacy_file_1.xlsx',
                        size: 1024,
                        lastModified: '2025-08-05T10:00:00.000Z'
                    },
                    {
                        fileName: 'pharmacy_file_2.xlsx',
                        fullPath: 'pharmacy/pharmacy_file_2.xlsx',
                        size: 2048,
                        lastModified: '2025-08-05T11:00:00.000Z'
                    }
                ],
                error: null
            });

            expect(mockPharmaDownloadService.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should return empty files array when no files found', async () => {
            mockPharmaDownloadService.listPharmacyFiles.mockResolvedValue([]);

            const result = await provider.listPharmacyFiles();

            expect(result).toEqual({
                success: true,
                files: [],
                error: null
            });

            expect(mockPharmaDownloadService.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should return error response when service throws Error instance', async () => {
            const errorMessage = 'Azure Blob Storage connection failed';
            const error = new Error(errorMessage);
            mockPharmaDownloadService.listPharmacyFiles.mockRejectedValue(error);

            const result = await provider.listPharmacyFiles();

            expect(result).toEqual({
                success: false,
                files: [],
                error: errorMessage
            });

            expect(mockPharmaDownloadService.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should return generic error message when service throws non-Error', async () => {
            mockPharmaDownloadService.listPharmacyFiles.mockRejectedValue('String error');

            const result = await provider.listPharmacyFiles();

            expect(result).toEqual({
                success: false,
                files: [],
                error: 'Unknown error occurred'
            });

            expect(mockPharmaDownloadService.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should return generic error message when service throws null', async () => {
            mockPharmaDownloadService.listPharmacyFiles.mockRejectedValue(null);

            const result = await provider.listPharmacyFiles();

            expect(result).toEqual({
                success: false,
                files: [],
                error: 'Unknown error occurred'
            });

            expect(mockPharmaDownloadService.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should return generic error message when service throws undefined', async () => {
            mockPharmaDownloadService.listPharmacyFiles.mockRejectedValue(undefined);

            const result = await provider.listPharmacyFiles();

            expect(result).toEqual({
                success: false,
                files: [],
                error: 'Unknown error occurred'
            });

            expect(mockPharmaDownloadService.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });
    });

    describe('generatePharmacyFileDownloadUrl', () => {
        const fileName = 'test_pharmacy_file.xlsx';

        it('should return success response with SAS URL and expiry', async () => {
            const mockUrl = 'https://storage.blob.core.windows.net/container/pharmacy/test_pharmacy_file.xlsx?sv=...';
            const expiryMinutes = 60;
            mockPharmaDownloadService.generateDownloadUrl.mockResolvedValue(mockUrl);

            const result = await provider.generatePharmacyFileDownloadUrl(fileName, expiryMinutes);

            expect(result.success).toBe(true);
            expect(result.downloadUrl).toBe(mockUrl);
            expect(result.isBFFProxied).toBe(false);
            expect(result.expiresAt).toBeDefined();
            expect(result.error).toBeNull();

            // Verify expiry time is approximately correct (within 1 minute tolerance)
            const expectedExpiry = new Date();
            expectedExpiry.setMinutes(expectedExpiry.getMinutes() + expiryMinutes);
            const actualExpiry = new Date(result.expiresAt!);
            const timeDiff = Math.abs(actualExpiry.getTime() - expectedExpiry.getTime());
            expect(timeDiff).toBeLessThan(60000); // Less than 1 minute difference

            expect(mockPharmaDownloadService.generateDownloadUrl).toHaveBeenCalledWith(fileName, expiryMinutes);
        });

        it('should return success response with BFF-proxied URL without expiry', async () => {
            const mockUrl = '/menfpt-category-bff/api/pharmacy/download/test_pharmacy_file.xlsx';
            mockPharmaDownloadService.generateDownloadUrl.mockResolvedValue(mockUrl);

            const result = await provider.generatePharmacyFileDownloadUrl(fileName);

            expect(result).toEqual({
                success: true,
                downloadUrl: mockUrl,
                expiresAt: null,
                isBFFProxied: true,
                error: null
            });

            expect(mockPharmaDownloadService.generateDownloadUrl).toHaveBeenCalledWith(fileName, 60);
        });

        it('should use default expiry of 60 minutes when not specified', async () => {
            const mockUrl = 'https://storage.blob.core.windows.net/container/pharmacy/test.xlsx?sv=...';
            mockPharmaDownloadService.generateDownloadUrl.mockResolvedValue(mockUrl);

            await provider.generatePharmacyFileDownloadUrl(fileName);

            expect(mockPharmaDownloadService.generateDownloadUrl).toHaveBeenCalledWith(fileName, 60);
        });

        it('should handle custom expiry minutes', async () => {
            const customExpiry = 120;
            const mockUrl = 'https://storage.blob.core.windows.net/container/pharmacy/test.xlsx?sv=...';
            mockPharmaDownloadService.generateDownloadUrl.mockResolvedValue(mockUrl);

            const result = await provider.generatePharmacyFileDownloadUrl(fileName, customExpiry);

            expect(result.success).toBe(true);
            expect(result.isBFFProxied).toBe(false);
            expect(result.expiresAt).toBeDefined();

            // Verify custom expiry time
            const expectedExpiry = new Date();
            expectedExpiry.setMinutes(expectedExpiry.getMinutes() + customExpiry);
            const actualExpiry = new Date(result.expiresAt!);
            const timeDiff = Math.abs(actualExpiry.getTime() - expectedExpiry.getTime());
            expect(timeDiff).toBeLessThan(60000);

            expect(mockPharmaDownloadService.generateDownloadUrl).toHaveBeenCalledWith(fileName, customExpiry);
        });

        it('should return error response when service throws Error instance', async () => {
            const errorMessage = 'File not found in pharmacy folder';
            const error = new Error(errorMessage);
            mockPharmaDownloadService.generateDownloadUrl.mockRejectedValue(error);

            const result = await provider.generatePharmacyFileDownloadUrl(fileName);

            expect(result).toEqual({
                success: false,
                downloadUrl: null,
                expiresAt: null,
                isBFFProxied: false,
                error: errorMessage
            });

            expect(mockPharmaDownloadService.generateDownloadUrl).toHaveBeenCalledWith(fileName, 60);
        });

        it('should return generic error message when service throws non-Error', async () => {
            mockPharmaDownloadService.generateDownloadUrl.mockRejectedValue('Network timeout');

            const result = await provider.generatePharmacyFileDownloadUrl(fileName);

            expect(result).toEqual({
                success: false,
                downloadUrl: null,
                expiresAt: null,
                isBFFProxied: false,
                error: 'Unknown error occurred'
            });
        });

        it('should handle empty URL from service', async () => {
            mockPharmaDownloadService.generateDownloadUrl.mockResolvedValue('');

            const result = await provider.generatePharmacyFileDownloadUrl(fileName);

            expect(result.success).toBe(true);
            expect(result.downloadUrl).toBe('');
            expect(result.isBFFProxied).toBe(false);
            // Empty string is falsy, so it's not BFF-proxied and expiry is calculated
            expect(result.expiresAt).toBeDefined();
            expect(result.error).toBeNull();
        });

        it('should detect BFF-proxied URL with different path variations', async () => {
            const bffUrls = [
                '/menfpt-category-bff/api/pharmacy/download/file.xlsx',
                'https://server.com/menfpt-category-bff/api/pharmacy/download/file.xlsx',
                '/app/menfpt-category-bff/api/pharmacy/download/file.xlsx'
            ];

            for (const url of bffUrls) {
                mockPharmaDownloadService.generateDownloadUrl.mockResolvedValue(url);

                const result = await provider.generatePharmacyFileDownloadUrl(fileName);

                expect(result.isBFFProxied).toBe(true);
                expect(result.expiresAt).toBeNull();
            }
        });

        it('should handle edge case of zero expiry minutes', async () => {
            const mockUrl = 'https://storage.blob.core.windows.net/container/pharmacy/test.xlsx?sv=...';
            mockPharmaDownloadService.generateDownloadUrl.mockResolvedValue(mockUrl);

            const result = await provider.generatePharmacyFileDownloadUrl(fileName, 0);

            expect(result.success).toBe(true);
            expect(result.isBFFProxied).toBe(false);

            // With 0 expiry, the expiry time should be approximately now
            const actualExpiry = new Date(result.expiresAt!);
            const now = new Date();
            const timeDiff = Math.abs(actualExpiry.getTime() - now.getTime());
            expect(timeDiff).toBeLessThan(60000); // Less than 1 minute difference

            expect(mockPharmaDownloadService.generateDownloadUrl).toHaveBeenCalledWith(fileName, 0);
        });

        it('should handle negative expiry minutes', async () => {
            const mockUrl = 'https://storage.blob.core.windows.net/container/pharmacy/test.xlsx?sv=...';
            mockPharmaDownloadService.generateDownloadUrl.mockResolvedValue(mockUrl);

            const result = await provider.generatePharmacyFileDownloadUrl(fileName, -30);

            expect(result.success).toBe(true);
            expect(result.isBFFProxied).toBe(false);

            // With negative expiry, the expiry time should be in the past
            const actualExpiry = new Date(result.expiresAt!);
            const expectedExpiry = new Date();
            expectedExpiry.setMinutes(expectedExpiry.getMinutes() - 30);
            const timeDiff = Math.abs(actualExpiry.getTime() - expectedExpiry.getTime());
            expect(timeDiff).toBeLessThan(60000);

            expect(mockPharmaDownloadService.generateDownloadUrl).toHaveBeenCalledWith(fileName, -30);
        });
    });
});
