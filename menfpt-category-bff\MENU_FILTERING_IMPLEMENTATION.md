# Menu Filtering Implementation for Pharmacy Users

## Overview

This implementation adds role-based menu filtering to the Category Excellence menu items based on the user's AD groups. Specifically, users belonging to either the `az-menfpt-prod-rxfinance`, `az-menfpt-nonprod-rxfinance`, or `az-menfpt-stage-rxfinance` AD groups will see only the top-level "Rx Forecast" subApp, while all other users will see the top-level "Allocatr Insights" subApp with "Dashboard" and "Adjustment Worksheet" submenu items.

## Implementation Details

### 1. Utility Functions

**File**: `src/util/menuFilterUtils.ts`

#### Functions:

-   **`isPharmacyUser(token: string): boolean`**

    -   Parses JWT token to extract AD groups
    -   Checks if user belongs to `az-menfpt-prod-rxfinance` or `az-menfpt-nonprod-rxfinance` group
    -   Returns `true` if pharmacy user, `false` otherwise

-   **`filterMenuItemsByUserRole(menuItemsData: any, isPharmacy: boolean)`**

    -   Filters menu items based on user role
    -   For pharmacy users: shows only top-level "Rx Forecast" subApp and hides "Allocatr Insights"
    -   For non-pharmacy users: shows only top-level "Allocatr Insights" subApp (its submenu contains "Dashboard" and "Adjustment Worksheet") and hides the top-level "Rx Forecast" subApp

-   **`extractJwtToken(headers: any): string | undefined`**
    -   Extracts JWT token from request headers
    -   Handles both `authorization` and `Authorization` header names
    -   Handles both string and array header values

### 2. MenuItemService Changes

**File**: `src/services/MenuItemService.ts`

#### Modified Function:

-   **`getMenuItems(authToken?: string)`**
    -   Now accepts optional JWT token parameter
    -   Uses utility functions from `menuFilterUtils.ts`
    -   Applies filtering logic before returning menu items
    -   Maintains backward compatibility when no token is provided

### 3. MenuItemController Changes

**File**: `src/controllers/MenuItemController.ts`

#### Modified Function:

-   **`getMenuItems(req: Request, res: Response)`**
    -   Uses `extractJwtToken` utility function to extract JWT token from request headers
    -   Passes token to MenuItemService for filtering

### 4. Menu Filtering Logic

#### For Pharmacy Users (`az-menfpt-prod-rxfinance`, `az-menfpt-nonprod-rxfinance`, or `az-menfpt-stage-rxfinance`):

-   ✅ **Rx Forecast (top-level subApp)** (`nfpt_rx_forecast`)
-   ❌ **Allocatr Insights (top-level subApp)** (`national_forecast_projection_tool`) - Hidden

#### For Non-Pharmacy Users:

-   ✅ **Allocatr Insights (top-level subApp)** (`national_forecast_projection_tool`)
    -   ✅ **Dashboard** (`nfpt_dashboard`)
    -   ✅ **Adjustment Worksheet** (`nfpt_adjustment_worksheet`)
-   ❌ **Rx Forecast (top-level subApp)** (`nfpt_rx_forecast`) - Hidden

## API Usage

### Request Format

```http
GET /menfpt-category-bff/api/menu-items
Authorization: Bearer <jwt_token>
```

### Response Examples

#### Non-Pharmacy User Response:

```json
{
    "menuItems": [
        {
            "contextId": "category_excellence",
            "contextLabel": "Category Excellence",
            "subApp": [
                {
                    "seqNum": 1,
                    "id": "national_forecast_projection_tool",
                    "label": "Allocatr Insights",
                    "uri": null,
                    "subMenu": [
                        {
                            "seqNum": 1,
                            "id": "nfpt_dashboard",
                            "label": "Dashboard",
                            "uri": "/menfpt/dashboard"
                        },
                        {
                            "seqNum": 2,
                            "id": "nfpt_adjustment_worksheet",
                            "label": "Adjustment Worksheet",
                            "uri": "/menfpt/adjustment-worksheet"
                        }
                    ]
                }
            ]
        }
    ]
}
```

#### Pharmacy User Response:

```json
{
    "menuItems": [
        {
            "contextId": "category_excellence",
            "contextLabel": "Category Excellence",
            "subApp": [
                {
                    "seqNum": 2,
                    "id": "nfpt_rx_forecast",
                    "label": "Rx Forecast",
                    "uri": "/menfpt/rx-forecast"
                }
            ]
        }
    ]
}
```

## Error Handling

-   **Invalid JWT Token**: Falls back to non-pharmacy behavior (shows Dashboard and Adjustment Worksheet)
-   **Missing JWT Token**: Falls back to non-pharmacy behavior
-   **JWT Parsing Errors**: Logs error and falls back to non-pharmacy behavior

## Testing

### Unit Tests

-   **menuFilterUtils.spec.ts**: Tests utility functions for JWT parsing and menu filtering
-   **MenuItemService.spec.ts**: Tests menu filtering logic with various JWT tokens
-   **MenuItemController.spec.ts**: Tests JWT token extraction from request headers
-   **MenuItemRouter.spec.ts**: Integration tests for the complete flow

### Test Scenarios

1. **No JWT Token**: Should show Dashboard and Adjustment Worksheet
2. **Pharmacy JWT Token**: Should show only Rx Forecast
3. **Non-Pharmacy JWT Token**: Should show Dashboard and Adjustment Worksheet
4. **Invalid JWT Token**: Should fall back to non-pharmacy behavior

## Code Organization

### File Structure

```
src/
├── util/
│   ├── menuFilterUtils.ts          # Utility functions for JWT parsing and menu filtering
│   └── menuFilterUtils.spec.ts     # Tests for utility functions
├── services/
│   ├── MenuItemService.ts          # Service using utility functions
│   └── MenuItemService.spec.ts     # Service tests
├── controllers/
│   ├── MenuItemController.ts       # Controller using utility functions
│   └── MenuItemController.spec.ts  # Controller tests
└── routes/
    └── MenuItemRouter.spec.ts      # Integration tests
```

### Benefits of Utility File Structure

-   **Separation of Concerns**: JWT parsing and menu filtering logic is separated from business logic
-   **Reusability**: Utility functions can be used by other parts of the application
-   **Testability**: Easier to unit test individual utility functions
-   **Maintainability**: Changes to JWT parsing or menu filtering logic are centralized
-   **Clean Code**: Service and controller files are cleaner and more focused

## Security Considerations

-   JWT tokens are parsed using `jwt.decode()` (no signature verification required for this use case)
-   AD groups are extracted from the JWT payload
-   Error handling ensures graceful degradation
-   No sensitive information is logged

## Dependencies

-   `jsonwebtoken`: For JWT token parsing
-   `lodash/get`: For safe object property access

## Backward Compatibility

-   The API maintains backward compatibility
-   Existing clients without JWT tokens will continue to work
-   Default behavior (non-pharmacy) is applied when no token is provided
