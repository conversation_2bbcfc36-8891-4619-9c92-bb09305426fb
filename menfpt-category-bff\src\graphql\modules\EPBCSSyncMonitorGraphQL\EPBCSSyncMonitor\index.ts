import {
    fetchDatabricksJobData,
    fetchDatabricksJobDataWithStatus
} from "./jobRunner";
import {
    DEFAULT_JOB_NAME,
    getAccessToken,
    listAllJobs,
    fetchJobData,
    fetchJobRunsWithFormatting,
    findJobIdByName as getJobIdByName
} from "./connection";
import {
    MONDAY_CRON,
    THURSDAY_CRON,
    FRIDAY_CRON,
    parseCronHours,
    formatCronHourToReadableTime
} from "./connection";

function getSyncDaysForCurrentWeek() {
    return {
        sync_days: [
            {
                sync_date: formatDateString(getNextDayOfWeek(4)),
                sync_day: getDayName(4)
            },
            {
                sync_date: formatDateString(getNextDayOfWeek(5)),
                sync_day: getDayName(5)
            },
            {
                sync_date: formatDateString(getNextDayOfWeek(1)),
                sync_day: getDayName(1)
            }
        ]
    };
}

function formatDateString(date: Date): string {
    return `${(date.getMonth() + 1).toString().padStart(2, "0")}/${date
        .getDate()
        .toString()
        .padStart(2, "0")}/${date.getFullYear()}`;
}

function getNextDayOfWeek(dayOfWeek: number): Date {
    const date = new Date();
    const diff = (dayOfWeek - date.getDay() + 7) % 7;
    date.setDate(date.getDate() + diff);
    return date;
}

function generateSyncSessions(runs: any[], syncDays: any, scheduledTime: any) {
    return [];
}

function calculateNextAndLastSyncTimes(syncSessions: any[]) {
    return {
        nextSync: null,
        lastSync: null
    };
}

export async function getDataFromDatabricks(
    jobIdOrName: number | string = DEFAULT_JOB_NAME ?? ""
): Promise<any> {
    return fetchDatabricksJobData(jobIdOrName);
}

export async function getDataWithStatusFromDatabricks(
    jobIdOrName: number | string = DEFAULT_JOB_NAME ?? ""
): Promise<any> {
    return fetchDatabricksJobDataWithStatus(jobIdOrName);
}

export async function listAllDatabricksJobs(): Promise<any> {
    try {
        const accessToken = await getAccessToken();
        return await listAllJobs(accessToken);
    } catch (error) {
        console.error("Error listing all Databricks jobs:", error);
        throw error;
    }
}

export async function getAllDatabricksJobsData(
    includeStatus: boolean = false
): Promise<any> {
    try {
        const accessToken = await getAccessToken();
        const jobsResponse = await listAllJobs(accessToken);

        if (!jobsResponse.jobs || jobsResponse.jobs.length === 0) {
            throw new Error("No jobs found in the workspace");
        }

        const jobPromises = jobsResponse.jobs.map(async (job: any) => {
            try {
                const jobName = job.settings.name;
                const jobId = job.job_id;
                const data = await fetchJobData(jobId, includeStatus).catch(
                    (error) => {
                        console.error(
                            `Error fetching data for job ${jobName} (${jobId}):`,
                            error
                        );
                        return { error: error.message || "Unknown error" };
                    }
                );

                return {
                    jobName,
                    jobId,
                    data
                };
            } catch (error) {
                console.error(`Error processing job ${job.job_id}:`, error);
                return {
                    jobName: job.settings?.name || "Unknown",
                    jobId: job.job_id,
                    data: { error: "Failed to process job" }
                };
            }
        });

        const results = await Promise.all(jobPromises);

        const jobsData: Record<string, any> = {};
        results.forEach((result) => {
            jobsData[result.jobName] = result.data;
        });

        return jobsData;
    } catch (error) {
        console.error("Error getting data from all Databricks jobs:", error);
        throw error;
    }
}

export function getCronScheduleInfo(format: boolean = true): any {
    if (!format) {
        return {
            monday: MONDAY_CRON,
            thursday: THURSDAY_CRON,
            friday: FRIDAY_CRON
        };
    }

    const mondayHours = parseCronHours(MONDAY_CRON ?? "").map(
        formatCronHourToReadableTime
    );
    const thursdayHours = parseCronHours(THURSDAY_CRON ?? "").map(
        formatCronHourToReadableTime
    );
    const fridayHours = parseCronHours(FRIDAY_CRON ?? "").map(
        formatCronHourToReadableTime
    );

    return {
        monday: mondayHours.join(", "),
        thursday: thursdayHours.join(", "),
        friday: fridayHours.join(", ")
    };
}

export function getScheduledTimeInfo(): any {
    const mondayHours = parseCronHours(MONDAY_CRON ?? "").map(
        formatCronHourToReadableTime
    );
    const thursdayHours = parseCronHours(THURSDAY_CRON ?? "").map(
        formatCronHourToReadableTime
    );
    const fridayHours = parseCronHours(FRIDAY_CRON ?? "").map(
        formatCronHourToReadableTime
    );

    return {
        Monday: mondayHours.join(", "),
        Thursday: thursdayHours.join(", "),
        Friday: fridayHours.join(", ")
    };
}

export const getJobRunsFromDatabricks = async (
    jobIdentifier: number | string,
    limit: number = 10
): Promise<any> => {
    try {
        console.log(
            `Getting job runs for job ${jobIdentifier} with limit ${limit}`
        );
        const accessToken = await getAccessToken();
        if (!accessToken) {
            throw new Error("Failed to get access token");
        }

        let jobId = typeof jobIdentifier === "number" ? jobIdentifier : null;
        if (!jobId && typeof jobIdentifier === "string") {
            jobId = await getJobIdByName(accessToken, jobIdentifier);
        }

        if (!jobId) {
            throw new Error(
                `Could not find job with identifier: ${jobIdentifier}`
            );
        }

        const jobRuns = await fetchJobRunsWithFormatting(
            accessToken,
            jobId,
            limit
        );

        const scheduledTime = getScheduledTimeInfo();

        const syncDays = getSyncDaysForCurrentWeek();

        const syncSessions = generateSyncSessions(
            jobRuns.runs,
            syncDays,
            scheduledTime
        );

        const { nextSync, lastSync } =
            calculateNextAndLastSyncTimes(syncSessions);

        return {
            ...jobRuns,
            scheduled_time: scheduledTime,
            syncDays,
            syncSessions,
            nextSync,
            lastSync,
            syncHistory: {
                weeks: getCurrentWeeks()
            }
        };
    } catch (error) {
        console.error("Error in getJobRunsFromDatabricks:", error);
        throw error;
    }
};

function getCurrentWeeks() {
    const today = new Date();
    const weeks = [];

    for (let i = 0; i < 3; i++) { // Changed from 4 to 3
        const weekEnd = new Date(today);
        weekEnd.setDate(today.getDate() - (today.getDay() + 1) + (2 - i) * 7); // Changed from 3-i to 2-i

        const weekStart = new Date(weekEnd);
        weekStart.setDate(weekEnd.getDate() - 6);

        const weekStartDate = `${(weekStart.getMonth() + 1)
            .toString()
            .padStart(2, "0")}/${weekStart
            .getDate()
            .toString()
            .padStart(2, "0")}/${weekStart.getFullYear()}`;
        const weekEndDate = `${(weekEnd.getMonth() + 1)
            .toString()
            .padStart(2, "0")}/${weekEnd
            .getDate()
            .toString()
            .padStart(2, "0")}/${weekEnd.getFullYear()}`;

        weeks.push({
            weekNumber: i + 1,
            weekStartDate,
            weekEndDate,
            lastRun: null
        });
    }

    return weeks;
}

function getDayName(dayNumber: number): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayNumber % 7];
}

export {
    fetchDatabricksJobData,
    fetchDatabricksJobDataWithStatus,
    DEFAULT_JOB_NAME,
    getAccessToken,
    MONDAY_CRON,
    THURSDAY_CRON,
    FRIDAY_CRON,
    parseCronHours,
    formatCronHourToReadableTime
};
