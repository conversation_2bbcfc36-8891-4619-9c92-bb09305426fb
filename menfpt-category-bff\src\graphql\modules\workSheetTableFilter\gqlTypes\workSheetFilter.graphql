input WorkSheetFilterReq {
    userId: String
}
type WorkSheetFilterRes {
    smicData: [smicDataDetails]
    userId: String
    userName: String
    userEmail: String
    userRole: String
    createdTimeStamp: String
    updatedTimeStamp: String
    createdBy: String
    updatedBy: String
}

type smicDataDetails {
    divisionId: String
    divisionName: String
    deptId: String
    deptName: String
    smicGroupCd: Int
    smicGroupDesc: String
    smicCategoryCd: Int
    smicCategoryDesc: String
    smicCategoryId: Int
    retailSectionCd: String
    retailSectionName: String
    deskId: String
    deskName: String
}
type Query {
    getWorkSheetFilter(
        workSheetFilterRequest: WorkSheetFilterReq
    ): WorkSheetFilterRes
}
