import { ContextValue } from "../../../context";
import { EnvVariablesService } from "../providers/envVariables.provider";

export const EnvVariablesResolver = async (
    parent: any,
    args: any,
    context: ContextValue
): Promise<any> => {
    try {
        const envVariablesService = context?.injector?.get(
            EnvVariablesService
        );

        const data = await envVariablesService?.getEnvVariables();
        return data || { variables: {} };
    } catch (error) {
        // Return empty variables object on error
        return { variables: {} };
    }
}; 