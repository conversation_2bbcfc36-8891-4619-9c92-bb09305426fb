/**
 * Parses date and time strings in MM/DD/YYYY and HH:MM AM/PM format into a Date object.
 */
export function parseDateTime(dateStr: string, timeStr: string): Date | null {
    if (!dateStr || !timeStr || dateStr === '-') return null;
    try {
        const [month, day, year] = dateStr.split('/').map(Number);
        if (!month || !day || !year) return null;
        const timeMatch = timeStr.match(/^(\d{1,2}):(\d{2})\s(AM|PM)$/i);
        if (!timeMatch) return null;
        let hours = parseInt(timeMatch[1], 10);
        const minutes = parseInt(timeMatch[2], 10);
        const ampm = timeMatch[3].toUpperCase();
        if (ampm === 'PM' && hours < 12) hours += 12;
        else if (ampm === 'AM' && hours === 12) hours = 0;
        return new Date(year, month - 1, day, hours, minutes);
    } catch {
        return null;
    }
}

/**
 * Returns an array of week objects for a given fiscal period.
 */
export function getWeeksForFiscalPeriod(fiscalPeriodStartDate: string, fiscalPeriodEndDate: string, formatDateString: (date: Date) => string): Array<{ weekNumber: number, weekStartDate: string, weekEndDate: string, lastRun: any }> {
    if (!fiscalPeriodStartDate || !fiscalPeriodEndDate ||
        fiscalPeriodStartDate === '-' || fiscalPeriodEndDate === '-') {
        return [];
    }

    try {
        const [startMonth, startDay, startYear] = fiscalPeriodStartDate.split('/').map(Number);
        const [endMonth, endDay, endYear] = fiscalPeriodEndDate.split('/').map(Number);

        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, endMonth - 1, endDay);

        const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        const numWeeks = Math.ceil(daysDiff / 7);

        const weeks = [];
        for (let i = 0; i < numWeeks; i++) {
            const weekStartDate = new Date(startDate);
            weekStartDate.setDate(startDate.getDate() + (i * 7));

            const weekEndDate = new Date(weekStartDate);
            weekEndDate.setDate(weekStartDate.getDate() + 6);

            if (weekEndDate > endDate) {
                weekEndDate.setTime(endDate.getTime());
            }

            weeks.push({
                weekNumber: i + 1,
                weekStartDate: formatDateString(weekStartDate),
                weekEndDate: formatDateString(weekEndDate),
                lastRun: null
            });
        }

        return weeks;
    } catch (error) {
        console.error("Error calculating weeks for fiscal period:", error);
        return [];
    }
}

/**
 * Returns the week number of a given date in its year.
 */
export function getWeekNumberOfDate(date: Date): number {
    const firstJan = new Date(date.getFullYear(), 0, 1);
    const days = Math.floor((date.getTime() - firstJan.getTime()) / (24 * 60 * 60 * 1000));
    return Math.ceil((days + firstJan.getDay() + 1) / 7);
}