import { EnvVariablesResolver } from "../envVariables.resolver";
import { EnvVariablesService } from "../../providers/envVariables.provider";

// Mock the service
jest.mock("../../providers/envVariables.provider");

describe("EnvVariablesResolver", () => {
    let mockService: jest.Mocked<EnvVariablesService>;
    let mockContext: any;

    beforeEach(() => {
        mockService = {
            getEnvVariables: jest.fn(),
        } as any;

        mockContext = {
            injector: {
                get: jest.fn().mockReturnValue(mockService),
            },
        };
    });

    it("should return environment variables from service", async () => {
        const mockData = {
            variables: {
                PHARMA_UPLOAD_DAYS: "THURSDAY,FRIDAY",
                NODE_ENV: "development",
                PORT: 4001
            },
        };

        mockService.getEnvVariables.mockResolvedValue(mockData);

        const result = await EnvVariablesResolver(null, {}, mockContext);

        expect(mockContext.injector.get).toHaveBeenCalledWith(
            EnvVariablesService
        );
        expect(mockService.getEnvVariables).toHaveBeenCalled();
        expect(result).toEqual(mockData);
    });

    it("should return empty variables object when service returns null", async () => {
        mockService.getEnvVariables.mockResolvedValue(null as any);

        const result = await EnvVariablesResolver(null, {}, mockContext);

        expect(result).toEqual({ variables: {} });
    });

    it("should return empty variables object when service throws error", async () => {
        mockService.getEnvVariables.mockRejectedValue(new Error("Test error"));

        const result = await EnvVariablesResolver(null, {}, mockContext);

        expect(result).toEqual({ variables: {} });
    });
}); 