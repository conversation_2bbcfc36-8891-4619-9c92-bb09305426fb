import jwt from "jsonwebtoken";
import get from "lodash/get";

/**
 * Utility function to check if user belongs to pharmacy group
 * @param token - JWT token string
 * @returns boolean - true if user belongs to the group specified by PHARMACY_AD_GROUP
 */
export const isPharmacyUser = (token: string): boolean => {
    try {
        if (!token) {
            return false;
        }

        const tokenParts = token.split(" ");
        const actualToken =
            tokenParts.length > 1 ? tokenParts[1] : tokenParts[0];

        const pharmacyGroup = process.env.PHARMACY_AD_GROUP;
        if (!pharmacyGroup) {
            return false;
        }

        // Try to decode the JWT token
        const decoded = jwt.decode(actualToken);

        let groups: string[] | undefined = (get(decoded, "groups") ||
            []) as string[];

        if (!decoded || !groups || groups.length === 0) {
            // If JWT decode fails or no groups present, try to manually parse the base64 payload for test tokens
            try {
                const parts = actualToken.split(".");
                if (parts.length === 3) {
                    const payload = parts[1];
                    const decodedPayload = JSON.parse(
                        Buffer.from(payload, "base64").toString()
                    );
                    groups = (get(decodedPayload, "groups") || []) as string[];
                }
            } catch (manualDecodeError) {
                return false;
            }
        }

        if (!groups || groups.length === 0) {
            return false;
        }

        return groups.some((group) => group === pharmacyGroup);
    } catch (error) {
        console.error("Error parsing JWT token:", error);
        return false;
    }
};

/**
 * Function to filter menu items based on user's AD groups
 * @param menuItemsData - Original menu items data
 * @param isPharmacy - Whether the user is a pharmacy user
 * @returns Filtered menu items data
 */
export const filterMenuItemsByUserRole = (
    menuItemsData: any,
    isPharmacy: boolean
) => {
    const filteredMenuItems = JSON.parse(JSON.stringify(menuItemsData));

    // Find the Category Excellence menu item
    const categoryExcellence = filteredMenuItems.menuItems.find(
        (item: any) => item.contextId === "category_excellence"
    );

    if (categoryExcellence && Array.isArray(categoryExcellence.subApp)) {
        const subApps = categoryExcellence.subApp;

        // New top-level Rx Forecast subApp id matches old menu id for continuity
        const rxForecastSubApp = subApps.find(
            (subApp: any) => subApp.id === "nfpt_rx_forecast"
        );

        if (isPharmacy) {
            // Pharmacy users: show only Rx Forecast at top level, hide Allocatr Insights
            categoryExcellence.subApp = [
                ...(rxForecastSubApp ? [rxForecastSubApp] : [])
            ];
        } else {
            // Non-pharmacy users: show only Allocatr Insights; ensure its submenu excludes Rx Forecast if present
            categoryExcellence.subApp = subApps.filter(
                (subApp: any) => subApp.id !== "nfpt_rx_forecast"
            );
        }
    }

    return filteredMenuItems;
};

/**
 * Extract JWT token from request headers
 * @param headers - Request headers object
 * @returns JWT token string or undefined
 */
export const extractJwtToken = (headers: any): string | undefined => {
    const authToken = headers.authorization ?? headers.Authorization;
    return Array.isArray(authToken) ? authToken[0] : authToken;
};
