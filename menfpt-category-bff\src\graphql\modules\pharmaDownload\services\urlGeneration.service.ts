/**
 * Service for generating download URLs (SAS or BFF-proxied)
 */

import { isConnectionAvailable } from '../../pharmaUpload/Connection';
import { getBlobClient, checkBlobExists } from '../utils/blobUtils';
import { generateBffDownloadUrl, createExpiryDate } from '../utils/urlUtils';

export class UrlGenerationService {
    /**
     * Generates a temporary download URL for a pharmacy file (SAS URL)
     * Falls back to BFF-proxied URL when SAS generation fails
     */
    async generateDownloadUrl(fileName: string, expiryMinutes: number = 60): Promise<string> {
        if (!isConnectionAvailable()) {
            throw new Error('Azure Blob Storage connection not available');
        }

        // Verify the file exists
        const fileExists = await checkBlobExists(fileName);
        if (!fileExists) {
            throw new Error(`File '${fileName}' not found in pharmacy folder`);
        }

        try {
            const blobClient = getBlobClient(fileName);

            // Try to generate SAS URL (this will fail with Azure AD credentials)
            try {
                const expiresOn = createExpiryDate(expiryMinutes);

                const sasUrl = await blobClient.generateSasUrl({
                    permissions: {
                        read: true
                    },
                    expiresOn: expiresOn
                });

                return sasUrl;
            } catch (sasError) {
                // If SAS generation fails (likely due to Azure AD auth), return BFF download URL
                console.warn(`SAS URL generation failed for '${fileName}', returning BFF download URL instead:`, sasError);
                return generateBffDownloadUrl(fileName);
            }
        } catch (error) {
            console.error(`Error generating download URL for '${fileName}':`, error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            throw new Error(`Failed to generate download URL: ${errorMessage}`);
        }
    }
}
