import "reflect-metadata";
import { testkit, gql, MockedApplication } from "graphql-modules";
import { gqlApp } from "../modules/index";
import { CalendarService } from "../modules/services/calendar.service";
import { RoleMappingService } from "../modules/services/roleMapping.service";
import TimeRangeInYearModule from "../modules/TimeRangeInYear";
import WorkSheetFilterModule from "../modules/workSheetTableFilter";
import { TimeRangeInYearProvider } from "../modules/TimeRangeInYear/providers/TimeRangeInYear.provider";
import { WorkSheetFilterProvider } from "../modules/workSheetTableFilter/providers/workSheetFilter.provider";
import { level } from "winston";

const getQuartersInYearResponse = [
    {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 202401,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        createTimestamp: "2025-02-18T08:43:04.543Z",
        updateTimestamp: "2025-02-18T08:43:04.543Z"
    },
    {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 202402,
        fiscalQuarterStartDate: "06/16/2024",
        fiscalQuarterEndDate: "09/14/2024",
        createTimestamp: "2025-02-18T08:43:04.543Z",
        updateTimestamp: "2025-02-18T08:43:04.543Z"
    }
];

// Raw data format that would come from the API
const rawWorkSheetFilterResponse = {
    smicData: [
        {
            division_id: "27",
            division_nm: "Seattle",
            dept_id: "311",
            dept_nm: "GM/HBC",
            smic_group_cd: 52,
            smic_group_desc: "HAIR CARE",
            smic_category_cd: 55,
            smic_category_desc: "HAIR CARE MULTI-CULTURAL",
            smic_category_id: 5255,
            retail_section_cd: "312",
            retail_section_nm: "FAMILY CARE GMHBC",
            desk_id: "27-25",
            desk_nm: "27-BONNIE KWOK"
        }
    ],
    user_id: "user123",
    user_nm: "Test User",
    user_email: "<EMAIL>",
    user_role: "ADMIN",
    created_ts: "2025-02-18T08:43:04.543Z",
    update_ts: "2025-02-18T08:43:04.543Z",
    created_by: "system",
    updated_by: "system"
};

// Expected formatted response after transformation
const getWorkSheetFilterResponse = {
    smicData: [
        {
            divisionId: "27",
            divisionName: "Seattle",
            deptId: "311",
            deptName: "GM/HBC",
            smicGroupCd: 52,
            smicGroupDesc: "HAIR CARE",
            smicCategoryCd: 55,
            smicCategoryDesc: "HAIR CARE MULTI-CULTURAL",
            smicCategoryId: 5255,
            retailSectionCd: "312",
            retailSectionName: "FAMILY CARE GMHBC",
            deskId: "27-25",
            deskName: "27-BONNIE KWOK"
        }
    ],
    userId: "user123",
    userName: "Test User",
    userEmail: "<EMAIL>",
    userRole: "ADMIN",
    createdTimeStamp: "2025-02-18T08:43:04.543Z",
    updatedTimeStamp: "2025-02-18T08:43:04.543Z",
    createdBy: "system",
    updatedBy: "system"
};

describe("Combined Filter and Quarters Query", () => {
    describe("Testing positive scenarios for combined query", () => {
        let app: MockedApplication;
        beforeAll(() => {
            app = testkit
                .mockApplication(gqlApp)
                .replaceModule(
                    testkit.mockModule(TimeRangeInYearModule, {
                        providers: [
                            {
                                provide: TimeRangeInYearProvider.provide,
                                useValue: {
                                    getCalendarData() {
                                        return getQuartersInYearResponse;
                                    }
                                }
                            }
                        ]
                    })
                )
                .replaceModule(
                    testkit.mockModule(WorkSheetFilterModule, {
                        providers: [
                            {
                                provide: WorkSheetFilterProvider.provide,
                                useValue: {
                                    getWorkSheetFilter() {
                                        return rawWorkSheetFilterResponse;
                                    }
                                }
                            }
                        ]
                    })
                );
        });

        it("Should return both filter and quarters data in a single query", async () => {
            const result = await testkit.execute(app, {
                document: gql`
                    query CombinedFilterAndQuarters(
                        $workSheetFilterRequest: WorkSheetFilterReq
                        $timeRangeInYearReq: TimeRangeInYearReq!
                    ) {
                        getWorkSheetFilter(
                            workSheetFilterRequest: $workSheetFilterRequest
                        ) {
                            smicData {
                                divisionId
                                divisionName
                                deptId
                                deptName
                                smicGroupCd
                                smicGroupDesc
                                smicCategoryCd
                                smicCategoryDesc
                                smicCategoryId
                                retailSectionCd
                                retailSectionName
                                deskId
                                deskName
                            }
                            userId
                            userName
                            userEmail
                            userRole
                        }
                        getAllTimeRangeInYear(timeRangeInYearReq: $timeRangeInYearReq) {
                            fiscalYearNumber
                            fiscalQuarterNumber
                            fiscalQuarterStartDate
                            fiscalQuarterEndDate
                        }
                    }
                `,
                variableValues: {
                    workSheetFilterRequest: {},
                    timeRangeInYearReq: { fiscalYearNumber: [2024], level: 'Quarter' }
                },
                contextValue: {}
            });

            // Check that both parts of the query returned data
            expect(result?.data?.getWorkSheetFilter).toBeDefined();
            expect(result?.data?.getAllTimeRangeInYear).toBeDefined();

            // Check filter data
            expect(result?.data?.getWorkSheetFilter).toEqual({
                smicData: getWorkSheetFilterResponse.smicData,
                userId: getWorkSheetFilterResponse.userId,
                userName: getWorkSheetFilterResponse.userName,
                userEmail: getWorkSheetFilterResponse.userEmail,
                userRole: getWorkSheetFilterResponse.userRole
            });

            // Check quarters data
            expect(result?.data?.getAllTimeRangeInYear).toEqual(
                getQuartersInYearResponse.map((quarter) => ({
                    fiscalYearNumber: quarter.fiscalYearNumber,
                    fiscalQuarterNumber: quarter.fiscalQuarterNumber,
                    fiscalQuarterStartDate: quarter.fiscalQuarterStartDate,
                    fiscalQuarterEndDate: quarter.fiscalQuarterEndDate
                }))
            );
        });
    });

    describe("Testing error scenarios for combined query", () => {
        let app: MockedApplication;
        beforeAll(() => {
            app = testkit
                .mockApplication(gqlApp)
                .replaceModule(
                    testkit.mockModule(TimeRangeInYearModule, {
                        providers: [
                            {
                                provide: TimeRangeInYearProvider.provide,
                                useValue: {
                                    getCalendarData() {
                                        return null;
                                    }
                                }
                            }
                        ]
                    })
                )
                .replaceModule(
                    testkit.mockModule(WorkSheetFilterModule, {
                        providers: [
                            {
                                provide: WorkSheetFilterProvider.provide,
                                useValue: {
                                    getWorkSheetFilter() {
                                        return rawWorkSheetFilterResponse;
                                    }
                                }
                            }
                        ]
                    })
                );
        });

        it("Should handle when quarters data is not available", async () => {
            const result = await testkit.execute(app, {
                document: gql`
                    query CombinedFilterAndQuarters(
                        $workSheetFilterRequest: WorkSheetFilterReq
                        $timeRangeInYearReq: TimeRangeInYearReq!
                    ) {
                        getWorkSheetFilter(
                            workSheetFilterRequest: $workSheetFilterRequest
                        ) {
                            userId
                            userName
                        }
                        getAllTimeRangeInYear(timeRangeInYearReq: $timeRangeInYearReq) {
                            fiscalYearNumber
                            fiscalQuarterNumber
                        }
                    }
                `,
                variableValues: {
                    workSheetFilterRequest: {},
                    timeRangeInYearReq: { fiscalYearNumber: [2025], level: 'Quarter' }
                },
                contextValue: {}
            });

            // Filter data should still be available
            expect(result?.data?.getWorkSheetFilter).toEqual({
                userId: getWorkSheetFilterResponse.userId,
                userName: getWorkSheetFilterResponse.userName
            });

            // Quarters data should be empty array
            expect(result?.data?.getAllTimeRangeInYear).toEqual([]);
        });
    });
});
