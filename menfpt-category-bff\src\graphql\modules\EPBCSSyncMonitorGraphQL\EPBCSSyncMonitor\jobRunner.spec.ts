import {
    fetchDatabricksJobData,
    fetchDatabricksJobDataWithStatus
} from "./jobRunner";
import * as connection from "./connection";

describe("jobRunner", () => {
    const originalConsoleError = console.error;
    beforeEach(() => {
        jest.clearAllMocks();
        console.error = jest.fn();
    });
    afterAll(() => {
        console.error = originalConsoleError;
    });

    describe("fetchDatabricksJobData", () => {
        it("should return output on success (default arg)", async () => {
            const fakeOutput = { foo: "bar" };
            jest.spyOn(connection, "fetchJobData").mockResolvedValue({
                output: fakeOutput
            });
            const result = await fetchDatabricksJobData();
            expect(result).toEqual(fakeOutput);
            expect(connection.fetchJobData).toHaveBeenCalledWith(
                connection.DEFAULT_JOB_NAME ?? "",
                false
            );
        });

        it("should return output on success (custom arg)", async () => {
            const fakeOutput = { baz: 123 };
            jest.spyOn(connection, "fetchJobData").mockResolvedValue({
                output: fakeOutput
            });
            const result = await fetchDatabricksJobData("customJob");
            expect(result).toEqual(fakeOutput);
            expect(connection.fetchJobData).toHaveBeenCalledWith(
                "customJob",
                false
            );
        });

        it("should return output on success (number arg)", async () => {
            const fakeOutput = { num: 456 };
            jest.spyOn(connection, "fetchJobData").mockResolvedValue({
                output: fakeOutput
            });
            const result = await fetchDatabricksJobData(123);
            expect(result).toEqual(fakeOutput);
            expect(connection.fetchJobData).toHaveBeenCalledWith(123, false);
        });

        it("should use default when jobIdOrName is undefined", async () => {
            const fakeOutput = { foo: "bar" };
            jest.spyOn(connection, "fetchJobData").mockResolvedValue({
                output: fakeOutput
            });
            const result = await fetchDatabricksJobData(undefined);
            expect(result).toEqual(fakeOutput);
            expect(connection.fetchJobData).toHaveBeenCalledWith(
                connection.DEFAULT_JOB_NAME ?? "",
                false
            );
        });

        it("should handle null output gracefully", async () => {
            jest.spyOn(connection, "fetchJobData").mockResolvedValue({
                output: null
            });
            const result = await fetchDatabricksJobData("customJob");
            expect(result).toBeNull();
        });

        it("should handle missing output key", async () => {
            jest.spyOn(connection, "fetchJobData").mockResolvedValue({});
            const result = await fetchDatabricksJobData("customJob");
            expect(result).toBeUndefined();
        });

        it("should log and re-throw non-Error thrown values", async () => {
            jest.spyOn(connection, "fetchJobData").mockRejectedValue(
                "fail-string"
            );
            await expect(fetchDatabricksJobData("badJob")).rejects.toEqual(
                "fail-string"
            );
            expect(console.error).toHaveBeenCalledWith(
                "Error fetching EPBCSSyncMonitor job data:",
                "fail-string"
            );
        });
    });

    describe("fetchDatabricksJobDataWithStatus", () => {
        it("should return result on success (default arg)", async () => {
            const fakeResult = { status: { foo: "bar" }, output: { baz: 1 } };
            jest.spyOn(connection, "fetchJobData").mockResolvedValue(
                fakeResult
            );
            const result = await fetchDatabricksJobDataWithStatus();
            expect(result).toEqual(fakeResult);
            expect(connection.fetchJobData).toHaveBeenCalledWith(
                connection.DEFAULT_JOB_NAME ?? "",
                true
            );
        });

        it("should return result on success (custom arg)", async () => {
            const fakeResult = { status: { foo: "bar" }, output: { baz: 2 } };
            jest.spyOn(connection, "fetchJobData").mockResolvedValue(
                fakeResult
            );
            const result = await fetchDatabricksJobDataWithStatus(42);
            expect(result).toEqual(fakeResult);
            expect(connection.fetchJobData).toHaveBeenCalledWith(42, true);
        });

        it("should return result on success (string arg)", async () => {
            const fakeResult = { status: { foo: "bar" }, output: { baz: 3 } };
            jest.spyOn(connection, "fetchJobData").mockResolvedValue(
                fakeResult
            );
            const result = await fetchDatabricksJobDataWithStatus("jobName");
            expect(result).toEqual(fakeResult);
            expect(connection.fetchJobData).toHaveBeenCalledWith(
                "jobName",
                true
            );
        });

        it("should use default when jobIdOrName is undefined", async () => {
            const fakeResult = { status: { foo: "bar" }, output: { baz: 4 } };
            jest.spyOn(connection, "fetchJobData").mockResolvedValue(
                fakeResult
            );
            const result = await fetchDatabricksJobDataWithStatus(undefined);
            expect(result).toEqual(fakeResult);
            expect(connection.fetchJobData).toHaveBeenCalledWith(
                connection.DEFAULT_JOB_NAME ?? "",
                true
            );
        });

        it("should handle null result gracefully", async () => {
            jest.spyOn(connection, "fetchJobData").mockResolvedValue(null);
            const result = await fetchDatabricksJobDataWithStatus("customJob");
            expect(result).toBeNull();
        });

        it("should handle missing status/output keys", async () => {
            jest.spyOn(connection, "fetchJobData").mockResolvedValue({});
            const result = await fetchDatabricksJobDataWithStatus("customJob");
            expect(result).toEqual({});
        });

        it("should log and re-throw non-Error thrown values", async () => {
            jest.spyOn(connection, "fetchJobData").mockRejectedValue({
                foo: "bar"
            });
            await expect(
                fetchDatabricksJobDataWithStatus("badJob")
            ).rejects.toEqual({ foo: "bar" });
            expect(console.error).toHaveBeenCalledWith(
                "Error fetching EPBCSSyncMonitor job data with status:",
                { foo: "bar" }
            );
        });
    });

    it("should restore console.error after all tests", () => {
        expect(console.error).toBeDefined();
    });
});
