/**
 * Service for downloading pharmacy files
 */

import { isConnectionAvailable } from '../../pharmaUpload/Connection';
import { DownloadResult } from '../types/pharmaTypes';
import { getBlobClient, findSimilarFileNames } from '../utils/blobUtils';
import { getContentTypeFromFileName, streamToBuffer, isValidFileName } from '../utils/fileUtils';

export class FileDownloadService {
    /**
     * Downloads a specific pharmacy file from blob storage
     */
    async downloadFile(fileName: string): Promise<DownloadResult> {
        // Validate input
        if (!isValidFileName(fileName)) {
            return {
                success: false,
                error: 'Invalid filename provided'
            };
        }

        if (!isConnectionAvailable()) {
            return {
                success: false,
                error: 'Azure Blob Storage connection not available'
            };
        }

        try {
            const blobClient = getBlobClient(fileName);

            // Check if blob exists
            const exists = await blobClient.exists();
            if (!exists) {
                return await this.handleFileNotFound(fileName);
            }

            // Download the blob
            const downloadResponse = await blobClient.download();

            if (!downloadResponse.readableStreamBody) {
                return {
                    success: false,
                    error: 'Failed to get file stream'
                };
            }

            // Convert stream to buffer
            const data = await streamToBuffer(downloadResponse.readableStreamBody);

            // Get file properties
            const properties = await blobClient.getProperties();

            return {
                success: true,
                data: data,
                fileName: fileName,
                contentType: properties.contentType || getContentTypeFromFileName(fileName)
            };

        } catch (error) {
            console.error(`Error downloading pharmacy file '${fileName}':`, error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return {
                success: false,
                error: `Failed to download file: ${errorMessage}`
            };
        }
    }

    /**
     * Handles file not found scenario with helpful error messages
     */
    private async handleFileNotFound(fileName: string): Promise<DownloadResult> {
        const similarFiles = await findSimilarFileNames(fileName);
        let errorMessage = `File '${fileName}' not found in pharmacy folder`;

        if (similarFiles.length > 0) {
            errorMessage += `. Similar files found: ${similarFiles.slice(0, 3).join(', ')}`;
            if (similarFiles.length > 3) {
                errorMessage += ` and ${similarFiles.length - 3} more`;
            }
        }

        console.warn(`File not found: ${fileName}. Available similar files:`, similarFiles);

        return {
            success: false,
            error: errorMessage
        };
    }
}
