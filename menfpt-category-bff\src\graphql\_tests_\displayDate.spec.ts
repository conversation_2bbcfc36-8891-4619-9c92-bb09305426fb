import "reflect-metadata";
import { testkit, gql, MockedApplication } from "graphql-modules";
import { gqlApp } from "../modules/index";
import { CalendarService } from "../modules/services/calendar.service";
import DisplayDateModule from "../modules/DisplayDate";
import { DisplayDateProvider } from "../modules/DisplayDate/providers/DisplayDate.provider";

const getDisplayDateResponse = [
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 1,
        fiscalQuarterNumber: 1,
        fiscalPeriodNumber: 1,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "02/25/2024",
        fiscalWeekEndDate: "03/02/2024",
        fiscalPeriodStartDate: "02/25/2024",
        fiscalPeriodEndDate: "03/23/2024",
        createTimestamp: "2025-02-18T08:43:04.543Z",
        updateTimestamp: "2025-02-18T08:43:04.543Z"
    }
];

describe("getDisplayDate", () => {
    let service: CalendarService;
    beforeEach(() => {
        service = new CalendarService();
    });
    // Fix for file loading/caching error: clear require cache before each test
    beforeEach(() => {
        Object.keys(require.cache).forEach((key) => {
            if (key.includes("displayDate")) {
                delete require.cache[key];
            }
        });
    });
    describe("Testing positive scenarios for getDisplay Date details", () => {
        let app: MockedApplication;
        beforeAll(() => {
            app = testkit.mockApplication(gqlApp).replaceModule(
                testkit.mockModule(DisplayDateModule, {
                    providers: [
                        {
                            provide: DisplayDateProvider.provide,
                            useValue: {
                                getCalendarData() {
                                    return getDisplayDateResponse;
                                }
                            }
                        }
                    ]
                })
            );
        });
        it("Should return the correct response for getDisplayDate query", async () => {
            const result = await testkit.execute(app, {
                document: gql`
                    {
                        getDisplayDate(displayDateReq: { date: "" }) {
                            calendarDetails {
                                fiscalYearNumber
                                fiscalWeekNumber
                                fiscalQuarterNumber
                                fiscalPeriodNumber
                                fiscalQuarterStartDate
                                fiscalQuarterEndDate
                                fiscalWeekStartDate
                                fiscalWeekEndDate
                                fiscalPeriodStartDate
                                fiscalPeriodEndDate
                                createTimestamp
                                updateTimestamp
                            }
                        }
                    }
                `,
                contextValue: {}
            });
            expect(result?.data?.getDisplayDate?.calendarDetails[0]).toEqual({
                fiscalYearNumber: 2024,
                fiscalWeekNumber: 1,
                fiscalQuarterNumber: 1,
                fiscalPeriodNumber: 1,
                fiscalQuarterStartDate: "02/25/2024",
                fiscalQuarterEndDate: "06/15/2024",
                fiscalWeekStartDate: "02/25/2024",
                fiscalWeekEndDate: "03/02/2024",
                fiscalPeriodStartDate: "02/25/2024",
                fiscalPeriodEndDate: "03/23/2024",
                createTimestamp: "2025-02-18T08:43:04.543Z",
                updateTimestamp: "2025-02-18T08:43:04.543Z"
            });
        });
    });
});

describe("DisplayDateResolver additional coverage", () => {
    const {
        DisplayDateResolver
    } = require("../modules/DisplayDate/resolvers/DisplayDate.resolver");
    const { LOCKOUT_CERTIFY_CUTTOFF } = require("../Util");

    it("throws error if DisplayDateService is missing in context", async () => {
        await expect(
            DisplayDateResolver(
                null,
                { displayDateReq: {} },
                { injector: { get: () => undefined } }
            )
        ).rejects.toThrow(
            "DisplayDateService is not available in the context injector."
        );
    });

    it("handles multiple qtrNumbersArr and flattens results", async () => {
        const mockService = {
            getCalendarData: jest
                .fn()
                .mockImplementation(({ fiscalQuarterNumber }) => [
                    {
                        fiscalQuarterNumber,
                        fiscalPeriodLockoutDate: "01/01/2024",
                        fiscalPeriodCertificationDate: "02/01/2024"
                    }
                ])
        };
        const context = { injector: { get: () => mockService } };
        const req = { qtrNumbersArr: [1, 2] };
        const result = await DisplayDateResolver(
            null,
            { displayDateReq: req },
            context
        );
        expect(result.calendarDetails.length).toBe(2);
        expect(result.calendarDetails[0].fiscalQuarterNumber).toBe(1);
        expect(result.calendarDetails[1].fiscalQuarterNumber).toBe(2);
        expect(result.calendarDetails[0].fiscalPeriodLockoutDate).toContain(
            LOCKOUT_CERTIFY_CUTTOFF
        );
        expect(
            result.calendarDetails[0].fiscalPeriodCertificationDate
        ).toContain(LOCKOUT_CERTIFY_CUTTOFF);
    });

    it("returns empty array if getCalendarData returns empty", async () => {
        const mockService = {
            getCalendarData: jest.fn().mockResolvedValue([])
        };
        const context = { injector: { get: () => mockService } };
        const req = {};
        const result = await DisplayDateResolver(
            null,
            { displayDateReq: req },
            context
        );
        expect(result.calendarDetails).toEqual([]);
    });

    it("returns empty array if getCalendarData returns undefined", async () => {
        const mockService = {
            getCalendarData: jest.fn().mockResolvedValue(undefined)
        };
        const context = { injector: { get: () => mockService } };
        const req = {};
        const result = await DisplayDateResolver(
            null,
            { displayDateReq: req },
            context
        );
        expect(result.calendarDetails).toEqual([]);
    });

    it("handles missing qtrNumbersArr and returns first data element", async () => {
        const mockService = {
            getCalendarData: jest.fn().mockResolvedValue([{ foo: "bar" }])
        };
        const context = { injector: { get: () => mockService } };
        const req = {};
        const result = await DisplayDateResolver(
            null,
            { displayDateReq: req },
            context
        );
        expect(result.calendarDetails).toEqual([{ foo: "bar" }]);
    });
});
