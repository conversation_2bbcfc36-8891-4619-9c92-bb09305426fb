import "reflect-metadata";
import { testkit, gql, MockedApplication } from "graphql-modules";
import { gqlApp } from "../../../../modules/index";
import { UserInfoService } from "../../../../modules/userInfo/services/userInfo.service";
import UserInfoModule from "../../../../modules/userInfo";
import { UserInfoProvider } from "../../../../modules/userInfo/providers/userInfo.provider";
import { ContextValue } from "../../../../context";
import { get } from "lodash";

const getUserInfoResponse = {
    userId: "<EMAIL>",
    userName: "<PERSON><PERSON><PERSON> (Contractor)",
    userEmail: "<EMAIL>",
    userRole: "DivisionalManager",
    userPermissions: {
        canView: true,
        canEdit: true
    }
}

describe("getUserInfo", () => {
    describe("Testing positive scenarios for getUserInfo details", () => {
        let app: MockedApplication;
        beforeAll(() => {
            app = testkit.mockApplication(gqlApp).replaceModule(
                testkit.mockModule(UserInfoModule, {
                    providers: [
                        {
                            provide: UserInfoProvider.provide,
                            useValue: {
                                fetchUserInfoDetails() {
                                    return getUserInfoResponse;
                                }
                            }
                        }
                    ]
                })
            );
        });
        it("Should return the correct response for getUserInfo query", async () => {
            const result = await testkit.execute(app, {
                document: gql`
                    {
                        userInfo {
                            userId
                            userName
                            userEmail
                            userRole
                            userPermissions {
                                canView
                                canEdit
                            }
                        }
                    }
                `,
                contextValue: {}
            });
            expect(result.data).toEqual({
                userInfo: getUserInfoResponse
            });
        });
    });
});