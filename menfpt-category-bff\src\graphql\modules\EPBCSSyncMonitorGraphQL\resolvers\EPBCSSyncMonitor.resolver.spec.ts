import "reflect-metadata";

// Mock helper functions
const mockGetFiscalWeekDates = jest.fn();
const mockGetJobRunsFromDatabricks = jest.fn();
const mockParseRunDateTime = jest.fn();
const mockFormatDateString = jest.fn();
const mockGetWeeksForFiscalPeriod = jest.fn();
const mockGetWeekNumberOfDate = jest.fn();

// Deep mocks for Databricks/connection dependencies
jest.mock("../EPBCSSyncMonitor", () => ({
    getJobRunsFromDatabricks: mockGetJobRunsFromDatabricks,
    parseCronHours: jest.fn(() => [9, 10]),
    formatCronHourToReadableTime: jest.fn((hour: number) => `${hour}:00 AM`),
    MONDAY_CRON: "0 9 * * 1",
    THURSDAY_CRON: "0 10 * * 4",
    FRIDAY_CRON: "0 11 * * 5",
    DEFAULT_JOB_NAME: "test-job"
}));

jest.mock("../helpers/EPBCSSyncMonitorHelper", () => ({
    getFiscalWeekDates: mockGetFiscalWeekDates,
    parseRunDateTime: mockParseRunDateTime,
    formatDateString: mockFormatDateString,
    checkJobRunAtTime: jest.fn(),
    parseTimeString: jest.fn(),
    parseSessionDateTime: jest.fn(),
    generateFormattedJobRuns: jest.fn(),
    getFiscalWeekForDate: jest.fn(),
    getActiveJobs: jest.fn(),
    getWeekNumberOfDate: jest.fn(),
    processDaySchedule: jest.fn(),
    getPreviousWeekMondaySync: jest.fn(),
    getDefaultWeeks: jest.fn(),
    getWeeksForFiscalPeriod: mockGetWeeksForFiscalPeriod,
    logPeriodFormattedRuns: jest.fn(),
    findRunsInDateRange: jest.fn(),
    groupRunsByDate: jest.fn(),
    generateSyncSessionsForPeriod: jest.fn(() => []),
    updateSyncHistoryWithPreviousMonday: jest.fn(),
    getSyncsForAllMondays: jest.fn(() => [])
}));

jest.mock("../../../Util", () => ({
    getPacificDate: jest.fn(() => new Date("2023-01-01")),
    formatDateString: mockFormatDateString
}));

jest.mock("../../../EPBCSSyncMonitor_WeeksDateUtility", () => ({
    getWeekNumberOfDate: mockGetWeekNumberOfDate
}));

describe("EPBCSSyncMonitor.resolver", () => {
    beforeEach(() => {
        jest.resetModules();
        jest.spyOn(
            require("../helpers/EPBCSSyncMonitorHelper"),
            "getFiscalWeekDates"
        ).mockResolvedValue({
            start: "01/01/2023",
            end: "01/07/2023",
            fiscalPeriodStartDate: "01/01/2023",
            fiscalPeriodEndDate: "01/31/2023"
        });
    });
    describe("generateSyncSessionsForPeriod", () => {
        it("returns sessions for valid runs and period", () => {
            const resolver = require("./EPBCSSyncMonitor.resolver");
            const runs = [
                {
                    start_time: "01/02/2023, 1:00 PM",
                    result_state: "SUCCESS",
                    end_time: "2:00 PM",
                    life_cycle_state: "RUNNING"
                }
            ];
            const sessions = resolver.generateSyncSessionsForPeriod(
                runs,
                "01/01/2023",
                "01/31/2023"
            );
            expect(Array.isArray(sessions)).toBe(true);
        });
        it("returns empty array for no runs", () => {
            const resolver = require("./EPBCSSyncMonitor.resolver");
            const sessions = resolver.generateSyncSessionsForPeriod(
                [],
                "01/01/2023",
                "01/31/2023"
            );
            expect(Array.isArray(sessions)).toBe(true);
        });
        it("handles edge case with invalid dates", () => {
            const resolver = require("./EPBCSSyncMonitor.resolver");
            const runs = [
                {
                    start_time: "invalid",
                    result_state: "FAILURE",
                    end_time: "invalid",
                    life_cycle_state: "TERMINATED"
                }
            ];
            const sessions = resolver.generateSyncSessionsForPeriod(
                runs,
                "-",
                "-"
            );
            expect(Array.isArray(sessions)).toBe(true);
        });
    });

    describe("GetJobRunsFromDatabricksResolver", () => {
        it("throws and logs error on dependency failure", async () => {
            const resolver = require("./EPBCSSyncMonitor.resolver");
            jest.spyOn(
                require("../EPBCSSyncMonitor"),
                "getJobRunsFromDatabricks"
            ).mockRejectedValue(new Error("fail"));
            const args = { input: { jobName: "test-job", limit: 1 } };
            const context = {};
            await expect(
                resolver.GetJobRunsFromDatabricksResolver(null, args, context)
            ).rejects.toThrow();
        });
    });
});

describe("EPBCSSyncMonitorResolver.Query.getJobRunsFromDatabricks", () => {
    it("throws if neither jobId nor jobName is provided", async () => {
        const resolver = require("./EPBCSSyncMonitor.resolver");
        const args = { input: {} };
        const context = {};
        await expect(
            resolver.EPBCSSyncMonitorResolver.Query.getJobRunsFromDatabricks(
                null,
                args,
                context
            )
        ).rejects.toThrow("Either jobId or jobName must be provided");
    });

    it("should successfully fetch job runs with jobId", async () => {
        const mockJobRuns = {
            runs: [
                { start_time: "01/02/2023, 1:00 PM", result_state: "SUCCESS" }
            ]
        };

        mockGetJobRunsFromDatabricks.mockResolvedValueOnce(mockJobRuns);
        mockGetFiscalWeekDates.mockResolvedValueOnce({
            start: "01/01/2023",
            end: "01/07/2023"
        });

        const resolver = require("./EPBCSSyncMonitor.resolver");
        const result = await resolver.EPBCSSyncMonitorResolver.Query.getJobRunsFromDatabricks(
            null,
            { input: { jobId: "123", limit: 5 } },
            {}
        );

        expect(mockGetJobRunsFromDatabricks).toHaveBeenCalledWith("123", 5);
        expect(result).toEqual(mockJobRuns);
    });

    it("should successfully fetch job runs with jobName", async () => {
        const mockJobRuns = {
            runs: [
                { start_time: "01/02/2023, 1:00 PM", result_state: "SUCCESS" }
            ]
        };

        mockGetJobRunsFromDatabricks.mockResolvedValueOnce(mockJobRuns);
        mockGetFiscalWeekDates.mockResolvedValueOnce({
            start: "01/01/2023",
            end: "01/07/2023"
        });

        const resolver = require("./EPBCSSyncMonitor.resolver");
        const result = await resolver.EPBCSSyncMonitorResolver.Query.getJobRunsFromDatabricks(
            null,
            { input: { jobName: "test-job", limit: 10 } },
            {}
        );

        expect(mockGetJobRunsFromDatabricks).toHaveBeenCalledWith("test-job", 10);
        expect(result).toEqual(mockJobRuns);
    });

    it("should use default limit when not provided", async () => {
        const mockJobRuns = { runs: [] };

        mockGetJobRunsFromDatabricks.mockResolvedValueOnce(mockJobRuns);
        mockGetFiscalWeekDates.mockResolvedValueOnce({
            start: "01/01/2023",
            end: "01/07/2023"
        });

        const resolver = require("./EPBCSSyncMonitor.resolver");
        await resolver.EPBCSSyncMonitorResolver.Query.getJobRunsFromDatabricks(
            null,
            { input: { jobId: "123" } },
            {}
        );

        expect(mockGetJobRunsFromDatabricks).toHaveBeenCalledWith("123", 10);
    });

    it("should handle getJobRunsFromDatabricks errors and log them", async () => {
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

        mockGetJobRunsFromDatabricks.mockRejectedValueOnce(new Error("Databricks error"));
        mockGetFiscalWeekDates.mockResolvedValueOnce({
            start: "01/01/2023",
            end: "01/07/2023"
        });

        const resolver = require("./EPBCSSyncMonitor.resolver");

        await expect(
            resolver.EPBCSSyncMonitorResolver.Query.getJobRunsFromDatabricks(
                null,
                { input: { jobId: "123" } },
                {}
            )
        ).rejects.toThrow("Databricks error");

        expect(consoleErrorSpy).toHaveBeenCalledWith(
            "Error in getJobRunsFromDatabricks resolver:",
            expect.any(Error)
        );

        consoleErrorSpy.mockRestore();
    });
});

// Only test exported functions directly
// Internal functions are not exported and cannot be tested directly
// The following tests are commented out because the functions are not exported
/*
describe("getWeeksForFiscalPeriod", () => {
    it("returns correct weeks for a period", () => {
        const weeks = resolver.getWeeksForFiscalPeriod("01/01/2023", "01/31/2023");
        expect(Array.isArray(weeks)).toBe(true);
        expect(weeks.length).toBeGreaterThan(0);
    });
});

describe("findNextSync", () => {
    it("returns null if no sessions", () => {
        const result = resolver.findNextSync([], "01/01/2023", "01/31/2023", []);
        expect(result).toBeNull();
    });
});

describe("findLastSync", () => {
    it("returns null if no completed sessions", () => {
        const result = resolver.findLastSync({ runs: [] }, []);
        expect(result).toBeNull();
    });
});

describe("getSyncsForAllMondays", () => {
    it("returns an array for valid runs", () => {
        const runs = [{ start_time: "2023-01-02T00:00:00Z" }];
        const result = resolver.getSyncsForAllMondays(runs, "01/01/2023");
        expect(Array.isArray(result)).toBe(true);
    });
});

describe("updateSyncHistoryWithPreviousMonday", () => {
    it("returns updated sync history", () => {
        const syncHistory = { weeks: [], mondaySyncs: [] };
        const runs = [{ start_time: "2023-01-02T00:00:00Z" }];
        const result = resolver.updateSyncHistoryWithPreviousMonday(syncHistory, runs, "01/01/2023");
        expect(result).toHaveProperty('weeks');
        expect(result).toHaveProperty('mondaySyncs');
    });
});

describe("getPreviousWeekLastSync", () => {
    it("returns null if no runs", () => {
        const result = resolver.getPreviousWeekLastSync([]);
        expect(result).toBeNull();
    });
});
*/
