import { PharmaDownloadProvider } from "../providers/pharmaDownload.provider";

export default {
    Query: {
        listPharmacyFiles: async (_: any, __: any, { injector }: any) => {
            const pharmaDownloadProvider = injector.get(PharmaDownloadProvider);
            return await pharmaDownloadProvider.listPharmacyFiles();
        },

        generatePharmacyFileDownloadUrl: async (_: any, { fileName, expiryMinutes }: any, { injector }: any) => {
            const pharmaDownloadProvider = injector.get(PharmaDownloadProvider);
            return await pharmaDownloadProvider.generatePharmacyFileDownloadUrl(fileName, expiryMinutes);
        }
    }
};
