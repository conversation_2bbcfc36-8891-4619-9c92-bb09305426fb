<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>BFF Live Log Viewer</title>
        <style>
            body {
                background-color: #1e1e1e;
                color: #d4d4d4;
                font-family: Consolas, "Courier New", monospace;
                margin: 0;
            }
            #header {
                background-color: #333;
                padding: 10px 20px;
                font-size: 1.2em;
                border-bottom: 2px solid #007acc;
            }
            #log-container {
                padding: 20px;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            .log-entry {
                border-left: 3px solid #555;
                padding: 10px;
                margin-bottom: 15px;
                background-color: #252526;
                border-radius: 4px;
            }
            .log-entry.info {
                border-left-color: #007acc;
            }
            .log-entry.error {
                border-left-color: #f44747;
                background-color: #4b1e1e;
            }
            details {
                cursor: pointer;
                margin-top: 5px;
            }
            summary {
                font-weight: bold;
                color: #4ec9b0;
            }
            pre {
                background-color: #1e1e1e;
                padding: 10px;
                border-radius: 4px;
                white-space: pre-wrap;
            }
        </style>
    </head>
    <body>
        <div id="header">BFF Live Log Viewer</div>
        <div id="log-container">Loading logs...</div>

        <script>
            const logContainer = document.getElementById("log-container");
            let lastLogContent = "";

            function renderLogs(fullLogText) {
                if (fullLogText === lastLogContent) {
                    return; // No new content
                }

                logContainer.innerHTML = ""; // Clear and re-render
                lastLogContent = fullLogText;

                const separator = "=====================================";
                const entries = fullLogText
                    .split(new RegExp(`\\n\\n${separator}`))
                    .filter(Boolean);

                entries.forEach((logText) => {
                    const entryDiv = document.createElement("div");
                    let level = "info";
                    if (logText.includes("    !!! ERROR !!!")) {
                        level = "error";
                    }
                    entryDiv.className = "log-entry " + level;

                    const responseRegex = /(response\s+:\s*)({.*})/s;
                    const match = logText.match(responseRegex);

                    if (match && match[2].length > 200) {
                        const preResponseText = logText.substring(
                            0,
                            match.index + match[1].length
                        );
                        const responseJson = match[2];
                        const postResponseText = logText.substring(
                            match.index + match[0].length
                        );

                        const details = document.createElement("details");
                        const summary = document.createElement("summary");
                        summary.textContent = "Response (Click to expand)";
                        details.appendChild(summary);

                        const pre = document.createElement("pre");
                        try {
                            pre.textContent = JSON.stringify(
                                JSON.parse(responseJson),
                                null,
                                2
                            );
                        } catch (e) {
                            pre.textContent = responseJson;
                        }
                        details.appendChild(pre);

                        const tempDiv = document.createElement("div");
                        tempDiv.textContent = preResponseText;
                        entryDiv.appendChild(tempDiv);
                        entryDiv.appendChild(details);

                        const postDiv = document.createElement("div");
                        postDiv.textContent = postResponseText;
                        entryDiv.appendChild(postDiv);
                    } else {
                        entryDiv.textContent = logText;
                    }
                    logContainer.appendChild(entryDiv);
                });
            }

            async function fetchLogs() {
                try {
                    const response = await fetch("api/logs");
                    if (response.ok) {
                        const text = await response.text();
                        renderLogs(text);
                    } else {
                        logContainer.textContent = `Error loading logs: ${response.statusText}`;
                    }
                } catch (e) {
                    console.error("Failed to fetch logs:", e);
                    logContainer.textContent =
                        "Failed to fetch logs. Is the server running?";
                }
            }

            setInterval(fetchLogs, 2000);
            fetchLogs(); // Initial call
        </script>
    </body>
</html>
