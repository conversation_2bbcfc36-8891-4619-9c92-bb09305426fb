import { EnvVariablesService } from "../envVariables.provider";

describe("EnvVariablesService", () => {
    let service: EnvVariablesService;
    let originalEnv: NodeJS.ProcessEnv;

    beforeEach(() => {
        service = new EnvVariablesService();
        originalEnv = { ...process.env };
    });

    afterEach(() => {
        process.env = originalEnv;
    });

    it("should return only whitelisted environment variables and variables with _EXP suffix", async () => {
        // Set some test environment variables
        process.env.PHARMA_UPLOAD_DAYS = "THURSDAY,FRIDAY";
        process.env.LINE1_TOOLTIP_TEXT = "Line 1 tooltip";
        process.env.TEST_VAR = "test_value";
        process.env.NUMERIC_VAR = "42";
        process.env.SECRET_API_KEY = "secret123";
        process.env.DATABASE_PASSWORD = "password123";
        process.env.CONFIG_EXP = "config_value";
        process.env.SETTINGS_EXP = "settings_value";
        process.env.API_ENDPOINT_EXP = "https://api.example.com";

        // Set some system variables that should be filtered out
        process.env.COMPUTERNAME = "TEST-PC";
        process.env.USERNAME = "testuser";
        process.env.PATH = "/usr/bin:/usr/local/bin";
        process.env.npm_config_registry = "https://registry.npmjs.org/";

        const result = await service.getEnvVariables();

        expect(result).toHaveProperty("variables");
        expect(typeof result.variables).toBe("object");
        expect(Array.isArray(result.variables)).toBe(false);

        // Check that variables with _EXP suffix are included
        expect(result.variables.CONFIG_EXP).toBe("config_value");
        expect(result.variables.SETTINGS_EXP).toBe("settings_value");
        expect(result.variables.API_ENDPOINT_EXP).toBe(
            "https://api.example.com"
        );

        // Check that non-whitelisted variables without _EXP suffix are excluded
        expect(result.variables.TEST_VAR).toBeUndefined();
        expect(result.variables.NUMERIC_VAR).toBeUndefined();
        expect(result.variables.SECRET_API_KEY).toBeUndefined();
        expect(result.variables.DATABASE_PASSWORD).toBeUndefined();

        // Check that whitelisted variables are included
        expect(result.variables.PHARMA_UPLOAD_DAYS).toBe("THURSDAY,FRIDAY");
        expect(result.variables.LINE1_TOOLTIP_TEXT).toBe("Line 1 tooltip");

        // Check that system variables are excluded
        expect(result.variables.COMPUTERNAME).toBeUndefined();
        expect(result.variables.USERNAME).toBeUndefined();
        expect(result.variables.PATH).toBeUndefined();
        expect(result.variables.npm_config_registry).toBeUndefined();
    });

    it("should return whitelisted variables when they are added to the whitelist", async () => {
        // Create a service instance and add variables to whitelist
        const serviceWithWhitelist = new EnvVariablesService();

        // Set test environment variables
        process.env.PHARMA_UPLOAD_DAYS = "THURSDAY,FRIDAY";
        process.env.LINE1_TOOLTIP_TEXT = "Line 1 tooltip";
        process.env.DATABASE_URL = "postgresql://localhost:5432/db";
        process.env.SECRET_KEY = "secret123";
        process.env.CONFIG_EXP = "config_value";

        const result = await serviceWithWhitelist.getEnvVariables();

        // _EXP variables should be returned
        expect(result.variables.CONFIG_EXP).toBe("config_value");

        // Whitelisted variables should be returned
        expect(result.variables.PHARMA_UPLOAD_DAYS).toBe("THURSDAY,FRIDAY");
        expect(result.variables.LINE1_TOOLTIP_TEXT).toBe("Line 1 tooltip");

        // Non-whitelisted variables should be excluded
        expect(result.variables.SECRET_KEY).toBeUndefined();
        expect(result.variables.CUSTOM_VAR).toBeUndefined();
    });

    it("should not include undefined environment variables", async () => {
        // Set a variable to undefined
        process.env.UNDEFINED_VAR = undefined as any;

        const result = await service.getEnvVariables();

        expect(result.variables.UNDEFINED_VAR).toBeUndefined();
    });

    it("should return empty object when no environment variables are set", async () => {
        // Clear all environment variables
        process.env = {};

        const result = await service.getEnvVariables();

        expect(result.variables).toEqual({});
    });

    it("should parse numeric values correctly for _EXP variables", async () => {
        process.env.INTEGER_EXP = "123";
        process.env.FLOAT_EXP = "123.45";
        process.env.ZERO_EXP = "0";
        process.env.EMPTY_EXP = "";
        process.env.STRING_EXP = "hello";

        const result = await service.getEnvVariables();

        expect(result.variables.INTEGER_EXP).toBe(123);
        expect(result.variables.FLOAT_EXP).toBe(123.45);
        expect(result.variables.ZERO_EXP).toBe(0);
        expect(result.variables.EMPTY_EXP).toBe(""); // Empty string should remain string
        expect(result.variables.STRING_EXP).toBe("hello");
    });

    it("should include any environment variables with _EXP suffix regardless of system status", async () => {
        // Set various system variables with _EXP suffix that should now be included
        process.env.COMPUTERNAME_EXP = "TEST-PC";
        process.env.USERNAME_EXP = "testuser";
        process.env.PATH_EXP = "/usr/bin:/usr/local/bin";
        process.env.npm_config_registry_EXP = "https://registry.npmjs.org/";
        process.env.VSCODE_GIT_ASKPASS_MAIN_EXP = "main.js";
        process.env.GIT_ASKPASS_EXP = "askpass.sh";
        process.env.JAVA_HOME_EXP = "/usr/lib/jvm/java-11";
        process.env.TERM_PROGRAM_EXP = "vscode";
        process.env.CHROME_CRASHPAD_PIPE_NAME_EXP = "pipe";
        process.env.CURSOR_TRACE_ID_EXP = "trace123";
        process.env.ZES_ENABLE_SYSMAN_EXP = "1";

        const result = await service.getEnvVariables();

        // All these variables with _EXP suffix should now be included
        expect(result.variables.COMPUTERNAME_EXP).toBe("TEST-PC");
        expect(result.variables.USERNAME_EXP).toBe("testuser");
        expect(result.variables.PATH_EXP).toBe("/usr/bin:/usr/local/bin");
        expect(result.variables.npm_config_registry_EXP).toBe(
            "https://registry.npmjs.org/"
        );
        expect(result.variables.VSCODE_GIT_ASKPASS_MAIN_EXP).toBe("main.js");
        expect(result.variables.GIT_ASKPASS_EXP).toBe("askpass.sh");
        expect(result.variables.JAVA_HOME_EXP).toBe("/usr/lib/jvm/java-11");
        expect(result.variables.TERM_PROGRAM_EXP).toBe("vscode");
        expect(result.variables.CHROME_CRASHPAD_PIPE_NAME_EXP).toBe("pipe");
        expect(result.variables.CURSOR_TRACE_ID_EXP).toBe("trace123");
        expect(result.variables.ZES_ENABLE_SYSMAN_EXP).toBe(1); // Numeric values are automatically parsed
    });

    it("should return only _EXP variables when whitelist is empty", async () => {
        // Set mix of regular and _EXP variables
        process.env.REGULAR_VAR = "regular_value";
        process.env.SECRET_VAR = "secret_value";
        process.env.CONFIG_EXP = "config_value";
        process.env.SETTINGS_EXP = "settings_value";
        process.env.API_EXP = "api_value";

        const result = await service.getEnvVariables();

        // Only _EXP variables should be returned
        expect(result.variables.CONFIG_EXP).toBe("config_value");
        expect(result.variables.SETTINGS_EXP).toBe("settings_value");
        expect(result.variables.API_EXP).toBe("api_value");

        // Regular variables should be excluded
        expect(result.variables.REGULAR_VAR).toBeUndefined();
        expect(result.variables.SECRET_VAR).toBeUndefined();
    });
});
