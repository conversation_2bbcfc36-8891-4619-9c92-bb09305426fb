
scalar BigInt
scalar JSON

type CronScheduleInfo {
  monday: String
  thursday: String
  friday: String
}

type ScheduledTimeInfo {
  Monday: String
  Thursday: String
  Friday: String
}

type DatabricksJobRun {
  start_time: String
  end_time: String
  result_state: String
}

type FiscalWeekDates {
  thursday: String
  friday: String
  monday: String
}

type SyncDayInfo {
  sync_date: String
  sync_day: String
}

type SyncDays {
  sync_days: [SyncDayInfo]
}

type SyncSession {
  time: String
  status: String
  day: String
  date: String
}

type NextSync {
  sync_day: String
  sync_time: String
}

type LastSync {
  sync_day: String
  sync_time: String
}

type WeekRunInfo {
  run_id: BigInt
  date: String
  time: String
  status: String
  weekNumber: Int
}

type SimplifiedRunInfo {
  date: String
  time: String
  weekNumber: Int
}

type WeekInfo {
  lastRun: SimplifiedRunInfo
}

type SyncHistory {
  weeks: [WeekInfo]
}

type DatabricksJobRunsResult {
  syncSessions: [SyncSession]
  nextSync: NextSync
  lastSync: LastSync
  syncHistory: SyncHistory
}

input FetchJobRunsInput {
  jobId: BigInt
  jobName: String
  limit: Int
}

type Query {
  getJobRunsFromDatabricks(input: FetchJobRunsInput!): DatabricksJobRunsResult
}
