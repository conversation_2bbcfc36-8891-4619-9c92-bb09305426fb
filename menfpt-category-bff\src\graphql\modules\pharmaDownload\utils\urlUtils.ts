/**
 * Utility functions for URL generation and validation
 */

/**
 * Generates a BFF-proxied download URL
 */
export function generateBffDownloadUrl(fileName: string): string {
    const baseUrl = process.env.BFF_BASE_URL || '';
    return `${baseUrl}/menfpt-category-bff/api/pharmacy/download/${encodeURIComponent(fileName)}`;
}

/**
 * Creates expiry date for SAS URLs
 */
export function createExpiryDate(expiryMinutes: number): Date {
    const expiresOn = new Date();
    expiresOn.setMinutes(expiresOn.getMinutes() + expiryMinutes);
    return expiresOn;
}
