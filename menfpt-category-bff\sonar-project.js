const sonarqubeScanner = require("sonarqube-scanner");
sonarqubeScanner(
  {
    serverUrl: "https://sonarqube.albertsons.com",
    options: {
      "sonar.sources": "./src/",
      "sonar.exclusions":
        "**/__mocks__/**, **/*.test.*,  **/status-constants.tsx, **/*-mock.js, node_modules/**, coverage/**, public/**, build/**, dist/**, reports/**,src/index.tsx, src-msal-server/**, src/index.ts, src/bootstrap.ts",
      "sonar.tests": "./src/",
      "sonar.test.inclusions": "**/*.spec.tsx,**/*.spec.jsx,**/*.spec.ts",
      "sonar.javascript.lcov.reportPaths": "coverage/lcov.info",
      "sonar.testExecutionReportPaths": "coverage/test-report.xml",
    },
  },
  () => { }
);
