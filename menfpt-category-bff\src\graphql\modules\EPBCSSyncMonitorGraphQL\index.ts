import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";
import { createModule } from "graphql-modules";
import {
    GetJobRunsFromDatabricksResolver
} from "./resolvers/EPBCSSyncMonitor.resolver";
import { EPBCSSyncMonitorGraphQLProvider } from "./providers/EPBCSSyncMonitor.provider";
import { DisplayDateProvider } from "../DisplayDate/providers/DisplayDate.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));

const resolvers = {
    Query: {
        getJobRunsFromDatabricks: GetJobRunsFromDatabricksResolver
    }
};

const EPBCSSyncMonitorModule = createModule({
    id: "EPBCSSyncMonitorGraphQL",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [EPBCSSyncMonitorGraphQLProvider, DisplayDateProvider]
});

export default EPBCSSyncMonitorModule;
