import express from 'express';
import request from 'supertest';

// Mock the PharmaDownloadService before importing the router
const mockListPharmacyFiles = jest.fn();
const mockDownloadPharmacyFile = jest.fn();
const mockGenerateDownloadUrl = jest.fn();
const mockFindSimilarFiles = jest.fn();

jest.mock('../graphql/modules/pharmaDownload/services/pharmaDownload.service', () => {
    return {
        PharmaDownloadService: jest.fn().mockImplementation(() => ({
            listPharmacyFiles: mockListPharmacyFiles,
            downloadPharmacyFile: mockDownloadPharmacyFile,
            generateDownloadUrl: mockGenerateDownloadUrl,
            findSimilarFiles: mockFindSimilarFiles,
        }))
    };
});

import pharmaDownloadRouter from './pharmaDownloadRouter';

describe('pharmaDownloadRouter', () => {
    let app: express.Express;

    beforeEach(() => {
        app = express();
        app.use(express.json());
        app.use('/api/pharmacy', pharmaDownloadRouter);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('GET /api/pharmacy/files', () => {
        it('should return list of files successfully', async () => {
            const mockFiles = [
                {
                    fileName: 'file1.xlsx',
                    fullPath: '/path/to/file1.xlsx',
                    size: 1024,
                    lastModified: new Date('2025-08-01T10:00:00Z')
                },
                {
                    fileName: 'file2.xlsx',
                    fullPath: '/path/to/file2.xlsx',
                    size: 2048,
                    lastModified: new Date('2025-08-02T10:00:00Z')
                }
            ];

            mockListPharmacyFiles.mockResolvedValue(mockFiles);

            const response = await request(app).get('/api/pharmacy/files');

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.count).toBe(2);
            expect(response.body.files).toHaveLength(2);
            expect(response.body.files[0].fileName).toBe('file1.xlsx');
            expect(response.body.files[0].size).toBe(1024);
            expect(response.body.files[0].lastModified).toBe('2025-08-01T10:00:00.000Z');
            expect(response.body.files[1].fileName).toBe('file2.xlsx');
            expect(response.body.files[1].size).toBe(2048);
            expect(response.body.files[1].lastModified).toBe('2025-08-02T10:00:00.000Z');
            expect(mockListPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should handle errors when listing files', async () => {
            const errorMessage = 'Failed to list files';
            mockListPharmacyFiles.mockRejectedValue(new Error(errorMessage));

            const response = await request(app).get('/api/pharmacy/files');

            expect(response.status).toBe(500);
            expect(response.body).toEqual({
                success: false,
                error: errorMessage
            });
        });

        it('should handle non-Error exceptions when listing files', async () => {
            mockListPharmacyFiles.mockRejectedValue('String error');

            const response = await request(app).get('/api/pharmacy/files');

            expect(response.status).toBe(500);
            expect(response.body).toEqual({
                success: false,
                error: 'Unknown error occurred'
            });
        });
    });
    describe('Edge cases and integration', () => {
        it('should handle special characters in file names', async () => {
            const specialFileName = 'file with spaces & symbols!.xlsx';
            const mockResult = {
                success: true,
                data: Buffer.from('mock data'),
                fileName: specialFileName,
                contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };

            mockDownloadPharmacyFile.mockResolvedValue(mockResult);

            const response = await request(app).get(`/api/pharmacy/download/${encodeURIComponent(specialFileName)}`);

            expect(response.status).toBe(200);
            expect(mockDownloadPharmacyFile).toHaveBeenCalledWith(specialFileName);
        });

        it('should handle empty file list', async () => {
            mockListPharmacyFiles.mockResolvedValue([]);

            const response = await request(app).get('/api/pharmacy/files');

            expect(response.status).toBe(200);
            expect(response.body).toEqual({
                success: true,
                files: [],
                count: 0
            });
        });

        it('should handle zero-length files', async () => {
            const mockResult = {
                success: true,
                data: Buffer.alloc(0),
                fileName: 'empty-file.xlsx',
                contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };

            mockDownloadPharmacyFile.mockResolvedValue(mockResult);

            const response = await request(app).get('/api/pharmacy/download/empty-file.xlsx');

            expect(response.status).toBe(200);
            expect(response.headers['content-length']).toBe('0');
        });
    });
});
