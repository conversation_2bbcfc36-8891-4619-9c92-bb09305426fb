import type { Config } from "jest";

const config: Config = {
    verbose: true,
    preset: "ts-jest",
    testEnvironment: "node",
    testResultsProcessor: "jest-sonar-reporter",
    setupFilesAfterEnv: ["<rootDir>/src/test-setup.ts"],
    testPathIgnorePatterns: [
        "/node_modules/",
        "/dist/"
    ],
    modulePathIgnorePatterns: [
        "/dist/"
    ],
    roots: [
        "<rootDir>/src"
    ],
    testMatch: [
        "**/__tests__/**/*.ts",
        "**/?(*.)+(spec|test).ts"
    ],
    coveragePathIgnorePatterns: [
        "/node_modules/",
        "src/graphql/shared/utils/logApiCall.ts",
        "src/graphql/shared/utils/logger.ts"
    ]
};

export default config;
