import { jest, describe, it, expect, beforeEach } from "@jest/globals";
import { HELP_ICON_ENDPOINTS } from "../../graphql/shared/constants/routerConstants";
import helpIconRouter from "../helpIconRouter";
import { Request, Response } from "express";

const mockJson = jest.fn();
const mockStatus = jest
    .fn()
    .mockReturnValue({ json: mockJson, send: mockJson });
const mockRes = { status: mockStatus, json: mockJson } as unknown as Response;
const mockNext = jest.fn();

describe("helpIconRouter", () => {
    let req: Partial<Request>;

    beforeEach(() => {
        jest.clearAllMocks();
        req = {};
        process.env.HELP_PDF_SHAREPOINT = ""; // Reset environment variable
    });

    it("should return a success message for the HELP_ICON endpoint", () => {
        const routeHandler = helpIconRouter.stack.find(
            (layer) => layer.route?.path === HELP_ICON_ENDPOINTS.HELP_ICON
        )?.route?.stack[0].handle;

        routeHandler!(req as Request, mockRes, mockNext);

        expect(mockJson).toHaveBeenCalledWith({
            message: "Help icon route is working!"
        });
    });

    it("should return the SharePoint PDF URL when HELP_PDF_SHAREPOINT is set for VIEW endpoint", async () => {
        process.env.HELP_PDF_SHAREPOINT =
            "https://rxsafeway.sharepoint.com/sites";

        const routeHandler = helpIconRouter.stack.find(
            (layer) => layer.route?.path === HELP_ICON_ENDPOINTS.VIEW
        )?.route?.stack[0].handle;

        await routeHandler!(req as Request, mockRes, mockNext);

        expect(mockJson).toHaveBeenCalledWith({
            url: "https://rxsafeway.sharepoint.com/sites",
            message: "Success"
        });
    });
});
