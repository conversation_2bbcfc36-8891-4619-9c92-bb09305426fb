import { Injectable } from "graphql-modules";

export interface EnvVariablesResponse {
    variables: Record<string, any>;
}

@Injectable()
export class EnvVariablesService {
    // Whitelist of allowed environment variables
    private readonly whitelistedEnvVars = new Set<string>([
        // Common application environment variables

        "PHARMA_UPLOAD_DAYS",
        "LINE1_TOOLTIP_TEXT",
        "LINE5_TOOLTIP_TEXT",
        "LINE6_TOOLTIP_TEXT",
        "LINE7_TOOLTIP_TEXT",
        "LINE8_TOOLTIP_TEXT"
        // Add more whitelisted variables as needed
    ]);

    async getEnvVariables(): Promise<EnvVariablesResponse> {
        const variables: Record<string, any> = {};

        // Get all environment variables from process.env
        for (const [key, value] of Object.entries(process.env)) {
            if (value !== undefined && this.isAllowedVariable(key)) {
                // Try to parse numeric values, otherwise keep as string
                const numValue = Number(value);
                variables[key] =
                    !isNaN(numValue) && value !== "" ? numValue : value;
            }
        }

        return {
            variables
        };
    }

    private isAllowedVariable(key: string): boolean {
        // Check if it has the _EXP suffix
        if (key.endsWith("_EXP")) {
            return true; // Allow any variable with _EXP suffix
        }

        // Check if it's in the whitelist
        if (this.whitelistedEnvVars.has(key)) {
            return true;
        }

        // If not whitelisted and doesn't have _EXP suffix, exclude it
        return false;
    }
}

export const EnvVariablesProvider = {
    provide: EnvVariablesService,
    useClass: EnvVariablesService
};
