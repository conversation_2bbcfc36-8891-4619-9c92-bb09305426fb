# Environment Variables GraphQL Query Usage

## Query

```graphql
query GetEnvVariables {
    GetEnvVariables {
        variables
    }
}
```

## Example Response

The query will return only whitelisted environment variables and variables with `_EXP` suffix:

```json
{
    "data": {
        "GetEnvVariables": {
            "variables": {
                "NODE_ENV": "development",
                "PORT": 4001,
                "CONFIG_EXP": "debug_config",
                "API_ENDPOINT_EXP": "https://api.example.com",
                "DEBUG_MODE_EXP": true
            }
        }
    }
}
```

## Features

-   ✅ Returns only whitelisted environment variables
-   ✅ Returns variables with `_EXP` suffix for experimental/debugging purposes
-   ✅ Filters out system variables (Windows, Unix, npm, Git, VS Code, etc.)
-   ✅ Automatically parses numeric values
-   ✅ Returns JSON object format for easy consumption
-   ✅ Handles errors gracefully
-   ✅ Fully tested with comprehensive coverage
-   ✅ Enhanced security with whitelist-based access

## Security Features

-   **Whitelist Protection**: Only explicitly whitelisted variables are exposed
-   **Suffix-based Exposure**: Variables with `_EXP` suffix are automatically exposed
-   **System Variable Filtering**: All system and sensitive variables are filtered out
-   **No Sensitive Data**: Secure properties like API keys and passwords are protected

## Adding Variables to Whitelist

To expose specific environment variables, add them to the whitelist in `src/graphql/modules/envVariables/providers/envVariables.provider.ts`:

```typescript
private readonly whitelistedEnvVars = new Set<string>([
    'NODE_ENV',
    'PORT',
    'DATABASE_URL',
    // Add your whitelisted variables here
]);
```

## Using \_EXP Suffix

For temporary debugging or experimental purposes, suffix any environment variable with `_EXP`:

```bash
# These will be returned by the API
export CONFIG_EXP="debug_config"
export API_ENDPOINT_EXP="https://api.example.com"
export DEBUG_MODE_EXP="true"

# These will NOT be returned (unless whitelisted)
export SECRET_API_KEY="secret123"
export DATABASE_PASSWORD="password123"
```

## System Variables Filtered Out

The following types of system variables are automatically filtered out:

-   Windows system variables (COMPUTERNAME, USERNAME, PATH, etc.)
-   npm-related variables (npm*config*_, npm*package*_, etc.)
-   VS Code variables (VSCODE\_\*, etc.)
-   Git variables (GIT\_\*, etc.)
-   Java variables (JAVA\_\*, etc.)
-   Terminal variables (TERM\_\*, etc.)
-   Locale variables (LC\_\*, LANG, etc.)
-   And many more...

## Testing

All tests are passing:

```bash
npx jest src/graphql/modules/envVariables --verbose
```

This will run tests covering all functionality including:

-   Whitelist-based variable filtering
-   \_EXP suffix variable inclusion
-   System variable exclusion
-   Numeric value parsing
-   Error handling
-   Edge cases
