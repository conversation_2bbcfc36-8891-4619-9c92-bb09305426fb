import { listBlobFiles, checkBlobExists, getBlobClient, findSimilarFileNames } from './blobUtils';
import { pharmacyContainerClient, isConnectionAvailable, getPharmacyFolderPrefix } from '../../pharmaUpload/Connection';

// Mock the Connection module
jest.mock('../../pharmaUpload/Connection', () => ({
    pharmacyContainerClient: {
        listBlobsFlat: jest.fn(),
        getBlobClient: jest.fn(),
    },
    isConnectionAvailable: jest.fn(),
    getPharmacyFolderPrefix: jest.fn(),
}));

// Mock console.error to avoid console output in tests
const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

describe('blobUtils', () => {
    const mockListBlobsFlat = pharmacyContainerClient.listBlobsFlat as jest.MockedFunction<typeof pharmacyContainerClient.listBlobsFlat>;
    const mockGetBlobClient = pharmacyContainerClient.getBlobClient as jest.MockedFunction<typeof pharmacyContainerClient.getBlobClient>;
    const mockIsConnectionAvailable = isConnectionAvailable as jest.MockedFunction<typeof isConnectionAvailable>;
    const mockGetPharmacyFolderPrefix = getPharmacyFolderPrefix as jest.MockedFunction<typeof getPharmacyFolderPrefix>;

    beforeEach(() => {
        jest.clearAllMocks();
        mockIsConnectionAvailable.mockReturnValue(true);
        mockGetPharmacyFolderPrefix.mockReturnValue('pharmacy/');
        consoleErrorSpy.mockClear();
    });

    afterAll(() => {
        consoleErrorSpy.mockRestore();
    });

    describe('listBlobFiles', () => {
        it('should list blob files successfully', async () => {
            const mockBlobs = [
                {
                    name: 'pharmacy/file1.xlsx',
                    properties: { contentLength: 1024, lastModified: new Date('2023-01-01') }
                },
                {
                    name: 'pharmacy/file2.xlsx',
                    properties: { contentLength: 2048, lastModified: new Date('2023-01-02') }
                }
            ];

            // Create an async iterator for the mock
            const asyncIterator = {
                async *[Symbol.asyncIterator]() {
                    for (const blob of mockBlobs) {
                        yield blob;
                    }
                }
            };

            mockListBlobsFlat.mockReturnValue(asyncIterator as any);

            const result = await listBlobFiles();

            expect(result).toHaveLength(2);
            expect(result[0]).toEqual({
                fileName: 'file1.xlsx',
                fullPath: 'pharmacy/file1.xlsx',
                size: 1024,
                lastModified: new Date('2023-01-01')
            });
        });

        it('should handle empty blob list', async () => {
            const asyncIterator = {
                async *[Symbol.asyncIterator]() {
                    // Empty iterator
                }
            };

            mockListBlobsFlat.mockReturnValue(asyncIterator as any);

            const result = await listBlobFiles();

            expect(result).toEqual([]);
            expect(mockListBlobsFlat).toHaveBeenCalledWith({ prefix: 'pharmacy/' });
        });

        it('should handle connection unavailable', async () => {
            mockIsConnectionAvailable.mockReturnValue(false);

            await expect(listBlobFiles()).rejects.toThrow('Azure Blob Storage connection not available');
            expect(mockListBlobsFlat).not.toHaveBeenCalled();
        });

        it('should handle blob listing errors', async () => {
            const originalError = new Error('Blob storage network error');

            // Mock the async iterator to throw an error
            const asyncIterator = {
                async *[Symbol.asyncIterator]() {
                    throw originalError;
                }
            };

            mockListBlobsFlat.mockReturnValue(asyncIterator as any);

            await expect(listBlobFiles()).rejects.toThrow('Failed to list pharmacy files');
            expect(console.error).toHaveBeenCalledWith('Error listing pharmacy files:', originalError);
        });
    });

    describe('checkBlobExists', () => {
        it('should return true when blob exists', async () => {
            const mockBlobClient = {
                exists: jest.fn().mockResolvedValue(true)
            };
            mockGetBlobClient.mockReturnValue(mockBlobClient as any);

            const result = await checkBlobExists('test-file.xlsx');

            expect(result).toBe(true);
            expect(mockGetBlobClient).toHaveBeenCalledWith('pharmacy/test-file.xlsx');
        });

        it('should return false when blob does not exist', async () => {
            const mockBlobClient = {
                exists: jest.fn().mockResolvedValue(false)
            };
            mockGetBlobClient.mockReturnValue(mockBlobClient as any);

            const result = await checkBlobExists('non-existent.xlsx');

            expect(result).toBe(false);
        });

        it('should handle errors and return false', async () => {
            const mockBlobClient = {
                exists: jest.fn().mockRejectedValue(new Error('Network error'))
            };
            mockGetBlobClient.mockReturnValue(mockBlobClient as any);

            const result = await checkBlobExists('error-file.xlsx');

            expect(result).toBe(false);
        });
    });

    describe('getBlobClient', () => {
        it('should return blob client with prefixed path', () => {
            const mockBlobClient = { name: 'test-blob-client' };
            mockGetBlobClient.mockReturnValue(mockBlobClient as any);

            const result = getBlobClient('test.pdf');

            expect(result).toBe(mockBlobClient);
            expect(mockGetBlobClient).toHaveBeenCalledWith('pharmacy/test.pdf');
        });
    });

    describe('findSimilarFileNames', () => {
        it('should find similar file names', async () => {
            const mockBlobs = [
                {
                    name: 'pharmacy/report_2023.xlsx',
                    properties: { contentLength: 1024, lastModified: new Date('2023-01-01') }
                },
                {
                    name: 'pharmacy/report_2024.xlsx',
                    properties: { contentLength: 2048, lastModified: new Date('2023-01-02') }
                },
                {
                    name: 'pharmacy/other_file.pdf',
                    properties: { contentLength: 3072, lastModified: new Date('2023-01-03') }
                }
            ];

            const asyncIterator = {
                async *[Symbol.asyncIterator]() {
                    for (const blob of mockBlobs) {
                        yield blob;
                    }
                }
            };

            mockListBlobsFlat.mockReturnValue(asyncIterator as any);

            const result = await findSimilarFileNames('report');

            expect(result).toHaveLength(2);
            expect(result).toEqual(['report_2023.xlsx', 'report_2024.xlsx']);
        });

        it('should return empty array when no similar files found', async () => {
            const asyncIterator = {
                async *[Symbol.asyncIterator]() {
                    // Empty iterator
                }
            };

            mockListBlobsFlat.mockReturnValue(asyncIterator as any);

            const result = await findSimilarFileNames('nonexistent');

            expect(result).toEqual([]);
        });
    });
});