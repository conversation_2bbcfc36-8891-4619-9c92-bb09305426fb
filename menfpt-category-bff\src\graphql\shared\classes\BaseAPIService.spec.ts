import "reflect-metadata";
import axios from "axios";
import { BaseAPIService } from "./BaseAPI.service";
import * as logApiCallModule from "../utils/logApiCall";

jest.mock("axios");

jest.mock("process", () => ({
    env: {
        CALENDAR_ENDPOINT: "http://localhost:3000"
    }
}));

describe("BaseAPIService", () => {
    let service: BaseAPIService;

    beforeEach(() => {
        service = new BaseAPIService();
    });

    describe("createRequestURL", () => {
        it("should create the correct request URL", () => {
            const url = "calendar/search";
            expect(service.createRequestURL("search")).toBe(
                `${process.env.CALENDAR_ENDPOINT}search`
            );
        });
        it("should create the correct request URL with diffrent end points", () => {
            const url = "fetch/menu-items";
            expect(service.createRequestURL(url)).toBe(
                `${process.env.CALENDAR_ENDPOINT}${url}`
            );
        });
    });

    describe("get", () => {
        it("should call axios.request with the correct method, URL, and payload", async () => {
            const url = "test";
            const body = { body: { key: "value" } };
            const payload = {
                headers: {
                    "Content-Type": "application/json",
                    "bff-trace-id": "",
                    "ui-trace-id": ""
                }
            };
            const options = { header: "value" };
            const spy = jest.spyOn(axios, "request");

            await service.get(url, body, options);

            expect(spy).toHaveBeenCalledWith({
                method: "get",
                url: `${process.env.CALENDAR_ENDPOINT}${url}`,
                headers: payload.headers,
                data: body?.body,
                ...options
            });
        });
    });

    describe("post", () => {
        it("should call axios.request with the correct method, URL, and payload", async () => {
            const url = "test";
            const payload = {
                body: { key: "value" },
                headers: {
                    Authorization: "",
                    "Ocp-Apim-Subscription-Key": undefined,
                    "Content-Type": "application/json"
                }
            };
            const options = { headers: "value" };
            const spy = jest.spyOn(axios, "request");

            await service.post(url, payload, options);

            expect(spy).toHaveBeenCalledWith({
                method: "post",
                url: `${process.env.CALENDAR_ENDPOINT}${url}`,
                data: payload?.body,
                ...options
            });
        });
    });
    describe("delete", () => {
        it("should call axios.request with the correct method, URL, and payload", async () => {
            const url = "test";
            const payload = {
                body: { key: "value" },
                headers: {
                    Authorization: "",
                    "Ocp-Apim-Subscription-Key": undefined,
                    "Content-Type": "application/json"
                }
            };
            const options = { headers: "value" };
            const spy = jest.spyOn(axios, "request");

            await service.delete(url, payload, options);

            expect(spy).toHaveBeenCalledWith({
                method: "delete",
                url: `${process.env.CALENDAR_ENDPOINT}${url}`,
                data: payload?.body,
                ...options
            });
        });
    });
    describe("patch", () => {
        it("should call axios.request with the correct method, URL, and payload", async () => {
            const url = "test";
            const payload = {
                body: { key: "value" },
                headers: {
                    Authorization: "",
                    "Ocp-Apim-Subscription-Key": undefined,
                    "Content-Type": "application/json"
                }
            };
            const options = { headers: "value" };
            const spy = jest.spyOn(axios, "request");

            await service.patch(url, payload, options);

            expect(spy).toHaveBeenCalledWith({
                method: "patch",
                url: `${process.env.CALENDAR_ENDPOINT}${url}`,
                data: payload?.body,
                ...options
            });
        });
    });

    describe("put", () => {
        it("should call axios.request with the correct method, URL, and payload", async () => {
            const url = "test";
            const payload = {
                body: { key: "value" },
                headers: {
                    Authorization: "",
                    "Ocp-Apim-Subscription-Key": undefined,
                    "Content-Type": "application/json"
                }
            };
            const options = { headers: "value" };
            const spy = jest.spyOn(axios, "request");

            await service.put(url, payload, options);

            expect(spy).toHaveBeenCalledWith({
                method: "put",
                url: `${process.env.CALENDAR_ENDPOINT}${url}`,
                data: payload?.body,
                ...options
            });
        });
    });

    describe("createErrorPayload", () => {
        it("should create the correct error payload", () => {
            const error = {
                response: { status: 400, data: { message: "Error message" } },
                code: "SOME_ERROR_CODE",
                name: "SOME_ERROR_NAME",
                message: "Error message",
                config: { url: "http://example.com" }
            };
            const expectedPayload = {
                httpStatus: 400,
                errorCode: "SOME_ERROR_CODE",
                name: "SOME_ERROR_NAME",
                message: "Error message",
                url: "http://example.com"
            };
            expect(service.createErrorPayload(error)).toEqual(expectedPayload);
        });
        it("should create the correct error payload even if the properties not exist on error object", () => {
            const error = {
                response: { data: { message: "Error message" } },
                message: "Error message",
                config: {}
            };
            const expectedPayload = {
                httpStatus: null,
                errorCode: "ERROR_CODE_UNKNOWN",
                name: "ERROR_NAME_UNKNOWN",
                message: "Error message",
                url: "REQUEST_URL_UNKNOWN"
            };
            expect(service.createErrorPayload(error)).toEqual(expectedPayload);
        });
    });
});

describe("BaseAPIService error handling", () => {
    let service1: BaseAPIService;

    beforeEach(() => {
        service1 = new BaseAPIService();
    });
    afterEach(() => {
        jest.restoreAllMocks();
    });
    it("should return domain url not found error", () => {
        jest.spyOn(service1, "createRequestURL").mockImplementationOnce(
            (url: string) => {
                throw new Error(`No domain found for the provided URL: ${url}`);
            }
        );
        expect(() => service1.createRequestURL("hello/world")).toThrow(
            "No domain found for the provided URL: hello/world"
        );
    });
});

describe("BaseAPIService additional coverage", () => {
    let service: BaseAPIService;
    let logApiCallSpy: jest.SpyInstance;
    let axiosRequestSpy: jest.SpyInstance;

    beforeEach(() => {
        service = new BaseAPIService();
        logApiCallSpy = jest
            .spyOn(logApiCallModule, "logApiCall")
            .mockImplementation(() => {});
        axiosRequestSpy = jest.spyOn(axios, "request");
    });
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("getHeader", () => {
        it("should return headers with empty ui-trace-id and bff-trace-id if context is not set", () => {
            const headers = service.getHeader();
            expect(headers["ui-trace-id"]).toBe("");
            expect(headers["bff-trace-id"]).toBe("");
        });
        it("should return headers with ui-trace-id and bff-trace-id if context is set", () => {
            (service as any).context = { context: { uiTraceId: "abc" } };
            const headers = service.getHeader();
            expect(headers["ui-trace-id"]).toBe("abc");
            expect(headers["bff-trace-id"]).not.toBe("");
        });
    });

    describe("apiCallTracker integration", () => {
        it("should call apiCallTracker.track in get method if present in context", async () => {
            const track = jest.fn();
            (service as any).context = {
                context: { apiCallTracker: { track }, uiTraceId: "abc" },
                info: { fieldName: "testField" }
            };
            axiosRequestSpy.mockResolvedValue({ data: {} });
            await service.get("test", { body: {} });
            expect(track).toHaveBeenCalledWith(
                "testField",
                "GET",
                expect.any(String)
            );
        });
    });

    describe("logApiCall integration and error handling", () => {
        it("should call logApiCall with phase ERROR and throw in get method on axios error", async () => {
            const error = new Error("fail");
            axiosRequestSpy.mockRejectedValue(error);
            try {
                await service.get("test", { body: {} });
            } catch (e) {
                expect(logApiCallSpy).toHaveBeenCalledWith(
                    expect.objectContaining({ phase: "ERROR" })
                );
                expect(e).toBe(error);
            }
        });
    });

    describe("createRequestURL edge cases", () => {
     
        it("should map to correct endpoint for various API_END_POINTS", () => {
            // Simulate process.env with multiple endpoints
            process.env.CALENDAR_ENDPOINT =
                "http://localhost:3000/menfpt/calendar/";
            process.env.FORECAST_ENDPOINT =
                "http://localhost:3000/menfpt/forecast/";
            (service as any).getServiceEndPoints = () => [
                { CALENDAR_ENDPOINT: process.env.CALENDAR_ENDPOINT },
                { FORECAST_ENDPOINT: process.env.FORECAST_ENDPOINT }
            ];
            // Should map to forecast endpoint
            const url = service.createRequestURL("forecast/search");
            expect(url).toContain("forecast");
        });
    });

    describe("put and delete error handling", () => {
        it("should call logApiCall with phase ERROR and throw in put method on axios error", async () => {
            const error = new Error("fail-put");
            axiosRequestSpy.mockRejectedValue(error);
            try {
                await service.put("test", { body: {} });
            } catch (e) {
                expect(logApiCallSpy).toHaveBeenCalledWith(
                    expect.objectContaining({ phase: "ERROR", method: "PUT" })
                );
                expect(e).toBe(error);
            }
        });
        it("should call logApiCall with phase ERROR and throw in delete method on axios error", async () => {
            const error = new Error("fail-delete");
            axiosRequestSpy.mockRejectedValue(error);
            try {
                await service.delete("test", { body: {} });
            } catch (e) {
                expect(logApiCallSpy).toHaveBeenCalledWith(
                    expect.objectContaining({
                        phase: "ERROR",
                        method: "DELETE"
                    })
                );
                expect(e).toBe(error);
            }
        });
    });
});
