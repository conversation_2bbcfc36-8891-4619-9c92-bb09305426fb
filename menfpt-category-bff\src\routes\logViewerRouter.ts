import express from "express";
import * as path from "path";
import * as fs from "fs";

const router = express.Router();

router.get("/api/logs", (req: express.Request, res: express.Response) => {
    const logFilePath = path.resolve(__dirname, "../../../bff-logs.log");
    if (fs.existsSync(logFilePath)) {
        res.sendFile(logFilePath);
    } else {
        res.status(404).send(
            "Log file not found. Logging may not be enabled for this environment."
        );
    }
});

router.get("/", (req: express.Request, res: express.Response) => {
    const htmlPath = path.resolve(__dirname, "../assets/log-viewer.html");
    res.sendFile(htmlPath);
});

export default router;
