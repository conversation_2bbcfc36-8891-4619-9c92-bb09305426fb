export function getPacificDate() {
    const now = new Date();
    const pacificOffset = -new Date().toLocaleString('en-US', { timeZone: 'America/Los_Angeles', hour12: false, hour: '2-digit' });
    const pacificNow = new Date(
        now.toLocaleString('en-US', { timeZone: 'America/Los_Angeles' })
    );
    return pacificNow;
}

//PST cutoff time for lockout and certification
export const LOCKOUT_CERTIFY_CUTTOFF = process.env.LOCKOUT_CERTIFY_CUTTOFF || 'T10:00:00.000';