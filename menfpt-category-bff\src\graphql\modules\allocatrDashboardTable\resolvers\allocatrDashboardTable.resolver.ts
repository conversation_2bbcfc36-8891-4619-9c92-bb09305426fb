import { AllocatrDashboardReq } from "src/graphql/__generated__/gql-ts-types";
import { ContextValue } from "../../../context";
import { AllocatrDataMappingServiceProvider, CalendarServiceProvider } from "../providers/allocatrDashboardTable.provider";
import {
  aggregationLevelFields,
  AggregationLevels,
  AggregationRanges,
  getBaseAggregatedData,
  rollUpDataFromWeek,
  sumDataByAggregationRange
} from "./dashboardTableHelper";
import { LOCKOUT_CERTIFY_CUTTOFF } from "../../../Util";

interface AggregateData {
  id?: string | number;
  periodNumber?: number;
  weekNumber?: number;
  quarterNumber?: number;
  line1Projection?: number;
  lastYear?: number;
  actual?: number;
  forecast?: number;
  idPercentage?: number;
  vsLY?: { value?: number; percentage?: number };
  vsProjection?: { value?: number; percentage?: number };
  bookGrossProfit?:
  | number
  | {
    projection?: number;
    actual?: number;
    forecast?: number;
    vsProjection?: number;
  };
  markdown?:
  | number
  | {
    projection?: number;
    actual?: number;
    forecast?: number;
    vsProjection?: number;
  };
  shrink?:
  | number
  | {
    projection?: number;
    actual?: number;
    forecast?: number;
    vsProjection?: number;
  };
  line5?: {
    actual?: number;
    percentActual?: number;
    fcst?: number;
    percentFcst?: number;
    vsProjection?: number;
    percentVsProjection?: number;
  };
  // Add line6, line7, line8 as needed
  aggregationLevel?: string;
  [key: string]: any;
}

export const AllocatrDashboardTableResolver = async (
  parent: any,
  args: { allocatrDashboardReq: AllocatrDashboardReq },
  context: ContextValue
): Promise<any> => {
  const AllocatrDataMappingService = context?.injector?.get(
    AllocatrDataMappingServiceProvider.provide
  );
  const calenderService = context?.injector?.get(CalendarServiceProvider.provide);
  const calendarData =
    (await calenderService?.getCalendarData({
      fiscalQuarterNumber: args.allocatrDashboardReq.quarterNbr
    })) || [];
  // create a map for every fiscalWeekNumber get its fiscalPeriodNumber
  const weekToPeriodMap = new Map<number, number>();
  calendarData.forEach((item: any) => {
    if (item.fiscalWeekNumber && item.fiscalPeriodNumber) {
      weekToPeriodMap.set(item.fiscalWeekNumber, item.fiscalPeriodNumber);
    }
  });
  const filteredWeeksLength = args.allocatrDashboardReq?.filteredWeekNumbers &&
    args.allocatrDashboardReq?.filteredWeekNumbers?.length > 0;
  const allWeeksOfQuarter: number[] = filteredWeeksLength
    ? (args.allocatrDashboardReq.filteredWeekNumbers as number[] ?? [])
    : Array.from(weekToPeriodMap.keys()) || [];

  // const IDENTICAL = "IDENTICAL";
  // const KEEPER = "KEEPER";
  const currentFiscalYearNbr =
    args.allocatrDashboardReq?.currentFiscalYearNbr;

  function getPreviousYearQuarter(currentQuarterNbr: number): number {
    const year = Math.floor(currentQuarterNbr / 100);
    const quarter = currentQuarterNbr % 100;
    return (year - 1) * 100 + quarter;
  }

  function getNextYearQuarter(currentQuarterNbr: number): number {
    const year = Math.floor(currentQuarterNbr / 100);
    const quarter = currentQuarterNbr % 100;
    return (year + 1) * 100 + quarter;
  }

  function getPreviousYearNumbers(allNumbersOfQuarter: number[]): number[] {
    if (!Array.isArray(allNumbersOfQuarter)) return [];
    return allNumbersOfQuarter.map(id => {
      // Extract year and week parts
      const year = Math.floor(id / 100);
      const previousId = id % 100;
      // Create previous year previousId number
      return (year - 1) * 100 + previousId;
    });
  }

  function filterOutWeeks(weekNumbers: number[], periodNumbers: number[]) {
    return weekNumbers
      .filter((week: number) => {
        const period = weekToPeriodMap.get(week);
        return period && periodNumbers.includes(period);
      })
  }

  const requestPayload = (
    args: Record<string, any>,
    aggregationLevel: AggregationLevels,
    allWeeksOfQuarter: number[]
  ) => {
    const { quarterNbr, type, periodNumbers, ...rest } = args;
    const previousYearQuarterNbr = getPreviousYearQuarter(Number(quarterNbr));
    const payload: any = {
      ...rest,
      fiscalPeriodNbrs: aggregationLevel === AggregationLevels.ACTUALS
        ? [...periodNumbers, ...getPreviousYearNumbers(periodNumbers)]
        : periodNumbers,
      fiscalQuarterNbrs:
        aggregationLevel === AggregationLevels.ACTUALS
          ? [quarterNbr, previousYearQuarterNbr]
          : [quarterNbr],
      include_period: false,
      include_weeks: true,
      include_quarter: false,
      include_qtd: false
    };
    if (args.latestReleaseWeek !== undefined && args.weekNumbers && aggregationLevel !== AggregationLevels.ACTUALS) {
      payload.snapshot_timestamp = transformWeek(args.latestReleaseWeek.value);
    }
    if (args.latestReleaseWeek !== undefined && aggregationLevel === AggregationLevels.ACTUALS) {
      const currentQuarterWeeks = args?.weekNumbers?.length > 0 ? args.weekNumbers : allWeeksOfQuarter;
      const previousYearWeekNumbers = getPreviousYearNumbers(filterOutWeeks(allWeeksOfQuarter, periodNumbers));
      const filteredCurrentQuarterWeeks = filterOutWeeks(currentQuarterWeeks, periodNumbers);
      payload.fiscalWeekNbrs = [...filteredCurrentQuarterWeeks, ...previousYearWeekNumbers];
    }
    delete payload.latestReleaseWeek;
    delete payload.weekNumbers;
    return payload;
  };

  const transformWeek = (week: string | number) => {
    if (typeof week === "string" && /^\d{2}\/\d{2}\/\d{4}$/.test(week)) {
      const [month, day, year] = week.split("/");
      const PSTdate = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}` + LOCKOUT_CERTIFY_CUTTOFF;
      const utcDate = new Date(PSTdate).toISOString();
      return utcDate;
    } else if (typeof week === "string" && week.includes("T")) {
      const utcDate = new Date(week).toISOString();
      return utcDate;
    }
  }

  const constructKey = (item: any, value: any) => {
    const aggregationLevel = item.aggregationLevel;
    return `${aggregationLevel}#${item.deptId}#${value}`;
  };

  const createActualObject = (key: string, value: any) => {
    const splitKey = key.split("#");
    const aggregationLevel = splitKey[0];
    const field = aggregationLevelFields[aggregationLevel as keyof typeof aggregationLevelFields];
    const data = {
      aggregationLevel,
      deptId: splitKey[1],
      [field]: getNextYearQuarter(Number(splitKey[2])),
      totalLine1Sales: 0,
      totalLine4CostOfSales: 0,
      totalLine5BookGrossProfit: 0,
      totalLine5MarkDowns: 0,
      totalLine5Shrink: 0,
      totalLine5RealGrossProfit: 0,
      totalLine6SuppliesPackaging: 0,
      totalLine7RetailAllowances: 0,
      totalLine8RealGrossProfit: 0,
      lastyearTotalSales: value
    }
    return data;
  }

  // Fetch KEEPER data for each type
  const [forecastAggregate = [], projection = [], actuals = []] = await Promise.all([
    AllocatrDataMappingService?.getForecastDataForDashboard(
      requestPayload(
        args.allocatrDashboardReq,
        AggregationLevels.FORECAST,
        allWeeksOfQuarter
      )
    ),
    AllocatrDataMappingService?.getProjectionDataForDashboard(
      requestPayload(
        args.allocatrDashboardReq,
        AggregationLevels.PROJECTION,
        allWeeksOfQuarter
      )
    ),
    AllocatrDataMappingService?.getActualDataForDashboard(
      requestPayload(
        args.allocatrDashboardReq,
        AggregationLevels.ACTUALS,
        allWeeksOfQuarter
      )
    )
  ]);

  const updatedForecastAggregate = forecastAggregate.filter((item: any) => item.forecastType === "FA");

  const lastYearActuals = new Map(
    actuals
      .filter((i: any) => (Number(i.fiscalYearNbr) == currentFiscalYearNbr - 1))
      .map((item: any) => {
        const field = item[aggregationLevelFields[item.aggregationLevel as keyof typeof aggregationLevelFields]];
        const key = constructKey(item, field);
        return [key, item.totalLine1Sales];
      })
  );

  const actualsWithLYSales = actuals
    .filter((i: any) => Number(i.fiscalYearNbr) == currentFiscalYearNbr)
    .map((item: any) => {
      const field = item[aggregationLevelFields[item.aggregationLevel as keyof typeof aggregationLevelFields]];
      const key = constructKey(item, getPreviousYearQuarter(field));
      const lastYearLine1 = lastYearActuals.get(key);
      lastYearActuals.delete(key);
      return {
        ...item,
        lastyearTotalSales: lastYearLine1 || 0
      };
    });

  // for all the remaining lastYearActuals, create an empty actual object
  // this scenario happens when there is no actuals from api.
  lastYearActuals.forEach((value, key) => {
    actualsWithLYSales.push(createActualObject(key, value));
  });

  const deptToActuals = new Map<string, boolean>();
  actualsWithLYSales.forEach((item: any) => {
    const key = constructKey(item, item[aggregationLevelFields[item.aggregationLevel as keyof typeof aggregationLevelFields]]);
    if (item.totalLine1Sales !== 0) {
      deptToActuals.set(key, true);
    }
  });

  const allDeptIds = args.allocatrDashboardReq.deptIds || [];
  const quarterNbr = args.allocatrDashboardReq.quarterNbr;

  const actOrFcstRollup = [
    ...rollUpDataFromWeek(actualsWithLYSales, updatedForecastAggregate, AggregationRanges.PERIOD, allDeptIds, allWeeksOfQuarter, weekToPeriodMap),
    ...rollUpDataFromWeek(actualsWithLYSales, updatedForecastAggregate, AggregationRanges.QUARTER, allDeptIds, allWeeksOfQuarter, quarterNbr)
  ];

  const projectionRollup = [
    ...rollUpDataFromWeek(projection, [], AggregationRanges.PERIOD, allDeptIds, allWeeksOfQuarter, weekToPeriodMap),
    ...rollUpDataFromWeek(projection, [], AggregationRanges.QUARTER, allDeptIds, allWeeksOfQuarter, quarterNbr)
  ];

  const actualsWithTotals = [...actualsWithLYSales, ...actOrFcstRollup, ...sumDataByAggregationRange([...actualsWithLYSales, ...actOrFcstRollup])];
  const projectionWithTotals = [...projection, ...projectionRollup, ...sumDataByAggregationRange([...projection, ...projectionRollup])];
  const forecastWithTotals = [...updatedForecastAggregate, ...actOrFcstRollup, ...sumDataByAggregationRange([...updatedForecastAggregate, ...actOrFcstRollup], true, "", deptToActuals)];


  // Helper to get all unique periodNumbers (from fiscalPeriodNbr) for a department
  function getAllPeriodNumbersForDept() {
    const periodNumbers = args.allocatrDashboardReq.periodNumbers || [];
    // Sort period numbers in ascending order to ensure consistent display order
    // Filter out null values and sort the remaining numbers
    return periodNumbers
      .filter((period): period is number => period !== null && period !== undefined)
      .sort((a: number, b: number) => a - b);
  }
  // Helper to get all unique weekNumbers (from fiscalQuater) for a department

  // Helper to get all unique periodNumbers (from fiscalPeriodNbr) for a department
  function getAllWeeksNumbersForDept() {
    return allWeeksOfQuarter;
  }

  function findData(
    dataset: any[],
    deptId: string | number,
    id: number,
    type: AggregationRanges
  ) {
    switch (type) {
      case AggregationRanges.WEEK:
        return (
          dataset?.find(
            (d: any) =>
              d.deptId === deptId && d.fiscalWeekNbr === id && d.aggregationLevel === AggregationRanges.WEEK
          ) || {}
        );
      case AggregationRanges.PERIOD:
        return (
          dataset?.find(
            (d: any) =>
              d.deptId === deptId && d.fiscalPeriodNbr === id && d.aggregationLevel === AggregationRanges.PERIOD
          ) || {}
        );
      case AggregationRanges.QUARTER:
        return (
          dataset?.find(
            (d: any) =>
              d.deptId === deptId && d.fiscalQuarterNbr === id && d.aggregationLevel === AggregationRanges.QUARTER
          ) || {}
        );
      default:
        throw new Error(`Unsupported aggregation range: ${type}`);
    }
  }

  function getRangeData(
    deptId: string | number,
    id: number,
    range: AggregationRanges
  ) {
    const proj = findData(projectionWithTotals, deptId, id, range);
    const fcst = findData(forecastWithTotals, deptId, id, range);
    const actual = findData(actualsWithTotals, deptId, id, range);
    return getBaseAggregatedData(
      proj,
      actual,
      fcst,
      {},
      {},
      {},
      range,
      id,
      weekToPeriodMap,
      deptToActuals
    );
  }

  const getDataforDept = (deptId: string | null) => {
    if (deptId) {
      const periodNumbers = getAllPeriodNumbersForDept();
      const allWeekNumbers = getAllWeeksNumbersForDept();
      const quarterNbr = args.allocatrDashboardReq?.quarterNbr;

      const weeks: any[] = allWeekNumbers.map((fiscalWeekNbr: any) => {
        return getRangeData(
          deptId,
          fiscalWeekNbr,
          AggregationRanges.WEEK
        );
      });

      const periods: any[] = periodNumbers.map((periodNumber: any) => {
        return getRangeData(
          deptId,
          periodNumber,
          AggregationRanges.PERIOD
        );
      });

      const quarter: any = getRangeData(
        deptId,
        quarterNbr,
        AggregationRanges.QUARTER
      );

      // Build the department object with id, name, and summary fields at root
      const deptObj = {
        id: deptId,
        name: "",
        quarter,
        weeks,
        periods
      };
      return deptObj;
    }
  }
  const resultForAllDepts = allDeptIds.length > 1 ? [getDataforDept("Total")] : [];
  allDeptIds.forEach((deptId) => {
    resultForAllDepts.push(getDataforDept(deptId));
  });
  return { allocatrDashboardTableData: resultForAllDepts };
};
