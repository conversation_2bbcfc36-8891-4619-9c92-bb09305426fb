import { generateBffDownloadUrl, createExpiryDate } from './urlUtils';

describe('urlUtils', () => {
    describe('generateBffDownloadUrl', () => {
        const originalEnv = process.env;

        beforeEach(() => {
            jest.resetModules();
            process.env = { ...originalEnv };
        });

        afterAll(() => {
            process.env = originalEnv;
        });

        it('should generate BFF download URL with base URL from environment', () => {
            process.env.BFF_BASE_URL = 'https://api.example.com';

            const result = generateBffDownloadUrl('test file.xlsx');

            expect(result).toBe('https://api.example.com/menfpt-category-bff/api/pharmacy/download/test%20file.xlsx');
        });

        it('should handle special characters in filename', () => {
            process.env.BFF_BASE_URL = 'https://api.example.com';

            const result = generateBffDownloadUrl('file with spaces & symbols.xlsx');

            expect(result).toBe('https://api.example.com/menfpt-category-bff/api/pharmacy/download/file%20with%20spaces%20%26%20symbols.xlsx');
        });

        it('should use empty base URL when environment variable is not set', () => {
            delete process.env.BFF_BASE_URL;

            const result = generateBffDownloadUrl('test.xlsx');

            expect(result).toBe('/menfpt-category-bff/api/pharmacy/download/test.xlsx');
        });

        it('should handle empty base URL environment variable', () => {
            process.env.BFF_BASE_URL = '';

            const result = generateBffDownloadUrl('test.xlsx');

            expect(result).toBe('/menfpt-category-bff/api/pharmacy/download/test.xlsx');
        });
    });

    describe('createExpiryDate', () => {
        beforeEach(() => {
            // Mock the current time
            jest.useFakeTimers();
            jest.setSystemTime(new Date('2025-08-06T10:00:00Z'));
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        it('should create expiry date by adding minutes to current time', () => {
            const result = createExpiryDate(60);

            const expected = new Date('2025-08-06T11:00:00Z');
            expect(result).toEqual(expected);
        });

        it('should handle different expiry durations', () => {
            const result30 = createExpiryDate(30);
            const result120 = createExpiryDate(120);

            expect(result30).toEqual(new Date('2025-08-06T10:30:00Z'));
            expect(result120).toEqual(new Date('2025-08-06T12:00:00Z'));
        });

        it('should handle zero minutes', () => {
            const result = createExpiryDate(0);

            expect(result).toEqual(new Date('2025-08-06T10:00:00Z'));
        });

        it('should handle negative minutes', () => {
            const result = createExpiryDate(-30);

            expect(result).toEqual(new Date('2025-08-06T09:30:00Z'));
        });
    });
});
