import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";

import { createModule } from "graphql-modules";
import { UserInfoProvider } from "./providers/userInfo.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));

const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const UserInfoModule = createModule({
    id: "UserInfo",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [UserInfoProvider]
});

export default UserInfoModule;
