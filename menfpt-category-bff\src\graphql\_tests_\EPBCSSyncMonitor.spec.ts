import "reflect-metadata";
import { 
    formatDateString,
    getWeekNumberOfDate,
    getDefaultWeeks,
    getWeeksForFiscalPeriod,
    checkJobRunAtTime,
    parseSessionDateTime,
    getPreviousWeekSync,
} from "../modules/EPBCSSyncMonitorGraphQL/helpers/EPBCSSyncMonitorHelper";
import {
    /* ...existing imports... */
    generateSyncSessionsForPeriod
} from "../modules/EPBCSSyncMonitorGraphQL/resolvers/EPBCSSyncMonitor.resolver";
import { jest } from "@jest/globals";

describe("EPBCSSyncMonitor Helper Functions", () => {
    beforeAll(() => {
        jest.spyOn(console, 'error').mockImplementation(() => {});
    });
    
    describe("formatDateString", () => {
        it("should format date as MM/DD/YYYY", () => {
            const date = new Date(2024, 5, 7); // June 7, 2024
            expect(formatDateString(date)).toBe("06/07/2024");
        });
        it("should pad single digit months and days", () => {
            const date = new Date(2024, 0, 5); // Jan 5, 2024
            expect(formatDateString(date)).toBe("01/05/2024");
        });
        it("should handle invalid date", () => {
            // @ts-expect-error
            expect(() => formatDateString("invalid")).toThrow();
        });
    });

    describe("getWeekNumberOfDate", () => {
        it("should return correct week number for a date", () => {
            const date = new Date(2024, 0, 1); // Jan 1, 2024
            expect(getWeekNumberOfDate(date)).toBeGreaterThan(0);
        });
        it("should return same week number for same week", () => {
            const date1 = new Date(2024, 5, 3);
            const date2 = new Date(2024, 5, 5);
            expect(getWeekNumberOfDate(date1)).toBe(getWeekNumberOfDate(date2));
        });
        it("should handle invalid date", () => {
            // @ts-expect-error
            expect(() => getWeekNumberOfDate("invalid")).toThrow();
        });
    });

    describe("getDefaultWeeks", () => {
        it("should return 4 weeks by default", () => {
            const weeks = getDefaultWeeks();
            expect(weeks).toBeDefined();
            expect(weeks!.length).toBe(4);
            expect(weeks![0]).toHaveProperty("weekNumber");
            expect(weeks![0]).toHaveProperty("weekStartDate");
            expect(weeks![0]).toHaveProperty("weekEndDate");
        });

        it("should return specified number of weeks", () => {
            const weeks = getDefaultWeeks(2);
            expect(weeks).toBeDefined();
            expect(weeks!.length).toBe(2);
        });

        it("should return empty array for 0 weeks", () => {
            const weeks = getDefaultWeeks(0);
            expect(weeks).toBeDefined();
            expect(weeks!.length).toBe(0);
        });

        it("should handle negative weeks", () => {
            const weeks = getDefaultWeeks(-1);
            expect(weeks).toBeDefined();
            expect(weeks!.length).toBe(0);
        });
    });

    describe("getWeeksForFiscalPeriod", () => {
        it("should return weeks for a valid fiscal period", () => {
            const weeks = getWeeksForFiscalPeriod("06/01/2024", "06/30/2024");
            expect(weeks).toBeDefined();
            expect(Array.isArray(weeks)).toBe(true);
            expect(weeks!.length).toBeGreaterThan(0);
            expect(weeks![0]).toHaveProperty("weekNumber");
        });

        it("should fallback to default weeks for invalid dates", () => {
            const weeks = getWeeksForFiscalPeriod("-", "-");
            expect(weeks).toBeDefined();
            expect(weeks!.length).toBe(3);
        });

        it("should return empty array if start is after end", () => {
            const weeks = getWeeksForFiscalPeriod("07/01/2024", "06/01/2024");
            expect(weeks).toBeDefined();
            expect(Array.isArray(weeks)).toBe(true);
            expect(weeks!.length).toBe(0);
        });

        it("should handle invalid date formats", () => {
            const weeks = getWeeksForFiscalPeriod("invalid", "invalid");
            expect(weeks).toBeDefined();
            expect(Array.isArray(weeks)).toBe(true);
        });
    });

    describe("checkJobRunAtTime", () => {

        it("should return sync_enabled false if no run matches", () => {
            const runs = [
                { start_time: "06/07/2024, 09:00 AM", status: "SUCCESS", life_cycle_state: "TERMINATED" }
            ];
            const result = checkJobRunAtTime(runs, "06/07/2024", "10:00 AM");
            expect(result.sync_enabled).toBe(false);
        });

        it("should handle empty runs array", () => {
            const result = checkJobRunAtTime([], "06/07/2024", "10:00 AM");
            expect(result.sync_enabled).toBe(false);
        });

        it("should handle missing fields in runs", () => {
            const runs = [
                { status: "SUCCESS", life_cycle_state: "TERMINATED" }
            ];
            const result = checkJobRunAtTime(runs as any, "06/07/2024", "10:00 AM");
            expect(result.sync_enabled).toBe(false);
        });
    });

    describe("parseSessionDateTime", () => {
        it("should parse session with date and time", () => {
            const session = { date: "06/07/2024", time: "10:00 AM" };
            const dt = parseSessionDateTime(session);
            expect(dt).toBeInstanceOf(Date);
            expect(dt?.getFullYear()).toBe(2024);
        });
        it("should return null for invalid session", () => {
            expect(parseSessionDateTime({})).toBeNull();
        });
        it("should return null for missing date or time", () => {
            expect(parseSessionDateTime({ date: "06/07/2024" })).toBeNull();
            expect(parseSessionDateTime({ time: "10:00 AM" })).toBeNull();
        });
        it("should return null for invalid date/time format", () => {
            expect(parseSessionDateTime({ date: "invalid", time: "invalid" })).toBeNull();
        });
    });

    describe("getPreviousWeekSync", () => {
        it("should return null if no runs", () => {
            expect(getPreviousWeekSync([])).toBeNull();
        });
        it("should return previous week run if exists", () => {
            const today = new Date();
            const prevMonday = new Date(today);
            prevMonday.setDate(today.getDate() - today.getDay() - 6);
            const prevMondayStr = formatDateString(prevMonday);
            const runs = [
                { start_time: `${prevMondayStr}, 10:00 AM`, status: "SUCCESS", life_cycle_state: "TERMINATED" }
            ];
            type PreviousWeekSyncResult = { date: string; [key: string]: any } | null;
            const result = getPreviousWeekSync(runs) as PreviousWeekSyncResult;
            expect(result).toBeTruthy();
            expect(result?.date).toBe(prevMondayStr);
        });
        it("should return null if no previous week run exists", () => {
            const runs = [
                { start_time: "06/01/2024, 10:00 AM", status: "SUCCESS", life_cycle_state: "TERMINATED" }
            ];
            expect(getPreviousWeekSync(runs)).toBeNull();
        });
        it("should handle runs with missing start_time", () => {
            const runs = [
                { status: "SUCCESS", life_cycle_state: "TERMINATED" }
            ];
            expect(getPreviousWeekSync(runs as any)).toBeNull();
        });
    });

    describe("generateSyncSessionsForPeriod", () => {
     
        it("should return empty array if no runs and invalid period", () => {
            const sessions = generateSyncSessionsForPeriod([], "-", "-");
            expect(Array.isArray(sessions)).toBe(true);
        });
        it("should handle empty runs and valid period", () => {
            const sessions = generateSyncSessionsForPeriod([], "06/01/2024", "06/30/2024");
            expect(Array.isArray(sessions)).toBe(true);
        });
    });

    describe("getWeekNumberOfDate", () => {
        it("should return correct week number for a date", () => {
            const date = new Date(2024, 0, 1); // Jan 1, 2024
            expect(getWeekNumberOfDate(date)).toBeGreaterThan(0);
        });
    });

    describe("getDefaultWeeks", () => {
        it("should return 4 weeks by default", () => {
            const weeks = getDefaultWeeks();
            expect(weeks).toHaveLength(4);
            expect(weeks![0]).toHaveProperty("weekNumber");
            expect(weeks[0]).toHaveProperty("weekStartDate");
            expect(weeks[0]).toHaveProperty("weekEndDate");
        });

        it("should return specified number of weeks", () => {
            const weeks = getDefaultWeeks(2);
            expect(weeks).toHaveLength(2);
        });
    });

    describe("getWeeksForFiscalPeriod", () => {
        it("should return weeks for a valid fiscal period", () => {
            const weeks = getWeeksForFiscalPeriod("06/01/2024", "06/30/2024");
            expect(Array.isArray(weeks)).toBe(true);
            expect(weeks!.length).toBeGreaterThan(0);
            expect(weeks![0]).toHaveProperty("weekNumber");
        });

        it("should fallback to default weeks for invalid dates", () => {
            const weeks = getWeeksForFiscalPeriod("-", "-");
            expect(weeks).toBeDefined();
            expect(weeks!.length).toBe(3);
        });
    });

    describe("checkJobRunAtTime", () => {
  
        it("should return sync_enabled false if no run matches", () => {
            const runs = [
                { start_time: "06/07/2024, 09:00 AM", status: "SUCCESS", life_cycle_state: "TERMINATED" }
            ];
            const result = checkJobRunAtTime(runs, "06/07/2024", "10:00 AM");
            expect(result.sync_enabled).toBe(false);
        });
    });

    describe("parseSessionDateTime", () => {
        it("should parse session with date and time", () => {
            const session = { date: "06/07/2024", time: "10:00 AM" };
            const dt = parseSessionDateTime(session);
            expect(dt).toBeInstanceOf(Date);
            expect(dt?.getFullYear()).toBe(2024);
        });
        it("should return null for invalid session", () => {
            expect(parseSessionDateTime({})).toBeNull();
        });
    });

    describe("getPreviousWeekSync", () => {
        it("should return null if no runs", () => {
            expect(getPreviousWeekSync([])).toBeNull();
        });
        it("should return previous week run if exists", () => {
            const today = new Date();
            const prevMonday = new Date(today);
            prevMonday.setDate(today.getDate() - today.getDay() - 6);
            const prevMondayStr = formatDateString(prevMonday);
            const runs = [
                { start_time: `${prevMondayStr}, 10:00 AM`, status: "SUCCESS", life_cycle_state: "TERMINATED" }
            ];
            type PreviousWeekSyncResult = { date: string; [key: string]: any } | null;
            const result = getPreviousWeekSync(runs) as PreviousWeekSyncResult;
            expect(result).toBeTruthy();
            expect(result?.date).toBe(prevMondayStr);
        });
    });

    describe("generateSyncSessionsForPeriod", () => {
       
        it("should return empty array if period is invalid", () => {
            const sessions = generateSyncSessionsForPeriod([], "invalid", "invalid");
            expect(Array.isArray(sessions)).toBe(true);
            expect(sessions.length).toBeGreaterThanOrEqual(0);
        });

        it("should handle runs with missing start_time", () => {
            const runs = [
            { status: "SUCCESS", life_cycle_state: "TERMINATED" }
            ];
            const sessions = generateSyncSessionsForPeriod(runs as any, "06/01/2024", "06/30/2024");
            expect(Array.isArray(sessions)).toBe(true);
        });

        it("should return empty array if both runs and period are invalid", () => {
            const sessions = generateSyncSessionsForPeriod(undefined as any, undefined as any, undefined as any);
            expect(Array.isArray(sessions)).toBe(true);
            expect(sessions.length).toBeGreaterThanOrEqual(0);
        });

        it("should handle sessions with missing status", () => {
            const runs = [
            { start_time: "06/09/2024, 10:00 AM", life_cycle_state: "TERMINATED" }
            ];
            const sessions = generateSyncSessionsForPeriod(runs as any, "06/01/2024", "06/30/2024");
            expect(Array.isArray(sessions)).toBe(true);
        });

        it("should handle sessions with missing life_cycle_state", () => {
            const runs = [
            { start_time: "06/10/2024, 10:00 AM", status: "SUCCESS" }
            ];
            const sessions = generateSyncSessionsForPeriod(runs as any, "06/01/2024", "06/30/2024");
            expect(Array.isArray(sessions)).toBe(true);
        });

        it("should handle sessions with extra fields", () => {
            const runs = [
            { start_time: "06/11/2024, 10:00 AM", status: "SUCCESS", life_cycle_state: "TERMINATED", extra: "field" }
            ];
            const sessions = generateSyncSessionsForPeriod(runs as any, "06/01/2024", "06/30/2024");
            expect(Array.isArray(sessions)).toBe(true);
        });
        });
    function expect(actual: any) {
        return {
            toBe(expected: any) {
                if (actual !== expected) {
                    throw new Error(`Expected ${actual} to be ${expected}`);
                }
            },
            toBeGreaterThan(expected: number) {
                if (!(actual > expected)) {
                    throw new Error(`Expected ${actual} to be greater than ${expected}`);
                }
            },
            toBeGreaterThanOrEqual(expected: number) {
                if (!(actual >= expected)) {
                    throw new Error(`Expected ${actual} to be greater than or equal to ${expected}`);
                }
            },
            toBeDefined() {
                if (actual === undefined) {
                    throw new Error(`Expected value to be defined, but received undefined`);
                }
            },
            toBeTruthy() {
                if (!actual) {
                    throw new Error(`Expected value to be truthy, but received ${actual}`);
                }
            },
            toBeFalsy() {
                if (actual) {
                    throw new Error(`Expected value to be falsy, but received ${actual}`);
                }
            },
            toHaveLength(length: number) {
                if (!actual || actual.length !== length) {
                    throw new Error(`Expected length ${length}, but got ${actual?.length}`);
                }
            },
            toHaveProperty(prop: string) {
                if (!actual || !(prop in actual)) {
                    throw new Error(`Expected object to have property '${prop}'`);
                }
            },
            toMatch(regexp: RegExp) {
                if (typeof actual !== "string" || !regexp.test(actual)) {
                    throw new Error(`Expected string '${actual}' to match ${regexp}`);
                }
            },
            toThrow() {
                let threw = false;
                try {
                    actual();
                } catch {
                    threw = true;
                }
                if (!threw) {
                    throw new Error(`Expected function to throw`);
                }
            },
            toBeInstanceOf(ctor: Function) {
                if (!(actual instanceof ctor)) {
                    throw new Error(`Expected value to be instance of ${ctor.name}`);
                }
            },
            toBeNull() {
                if (actual !== null) {
                    throw new Error(`Expected value to be null, but got ${actual}`);
                }
            },
            toBeNaN() {
                if (!Number.isNaN(actual)) {
                    throw new Error(`Expected value to be NaN, but got ${actual}`);
                }
            },
            some(predicate: (item: any) => boolean) {
                if (!Array.isArray(actual) || !actual.some(predicate)) {
                    throw new Error(`Expected some item in array to match predicate`);
                }
            },
            not: {
                toThrow() {
                    let threw = false;
                    try {
                        actual();
                    } catch {
                        threw = true;
                    }
                    if (threw) {
                        throw new Error(`Expected function not to throw`);
                    }
                }
            }
        };
    }
});