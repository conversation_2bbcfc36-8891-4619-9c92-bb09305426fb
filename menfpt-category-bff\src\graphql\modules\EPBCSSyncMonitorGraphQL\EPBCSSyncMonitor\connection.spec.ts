import axios from "axios";
import * as connection from "./connection";

jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe("EPBCSSyncMonitor connection utilities", () => {
    // Setup environment variables
    beforeAll(() => {
        process.env.DATABRICKS_TENANT_ID = "test-tenant";
        process.env.DATABRICKS_CLIENT_ID = "test-client";
        process.env.DATABRICKS_CLIENT_SECRET = "test-secret";
        process.env.DATABRICKS_INSTANCE = "https://test.databricks.com";
        process.env.DATABRICKS_JOB = "test-job";
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("formatTimestamp", () => {
        it("should format valid timestamp with minutes/seconds", () => {
            const timestamp = Date.now();
            const result = connection.formatTimestamp(timestamp);
            expect(result).toMatch(
                /\d{2}\/\d{2}\/\d{4}, \d{1,2}:\d{2}:\d{2} (AM|PM)/
            );
        });

        it("should format valid timestamp without minutes/seconds", () => {
            const timestamp = Date.now();
            const result = connection.formatTimestamp(timestamp, false);
            expect(result).toMatch(
                /\d{2}\/\d{2}\/\d{4}, \d{1,2}:\d{2} (AM|PM)/
            );
        });

        it("should return 'N/A' for null input", () => {
            expect(connection.formatTimestamp(null)).toBe("N/A");
        });

        it("should return 'Invalid Date' for invalid string", () => {
            expect(connection.formatTimestamp("invalid")).toBe("Invalid Date");
        });

        it("should return 'N/A' for NaN", () => {
            expect(connection.formatTimestamp(NaN)).toBe("N/A");
        });
    });

    describe("getAccessToken", () => {
        it("should return access token on successful authentication", async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: { access_token: "test-token" }
            });

            const token = await connection.getAccessToken();
            expect(token).toBe("test-token");
            expect(mockedAxios.post).toHaveBeenCalled();
        });

        it("should throw error on authentication failure", async () => {
            mockedAxios.post.mockRejectedValueOnce(
                new Error("Invalid response from token endpoint")
            );
            await expect(connection.getAccessToken()).rejects.toThrow(
                "Failed to fetch access token"
            );
        });
    });

    describe("Job operations", () => {
        const accessToken = "test-token";

        describe("listAllJobs", () => {
            it("should return job list on success", async () => {
                mockedAxios.get.mockResolvedValueOnce({
                    data: { jobs: [{ id: 1, name: "Job 1" }] }
                });

                const jobs = await connection.listAllJobs(accessToken);
                expect(jobs).toEqual({ jobs: [{ id: 1, name: "Job 1" }] });
            });

            it("should throw error on API failure", async () => {
                mockedAxios.get.mockRejectedValueOnce(
                    new Error("Empty response received from jobs list API")
                );
                await expect(
                    connection.listAllJobs(accessToken)
                ).rejects.toThrow("Failed to list all jobs");
            });
        });

        describe("findJobIdByName", () => {
            it("should return job ID when job exists", async () => {
                mockedAxios.get.mockResolvedValueOnce({
                    data: {
                        jobs: [
                            { settings: { name: "target-job" }, job_id: 123 }
                        ]
                    }
                });

                const jobId = await connection.findJobIdByName(
                    accessToken,
                    "target-job"
                );
                expect(jobId).toBe(123);
            });

            it("should throw error when job not found", async () => {
                mockedAxios.get.mockResolvedValueOnce({
                    data: {
                        jobs: [{ settings: { name: "other-job" }, job_id: 456 }]
                    }
                });

                await expect(
                    connection.findJobIdByName(accessToken, "target-job")
                ).rejects.toThrow("Job with name 'target-job' not found");
            });

            it("should throw error when no jobs exist", async () => {
                mockedAxios.get.mockResolvedValueOnce({ data: { jobs: [] } });

                await expect(
                    connection.findJobIdByName(accessToken, "target-job")
                ).rejects.toThrow("No jobs found in the workspace");
            });
        });

        describe("listJobRuns", () => {
            it("should return job runs on success", async () => {
                const mockRuns = { runs: [{ id: 1, status: "SUCCESS" }] };
                mockedAxios.get.mockResolvedValueOnce({ data: mockRuns });

                const runs = await connection.listJobRuns(accessToken, 123, 5);
                expect(runs).toEqual(mockRuns);
            });

            it("should throw error on API failure", async () => {
                mockedAxios.get.mockRejectedValueOnce(
                    new Error("Empty response received from job runs list API")
                );
                await expect(
                    connection.listJobRuns(accessToken, 123)
                ).rejects.toThrow("Failed to list job runs");
            });
        });
    });

    describe("Run operations", () => {
        const accessToken = "test-token";
        const runId = "run-123";

        describe("fetchRunStatus", () => {
            it("should return run status on success", async () => {
                const mockStatus = { state: "RUNNING", start_time: Date.now() };
                mockedAxios.get.mockResolvedValueOnce({ data: mockStatus });

                const status = await connection.fetchRunStatus(
                    accessToken,
                    runId
                );
                expect(status).toEqual(mockStatus);
            });

            it("should throw error on API failure", async () => {
                mockedAxios.get.mockRejectedValueOnce(new Error("API error"));
                await expect(
                    connection.fetchRunStatus(accessToken, runId)
                ).rejects.toThrow("Failed to fetch run status");
            });
        });

        describe("fetchRunOutput", () => {
            it("should return parsed output when available", async () => {
                const mockOutput = {
                    notebook_output: JSON.stringify({ data: "test-data" })
                };
                mockedAxios.get.mockResolvedValueOnce({ data: mockOutput });

                const output = await connection.fetchRunOutput(
                    accessToken,
                    runId
                );
                expect(output).toEqual({
                    notebook_output: JSON.stringify({ data: "test-data" }),
                    result_data: "test-data"
                });
            });

            it("should handle unparsable output", async () => {
                const mockOutput = {
                    notebook_output: "plain-text-output"
                };
                mockedAxios.get.mockResolvedValueOnce({ data: mockOutput });

                const output = await connection.fetchRunOutput(
                    accessToken,
                    runId
                );
                expect(output).toEqual({
                    notebook_output: "plain-text-output",
                    result_data: "plain-text-output"
                });
            });

            it("should throw error on API failure", async () => {
                mockedAxios.get.mockRejectedValueOnce(
                    new Error("Failed to fetch run output")
                );
                await expect(
                    connection.fetchRunOutput(accessToken, runId)
                ).rejects.toThrow("Failed to fetch run output");
            });
        });
    });

    describe("fetchJobRunsWithFormatting", () => {
        const accessToken = "test-token";
        const jobId = 123;

        it("should return formatted runs", async () => {
            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    runs: [
                        {
                            start_time: Date.now(),
                            end_time: Date.now() + 3600000,
                            state: { result_state: "SUCCESS" }
                        }
                    ]
                }
            });

            const result = await connection.fetchJobRunsWithFormatting(
                accessToken,
                jobId
            );
            expect(result.runs[0]).toHaveProperty("start_time");
            expect(result.runs[0]).toHaveProperty("end_time");
            expect(result.runs[0]).toHaveProperty("result_state", "SUCCESS");
        });

        it("should return empty array when no runs exist", async () => {
            mockedAxios.get.mockResolvedValueOnce({ data: { runs: [] } });

            const result = await connection.fetchJobRunsWithFormatting(
                accessToken,
                jobId
            );
            expect(result).toEqual({ runs: [] });
        });

        it("should handle errors from listJobRuns", async () => {
            mockedAxios.get.mockRejectedValueOnce(new Error("API error"));
            await expect(
                connection.fetchJobRunsWithFormatting(accessToken, jobId)
            ).rejects.toThrow("Failed to list job runs");
        });
    });

    describe("fetchJobData", () => {
        const accessToken = "test-token";

        it("should fetch job data by name", async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: { access_token: accessToken }
            });
            mockedAxios.get
                .mockResolvedValueOnce({
                    data: {
                        jobs: [{ settings: { name: "test-job" }, job_id: 123 }]
                    }
                })
                .mockResolvedValueOnce({
                    data: { runs: [{ run_id: "run-123" }] }
                })
                .mockResolvedValueOnce({
                    data: { notebook_output: "{}" }
                });

            const result = await connection.fetchJobData("test-job");
            expect(result).toHaveProperty("output");
        });

        it("should fetch job data by ID", async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: { access_token: accessToken }
            });
            mockedAxios.get
                .mockResolvedValueOnce({
                    data: { runs: [{ run_id: "run-123" }] }
                })
                .mockResolvedValueOnce({
                    data: { notebook_output: "{}" }
                });

            const result = await connection.fetchJobData(123);
            expect(result).toHaveProperty("output");
        });

        it("should include status when requested", async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: { access_token: accessToken }
            });
            mockedAxios.get
                .mockResolvedValueOnce({
                    data: {
                        runs: [{ run_id: "run-123", start_time: Date.now() }]
                    }
                })
                .mockResolvedValueOnce({
                    data: { notebook_output: "{}" }
                });

            const result = await connection.fetchJobData(123, true);
            expect(result).toHaveProperty("status");
            expect(result).toHaveProperty("output");
        });

        it("should throw error when no runs found", async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: { access_token: accessToken }
            });
            mockedAxios.get.mockResolvedValueOnce({
                data: { runs: [] }
            });

            await expect(connection.fetchJobData(123)).rejects.toThrow(
                "No completed runs found for job ID 123"
            );
        });
    });

    describe("Cron helper functions", () => {
        describe("formatCronHourToReadableTime", () => {
            it("should format 0 as 12 AM", () => {
                expect(connection.formatCronHourToReadableTime(0)).toBe(
                    "12:00 AM"
                );
            });

            it("should format 12 as 12 PM", () => {
                expect(connection.formatCronHourToReadableTime(12)).toBe(
                    "12:00 PM"
                );
            });

            it("should format 15 as 3 PM", () => {
                expect(connection.formatCronHourToReadableTime(15)).toBe(
                    "3:00 PM"
                );
            });
        });

        describe("parseCronHours", () => {
            it("should parse single hour", () => {
                expect(connection.parseCronHours("0 5 * * *")).toEqual([5]);
            });

            it("should parse multiple hours", () => {
                expect(connection.parseCronHours("0 8,12,16 * * *")).toEqual([
                    8, 12, 16
                ]);
            });

            it("should return empty array for invalid cron", () => {
                expect(connection.parseCronHours("invalid")).toEqual([]);
            });

            it("should return empty array for malformed cron", () => {
                expect(connection.parseCronHours("0 * * *")).toEqual([]);
            });
        });
    });

    describe("DEFAULT_JOB_NAME", () => {
        it("should be set from environment", () => {
            jest.resetModules();
            process.env.DATABRICKS_JOB = "test-job";
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const freshConnection = require("./connection");
            expect(freshConnection.DEFAULT_JOB_NAME).toBe("test-job");
        });
    });
});
