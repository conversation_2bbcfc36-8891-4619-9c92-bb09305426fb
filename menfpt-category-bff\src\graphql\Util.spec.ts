import { getPacificDate, LOCKOUT_CERTIFY_CUTTOFF } from './Util';

describe('Util functions', () => {
    describe('getPacificDate', () => {
        beforeEach(() => {
            // Mock the Date constructor to return a fixed date for consistent testing
            jest.useFakeTimers();
            jest.setSystemTime(new Date('2023-06-15T20:00:00.000Z')); // UTC time
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        it('should return a Date object', () => {
            const result = getPacificDate();
            expect(result).toBeInstanceOf(Date);
        });

        it('should return a valid Pacific time date', () => {
            const result = getPacificDate();
            expect(result).toBeInstanceOf(Date);
            expect(result.getTime()).toBeGreaterThan(0);
        });

        it('should handle different times of year (DST vs Standard)', () => {
            // Test during standard time (winter)
            jest.setSystemTime(new Date('2023-01-15T20:00:00.000Z'));
            const winterResult = getPacificDate();
            expect(winterResult).toBeInstanceOf(Date);

            // Test during daylight saving time (summer)
            jest.setSystemTime(new Date('2023-07-15T20:00:00.000Z'));
            const summerResult = getPacificDate();
            expect(summerResult).toBeInstanceOf(Date);

            // Both should be valid dates
            expect(winterResult.getTime()).toBeGreaterThan(0);
            expect(summerResult.getTime()).toBeGreaterThan(0);
        });

        it('should handle edge cases around midnight', () => {
            // Test around midnight UTC
            jest.setSystemTime(new Date('2023-06-15T07:00:00.000Z')); // Midnight Pacific
            const midnightResult = getPacificDate();
            expect(midnightResult).toBeInstanceOf(Date);
            expect(midnightResult.getTime()).toBeGreaterThan(0);
        });

        it('should return consistent results for same input time', () => {
            const fixedTime = new Date('2023-06-15T15:30:00.000Z');
            jest.setSystemTime(fixedTime);

            const result1 = getPacificDate();
            const result2 = getPacificDate();

            // Results should be close in time (accounting for any processing differences)
            expect(Math.abs(result1.getTime() - result2.getTime())).toBeLessThan(1000);
        });
    });

    describe('LOCKOUT_CERTIFY_CUTTOFF constant', () => {
        const originalEnv = process.env.LOCKOUT_CERTIFY_CUTTOFF;

        afterEach(() => {
            if (originalEnv !== undefined) {
                process.env.LOCKOUT_CERTIFY_CUTTOFF = originalEnv;
            } else {
                delete process.env.LOCKOUT_CERTIFY_CUTTOFF;
            }
        });

        it('should use environment variable when set', () => {
            // Since this is imported at module level, we need to test with the current value
            expect(typeof LOCKOUT_CERTIFY_CUTTOFF).toBe('string');
            expect(LOCKOUT_CERTIFY_CUTTOFF).toMatch(/T\d{2}:\d{2}:\d{2}\.\d{3}/);
        });

        it('should have default value format when env var not set', () => {
            // Test the default value format
            expect(LOCKOUT_CERTIFY_CUTTOFF).toMatch(/T\d{2}:\d{2}:\d{2}\.\d{3}/);
            // Default should be T10:00:00.000 if env var not set
            if (!process.env.LOCKOUT_CERTIFY_CUTTOFF) {
                expect(LOCKOUT_CERTIFY_CUTTOFF).toBe('T10:00:00.000');
            }
        });

        it('should be a valid time format string', () => {
            expect(LOCKOUT_CERTIFY_CUTTOFF).toMatch(/^T\d{2}:\d{2}:\d{2}\.\d{3}$/);
        });

        it('should represent a valid time (hours 00-23)', () => {
            const timeMatch = LOCKOUT_CERTIFY_CUTTOFF.match(/T(\d{2}):(\d{2}):(\d{2})\.(\d{3})/);
            expect(timeMatch).not.toBeNull();

            if (timeMatch) {
                const hours = parseInt(timeMatch[1], 10);
                const minutes = parseInt(timeMatch[2], 10);
                const seconds = parseInt(timeMatch[3], 10);
                const milliseconds = parseInt(timeMatch[4], 10);

                expect(hours).toBeGreaterThanOrEqual(0);
                expect(hours).toBeLessThanOrEqual(23);
                expect(minutes).toBeGreaterThanOrEqual(0);
                expect(minutes).toBeLessThanOrEqual(59);
                expect(seconds).toBeGreaterThanOrEqual(0);
                expect(seconds).toBeLessThanOrEqual(59);
                expect(milliseconds).toBeGreaterThanOrEqual(0);
                expect(milliseconds).toBeLessThanOrEqual(999);
            }
        });
    });
});
