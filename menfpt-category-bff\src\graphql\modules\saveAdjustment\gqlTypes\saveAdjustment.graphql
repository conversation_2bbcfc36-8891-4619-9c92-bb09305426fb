input Adjustment {
    smicCategoryIds: [Int]
    deptId: String
    deskId: String
    divisionIds: [String]
    updatedBy: String
    weeks: [Week]
    isReset: Boolean
}

input Week {
    fiscalWeekNbr: Int
    editedColumns: String
    previousAggregatedData: AdjustmentData
    newAggregatedData: AdjustmentData
    reason: String
    comment: String
}

input AdjustmentData {
    line1PublicToSalesNbr: Float
    line1PublicToSalesPct: Float
    line5BookGrossProfitNbr: Float
    line5BookGrossProfitPct: Float
    line5MarkDownsNbr: Float
    line5MarkDownsPct: Float
    line5ShrinkNbr: Float
    line5ShrinkPct: Float
    line6SuppliesPackagingNbr: Float
    line6SuppliesPackagingPct: Float
    line7RetailsAllowancesNbr: Float
    line7RetailsSellingAllowancesNbr: Float
    line7RetailsNonSellingAllowancesNbr: Float
}

type SaveAdjustmentResponse {
    success: Boolean
    message: String
}

type Query {
    saveAdjustment(adjustment: Adjustment): SaveAdjustmentResponse
}
