import { jest, describe, it, expect, beforeAll, afterAll, beforeEach } from "@jest/globals";
import { getHelpPdfUrl } from "../helpIconService";

describe("helpIconService", () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules(); // Clears the module cache
    process.env = { ...originalEnv }; // Resets environment variables
  });

  afterAll(() => {
    process.env = originalEnv; // Restores original environment variables
  });

  it("should return the SharePoint PDF URL when HELP_PDF_SHAREPOINT is set", () => {
    process.env.HELP_PDF_SHAREPOINT = "https://rxsafeway.sharepoint.com/sites";

    const result = getHelpPdfUrl();

    expect(result).toEqual({
      url: "https://rxsafeway.sharepoint.com/sites",
      message: "Success",
    });
  });

});