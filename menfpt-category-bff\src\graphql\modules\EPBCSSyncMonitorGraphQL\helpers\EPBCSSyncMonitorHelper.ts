// @ts-nocheck
import {
    DEFAULT_JOB_NAME,
    getJobRunsFromDatabricks,
    parseCronHours,
    formatCronHourToReadableTime,
    MONDAY_CRON,
    THURSDAY_CRON,
    FRIDAY_CRON
} from "../EPBCSSyncMonitor";
import { DisplayDateProvider } from "../../DisplayDate/providers/DisplayDate.provider";
import { parseDateTime } from "../../../EPBCSSyncMonitor_WeeksDateUtility";

const DAYS_OF_WEEK = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

export function formatDateString(date: Date): string {
    return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}/${date.getFullYear()}`;
}

export async function getFiscalWeekDates(context: any) {
    try {
        const DisplayDateService = context?.injector?.get(DisplayDateProvider.provide);
        if (!DisplayDateService) {
            console.error("DisplayDateService not available");
            return { start: "-", end: "-" };
        }

        // Format current date in MM/DD/YYYY format
        const today = new Date();
        const formattedDate = formatDateString(today);

        // Get fiscal week information
        const displayDateData = await DisplayDateService.getCalendarData({ date: formattedDate });

        if (displayDateData && displayDateData.length > 0) {
            const start = displayDateData[0].fiscalWeekStartDate || "-";
            const end = displayDateData[0].fiscalWeekEndDate || "-";
            const fiscalPeriodStartDate = displayDateData[0].fiscalPeriodStartDate || "-";
            const fiscalPeriodEndDate = displayDateData[0].fiscalPeriodEndDate || "-";
            // console.log(`Retrieved fiscal dates: Week ${start} to ${end}, Period ${fiscalPeriodStartDate} to ${fiscalPeriodEndDate}`);
            return { 
                start, 
                end,
                fiscalPeriodStartDate,
                fiscalPeriodEndDate
            };
        }

        // console.log("No fiscal week data returned from calendar service");
        return { start: "-", end: "-" };
    } catch (error) {
        console.error("Error getting fiscal week dates:", error);
        return { start: "-", end: "-" };
    }
}

/**
 * Check if a job run exists at a specific date and time
 */
export function checkJobRunAtTime(runs: any[], date: string, time: string) {
    if (!runs || !runs.length) {
        return { sync_enabled: false, status: null, start_time: null, end_time: null, life_cycle_state: null };
    }

    for (const run of runs) {
        if (run.start_time === 'N/A' || run.start_time === 'Invalid Date') {
            continue;
        }

        try {
            const [runDatePart, runTimePart] = run.start_time.split(', ');
            if (runDatePart === date) {
                const runDateObj = parseDateTime(runDatePart, runTimePart);
                const schedDateObj = parseDateTime(date, time);

                if (runDateObj && schedDateObj) {
                    const minuteDiff = Math.abs(
                        (runDateObj.getHours() * 60 + runDateObj.getMinutes()) -
                        (schedDateObj.getHours() * 60 + schedDateObj.getMinutes())
                    );

                    if (minuteDiff <= 15) {
                        return {
                            sync_enabled: true,
                            status: run.result_state || 'UNKNOWN',
                            start_time: run.start_time,
                            end_time: run.end_time,
                            life_cycle_state: run.life_cycle_state || null
                        };
                    }
                }
            }
        } catch (error) {
            console.error(`Error parsing run start time: ${run.start_time}`, error);
            continue;
        }
    }

    return { sync_enabled: false, status: null, start_time: null, end_time: null, life_cycle_state: null };
}

export function parseTimeString(timeStr: string): number {
    if (!timeStr) return 0;
    
    const [time, period] = timeStr.split(' ');
    let [hours, minutes] = time.split(':').map(Number);
    
    if (period === 'PM' && hours < 12) {
        hours += 12;
    } else if (period === 'AM' && hours === 12) {
        hours = 0;
    }
    
    return hours * 60 + minutes;
}

export function parseSessionDateTime(session: any): Date | null {
    if (!session || !session.date || !session.time || session.date === '-') {
        return null;
    }
    try {
        return parseDateTime(session.date, session.time);
    } catch (error) {
        console.error(`Error parsing session date/time: ${session.date} ${session.time}`, error);
        return null;
    }
}

export function parseRunDateTime(date: string, time: string): Date | null {
    if (!date || !time) return null;
    try {
        return parseDateTime(date, time);
    } catch (error) {
        console.error(`Error parsing run date/time: ${date} ${time}`, error);
        return null;
    }
}

export function getWeekNumberOfDate(date: Date): number {
    const firstJan = new Date(date.getFullYear(), 0, 1);
    const days = Math.floor((date.getTime() - firstJan.getTime()) / (24 * 60 * 60 * 1000));
    return Math.ceil((days + firstJan.getDay() + 1) / 7);
}

export function processDaySchedule(day: string, date: string, times: string[], runs: any[], syncSessions: any[]) {
    // console.log(`Processing schedule for ${day} (${date}): ${times.join(', ')}`);
    
    times.forEach(time => {
        // console.log(`Checking for run at ${time} on ${date}`);
                const runStatus = checkJobRunAtTime(runs, date, time);
        
        let formattedStatus = null;
        if (runStatus.sync_enabled) {
            formattedStatus = runStatus.status === 'SUCCESS' ? 'Complete' : 
                              runStatus.status === 'FAILED' ? 'Fail' : 
                              runStatus.life_cycle_state === 'RUNNING' ? 'Processing' : 
                              'Unknown';
        }
        
        syncSessions.push({
            day,
            date,
            time,
            sync_enabled: runStatus.sync_enabled,
            status: formattedStatus,
            start_time: runStatus.start_time || null,
            end_time: runStatus.end_time || null
        });
    });
}



export function getPreviousWeekSync(runs: any[]) {
    if (!runs || !runs.length) {
        return null;
    }

    const today = new Date();
    const prevMonday = new Date(today);
    prevMonday.setDate(today.getDate() - today.getDay() - 6); 
    prevMonday.setHours(0, 0, 0, 0);

    // console.log("[SyncHistory] Looking for runs on previous Monday:", formatDateString(prevMonday));

    let foundRun = null;
    for (const run of runs) {
        if (!run.start_time || run.start_time === 'N/A' || run.start_time === 'Invalid Date') {
            continue;
        }

        const startTimeStr = run.start_time;
        try {
            let runDateObj;
            if (run.start_time.includes(',')) {
                const [runDatePart, runTimePart] = run.start_time.split(', ');
                const [mm, dd, yyyy] = runDatePart.split('/').map(Number);
                runDateObj = new Date(yyyy, mm - 1, dd);
            } else {
                runDateObj = new Date(run.start_time);
            }
            runDateObj.setHours(0, 0, 0, 0);

            if (runDateObj.getTime() === prevMonday.getTime()) {
                let runTimePart = '';
                if (run.start_time.includes(',')) {
                    runTimePart = run.start_time.split(', ')[1];
                } else {
                    const hours = runDateObj.getHours();
                    const minutes = runDateObj.getMinutes();
                    const ampm = hours >= 12 ? 'PM' : 'AM';
                    const formattedHours = hours % 12 || 12;
                    runTimePart = `${formattedHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
                }
                foundRun = {
                    date: formatDateString(prevMonday),
                    time: runTimePart,
                    weekNumber: getWeekNumberOfDate(prevMonday),
                    raw: run
                };
                break;
            }
        } catch (e) {
            continue;
        }
    }

    // console.log("[SyncHistory] Previous Week Monday:", {
    //     prevMonday: formatDateString(prevMonday),
    //     found: !!foundRun,
    //     details: foundRun ? foundRun : "No run found for previous Monday"
    // });

    if (foundRun && foundRun.raw) delete foundRun.raw;

    return foundRun;
}


export function getDefaultWeeks(numWeeks = 4) {
    const weeks = [];
    const today = new Date();
        const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    
    for (let i = 0; i < numWeeks; i++) {
        const weekStartDate = new Date(startOfWeek);
        weekStartDate.setDate(startOfWeek.getDate() - (7 * i));
        
        const weekEndDate = new Date(weekStartDate);
        weekEndDate.setDate(weekStartDate.getDate() + 6);
        
        weeks.push({
            weekNumber: i + 1,
            weekStartDate: formatDateString(weekStartDate),
            weekEndDate: formatDateString(weekEndDate),
            lastRun: null
        });
    }
    
    return weeks.reverse(); 
}

export function getWeeksForFiscalPeriod(fiscalPeriodStartDate: string, fiscalPeriodEndDate: string) {
    if (!fiscalPeriodStartDate || !fiscalPeriodEndDate || 
        fiscalPeriodStartDate === '-' || fiscalPeriodEndDate === '-') {
        // console.log("Invalid fiscal period dates, using current date for week calculation");
        return getDefaultWeeks(3); 
    }

    try {
        const [startMonth, startDay, startYear] = fiscalPeriodStartDate.split('/').map(Number);
        const [endMonth, endDay, endYear] = fiscalPeriodEndDate.split('/').map(Number);
        
        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, endMonth - 1, endDay);
        
        const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        const numWeeks = Math.ceil(daysDiff / 7);
        
        // console.log(`Fiscal period spans ${daysDiff} days, approximately ${numWeeks} weeks`);
        
        const weeks = [];
        for (let i = 0; i < numWeeks; i++) {
            const weekStartDate = new Date(startDate);
            weekStartDate.setDate(startDate.getDate() + (i * 7));
            
            const weekEndDate = new Date(weekStartDate);
            weekEndDate.setDate(weekStartDate.getDate() + 6);
            
            if (weekEndDate > endDate) {
                weekEndDate.setTime(endDate.getTime());
            }
            
            const weekStartDateStr = formatDateString(weekStartDate);
            const weekEndDateStr = formatDateString(weekEndDate);
            
            weeks.push({
                weekNumber: i + 1,
                weekStartDate: weekStartDateStr,
                weekEndDate: weekEndDateStr,
                lastRun: null
            });
        }
        
        return weeks;
    } catch (error) {
        console.error("Error calculating weeks");
    }
}
