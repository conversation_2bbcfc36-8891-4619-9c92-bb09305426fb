import express from "express";
import {getHelpPdfUrl}  from "../services/helpIconService";
import { HELP_ICON_ENDPOINTS } from "../graphql/shared/constants/routerConstants";

const router = express.Router();

router.get(HELP_ICON_ENDPOINTS.HELP_ICON, (_req, res) => {
  res.json({ message: "Help icon route is working!" });
});

router.get(HELP_ICON_ENDPOINTS.VIEW, async (_req, res) => {
  console.log("View endpoint hit");
  try {
    const pdfResult = getHelpPdfUrl();
    if (pdfResult) {
      console.log("Returning SharePoint PDF URL");
      res.json(pdfResult);
    } else {
      res.status(404).send({ message: "PDF URL not found" });
    }
  } catch (error) {
    console.error("Error fetching PDF URL:", error);
    res.status(500).send({ message: "Internal server error" });
  }
});

export default router;