import logger, { colorizer } from "./logger";

export interface ErrorPayload {
    httpStatus: number | null;
    errorCode: string;
    name: string;
    message: string;
    url: string;
}

export function createErrorPayload(error: unknown): ErrorPayload {
    // Use lodash/get if needed, or fallback to optional chaining
    const get = (obj: any, path: string, defaultValue: any) => {
        return (
            path.split(".").reduce((acc, part) => acc && acc[part], obj) ??
            defaultValue
        );
    };
    return {
        httpStatus: get(error, "response.status", null),
        errorCode: get(error, "code", "ERROR_CODE_UNKNOWN"),
        name: get(error, "name", "ERROR_NAME_UNKNOWN"),
        message: get(error, "response.data.message", ""),
        url: get(error, "config.url", "REQUEST_URL_UNKNOWN")
    };
}

export interface LogApiCallOptions {
    phase: "START" | "END" | "ERROR";
    method: string;
    url: string;
    payload?: any;
    response?: any;
    error?: any;
}

export function logApiCall({
    phase,
    method,
    url,
    payload,
    response,
    error
}: LogApiCallOptions) {
    const timestamp = new Date().toISOString();
    const separator = "=====================================";
    let logMessage = "";
    if (phase === "START") {
        logMessage = [
            `\n\n${separator}`,
            "          API CALL START",
            separator,
            `timestamp     : ${timestamp}`,
            `url           : ${url}`,
            `method        : '${method}'`,
            `payload       : ${JSON.stringify(payload || {})}`,
            separator
        ].join("\n");
        logger.info(logMessage);
    } else if (phase === "END") {
        logMessage = [
            `\n\n${separator}`,
            "           API CALL END",
            separator,
            `timestamp     : ${timestamp}`,
            `url           : ${url}`,
            `method        : '${method}'`,
            `payload       : ${JSON.stringify(payload || {})}`,
            `response      : ${JSON.stringify(response?.data || {})}`,
            separator
        ].join("\n");
        logger.info(logMessage);
    } else if (phase === "ERROR") {
        const logMessage = [
            `\n\n${separator}`,
            "    !!! ERROR !!! API CALL FAILED !!!",
            separator,
            `timestamp     : ${timestamp}`,
            `url           : ${url}`,
            `method        : '${method}'`,
            `payload       : ${JSON.stringify(payload || {})}`,
            `error         : ${error?.message || error}`,
            `errorPayload  : ${JSON.stringify(createErrorPayload(error))}`,
            separator
        ].join("\n");

        const errorWithCustomMessage = new Error(logMessage);
        if (error && error.stack) {
            errorWithCustomMessage.stack = error.stack;
        }
        logger.error(errorWithCustomMessage);
    }
}
