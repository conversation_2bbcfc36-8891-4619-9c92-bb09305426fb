import { AdjustmentWorksheetReq } from "src/graphql/__generated__/gql-ts-types";
import { ContextValue } from "../../../context";
import {
    CalendarServiceProvider,
    ForecastProvider
} from "../providers/forecastAdjustmentTable.provider";
import getPreviousYearSameDate from "../../../shared/utils/dateMethods";
import {
    getWorksheetFormatData,
    worksheetSubRowsOneDimentionalData,
    worksheetSubRowsTwoDimentionalData
} from "../helper/forecastAdjustmentTableHelper";
import {
    worksheetDataTimings,
    worksheetTypes
} from "../forecastAdjustmentTableConstant";
export const ForecastAdjustmentTableResolver = async (
    parent: any,
    args: { adjustmentWorksheetReq: AdjustmentWorksheetReq },
    context: ContextValue
): Promise<any> => {
    const { adjustmentWorksheetReq } = args;
    const calenderService = context?.injector?.get(
        CalendarServiceProvider.provide
    );
    const forecastService = context?.injector?.get(ForecastProvider.provide);

    const getCalenderData = async () => {
        //let previousYearDate = args.adjustmentWorksheetReq.quarterEndingDate ||getPreviousYearSameDate() 06/22/2024;
        let previousYearDate = getPreviousYearSameDate(args.adjustmentWorksheetReq.quarterEndingDate)
        const [currentQuarterData, previousYearSameDateData] =
            await Promise.all([
                calenderService?.getCalendarData({
                    fiscalQuarterNumber: args.adjustmentWorksheetReq?.quarterNbr
                }),
                calenderService?.getCalendarData({
                    date: previousYearDate
                })
            ]);
        return { currentQuarterData, previousYearSameDateData };
    };

    const getWorksheetTableData = async (req: any): Promise<any> => {
        let response = await forecastService?.getWorksheetData(req);
        let worksheetData = getWorksheetFormatData(req, response);
        return worksheetData;
    };

    const getWorksheetTablaRawData = async (worksheetTableReq: any) => {
        let worksheetTableRawData: any = [];
        let start = new Date().getTime();
        const [foreCastData, worksheetDataPromises] = await Promise.all([
            getWorksheetTableData({
                smicCategoryIds: worksheetTableReq?.smicCategoryIds,
                divisionIds: worksheetTableReq?.divisionIds,
                deptIds: worksheetTableReq?.deptIds,
                fiscalQuarterNbrs: worksheetTableReq.fiscalCurrentQuarterNbrs,
                aggregate: false,
                fethAll: true,
                endPoint: "forecast"
            }),
            await Promise.allSettled(
                worksheetTypes.map((type: any) => {
                    return Promise.all([
                        getWorksheetTableData({
                            smicCategoryIds: worksheetTableReq?.smicCategoryIds,
                            divisionIds: worksheetTableReq?.divisionIds,
                            deptIds: worksheetTableReq?.deptIds,
                            fiscalWeekNbrs:
                                type === "actuals"
                                    ? [
                                        ...worksheetTableReq.currentQTDWeeksNbrs,
                                        ...worksheetTableReq.previousQTDWeekNbrs
                                    ]
                                    : worksheetTableReq.currentQTDWeeksNbrs,
                            aggregate: true,
                            aggregateBy: ["fiscal_quarter_nbr"],
                            fethAll: true,
                            sortBy: "fiscal_quarter_nbr",
                            sortDirection: "asc",
                            aggregatedLevel: "Quarter to Date",
                            subRow: type,
                            endPoint: type
                        }),
                        getWorksheetTableData({
                            smicCategoryIds: worksheetTableReq?.smicCategoryIds,
                            divisionIds: worksheetTableReq?.divisionIds,
                            deptIds: worksheetTableReq?.deptIds,
                            fiscalQuarterNbrs:
                                type === "actuals"
                                    ? [
                                        ...worksheetTableReq.fiscalCurrentQuarterNbrs,
                                        ...worksheetTableReq.fiscalPreviousQuarterNbrs
                                    ]
                                    : worksheetTableReq.fiscalCurrentQuarterNbrs,
                            aggregate: true,
                            aggregateBy: ["fiscal_quarter_nbr"],
                            fethAll: true,
                            sortBy: "fiscal_quarter_nbr",
                            sortDirection: "asc",
                            aggregatedLevel: "Quarter",
                            subRow: type,
                            endPoint: type
                        }),
                        getWorksheetTableData({
                            smicCategoryIds: worksheetTableReq?.smicCategoryIds,
                            divisionIds: worksheetTableReq?.divisionIds,
                            deptIds: worksheetTableReq?.deptIds,
                            fiscalQuarterNbrs:
                                type === "actuals"
                                    ? [
                                        ...worksheetTableReq.fiscalCurrentQuarterNbrs,
                                        ...worksheetTableReq.fiscalPreviousQuarterNbrs
                                    ]
                                    : worksheetTableReq.fiscalCurrentQuarterNbrs,
                            aggregate: true,
                            aggregateBy: ["fiscal_period_nbr"],
                            fethAll: true,
                            sortBy: "fiscal_period_nbr",
                            sortDirection: "asc",
                            aggregatedLevel: "Period",
                            subRow: type,
                            endPoint: type
                        }),
                        getWorksheetTableData({
                            smicCategoryIds: worksheetTableReq?.smicCategoryIds,
                            divisionIds: worksheetTableReq?.divisionIds,
                            deptIds: worksheetTableReq?.deptIds,
                            fiscalQuarterNbrs:
                                type === "actuals" || type === "forecast"
                                    ? [
                                        ...worksheetTableReq.fiscalCurrentQuarterNbrs,
                                        ...worksheetTableReq.fiscalPreviousQuarterNbrs
                                    ]
                                    : worksheetTableReq.fiscalCurrentQuarterNbrs,
                            aggregate: true,
                            aggregateBy: ["fiscal_week_nbr"],
                            fethAll: true,
                            sortBy: "fiscal_week_nbr",
                            sortDirection: "asc",
                            aggregatedLevel: "Weeks",
                            subRow: type,
                            endPoint: type
                        })
                    ]);
                })
            )
        ]);

        if (worksheetDataPromises) {
            worksheetDataPromises.forEach((result) => {
                if (
                    result?.status === "fulfilled" &&
                    result?.value?.length > 0
                ) {
                    worksheetTableRawData.push(...result.value);
                }
            });
        }
        return { foreCastData, worksheetTableRawData };
    };

    const getGroupedData = (data: any, key: any) => {
        let result = data?.reduce((acc: any, items: any) => {
            if (!acc[items[key]]) {
                acc[items[key]] = [];
            }
            acc[items[key]].push(items);
            return acc;
        }, {});
        return result;
    };

    const isLastYear = (previousYearNbr: any, grouedItemsKey: any) => {
        return grouedItemsKey?.toString().includes(previousYearNbr?.toString());
    };

    const getWorksheetTableFormatData = (
        rowData: any,
        subRows: any,
        key: any,
        aggregatedLevel: any,
        previousYearNbr: any,
        currentYearNbr: any,
        currentPeriodNbr?: any,
        currentWeekNbr?: any
    ) => {
        let formttedData: any = [];
        if (key) {
            let groupedData = getGroupedData(rowData, key);
            let previousWeekAF: any;
            let baseForecast: any;
            let lastYearLine1: Map<number, number> = new Map();
            for (let groupedItems in groupedData) {
                let forecast: any;
                let adjustedForecast: any;
                let projection: any;
                let actuals: any;
                if (!isLastYear(previousYearNbr, groupedItems)) {
                    let forecastData = groupedData[groupedItems].filter(
                        (grouped: any) => grouped.subRow === "forecast"
                    );
                    forecast = forecastData.filter(
                        (forecast: any) => forecast.forecastType === "DS"
                    )[0];
                    baseForecast = forecastData.filter(
                        (forecast: any) => forecast.forecastType === "Base"
                    )[0];
                    adjustedForecast =
                        forecastData.filter((forecast: any) => {
                            return (
                                forecast.forecastType === "FA" &&
                                forecast.state === "DRAFT"
                            );
                        })[0] ||
                        forecastData.filter((forecast: any) => {
                            return (
                                forecast.forecastType === "FA" &&
                                forecast.state === "PUBLISHED"
                            );
                        })[0];
                    groupedData[groupedItems].map((i: any) => {
                        if (i.subRow === "projection") {
                            projection = i;
                        } else if (i.subRow === "actuals") {
                            actuals = i;
                        }
                    });

                    subRows.map((subRow: any) => {
                        switch (subRow) {
                            case "FCST to PROJ":
                                if (
                                    !(
                                        adjustedForecast?.aggregatedLevel ===
                                        "Weeks" && // all weeks other than closed
                                        adjustedForecast?.fiscalWeekNbr <
                                        currentWeekNbr
                                    )
                                ) {
                                    let FCSTtoPROJdata =
                                        worksheetSubRowsTwoDimentionalData(
                                            adjustedForecast,
                                            projection,
                                            "FCST to PROJ",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    formttedData.push(FCSTtoPROJdata);
                                }
                                break;
                            case "ACT to PROJ":
                                if (
                                    (actuals?.aggregatedLevel === "Weeks" && // closed weeks only
                                        actuals?.fiscalWeekNbr <
                                        currentWeekNbr) ||
                                    (actuals?.aggregatedLevel === "Period" && // closed periods only
                                        actuals?.fiscalPeriodNbr <
                                        currentPeriodNbr)
                                ) {
                                    let ACTtoPROJData =
                                        worksheetSubRowsTwoDimentionalData(
                                            actuals,
                                            projection,
                                            "ACT to PROJ",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    formttedData.push(ACTtoPROJData);
                                }
                                break;
                            case "DS Forecast":
                                if (
                                    forecast &&
                                    forecast?.aggregatedLevel !== null
                                ) {
                                    let forecastFormatdData =
                                        worksheetSubRowsOneDimentionalData(
                                            forecast,
                                            "DS Forecast",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    formttedData.push(forecastFormatdData);
                                }
                                break;
                            case "Base":
                                if (
                                    baseForecast &&
                                    baseForecast?.aggregatedLevel !== null
                                ) {
                                    let forecastFormatdData =
                                        worksheetSubRowsOneDimentionalData(
                                            baseForecast,
                                            "Base",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    formttedData.push(forecastFormatdData);
                                }
                                break;
                            case "Projection":
                                if (
                                    projection &&
                                    projection?.aggregatedLevel !== undefined
                                ) {
                                    let projectionFormatData =
                                        worksheetSubRowsOneDimentionalData(
                                            projection,
                                            "Projection",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    formttedData.push(projectionFormatData);
                                }
                                break;
                            case "Actuals":
                                if (
                                    (actuals?.aggregatedLevel === "Weeks" && // closed weeks only
                                        actuals?.fiscalWeekNbr <
                                        currentWeekNbr) ||
                                    (actuals?.aggregatedLevel === "Period" && // closed periods only
                                        actuals?.fiscalPeriodNbr <
                                        currentPeriodNbr)
                                ) {
                                    let actualsFormatData =
                                        worksheetSubRowsOneDimentionalData(
                                            actuals,
                                            "Actuals",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    formttedData.push(actualsFormatData);
                                }
                                break;
                            case "Actual to Date":
                                if (
                                    !(
                                        actuals?.aggregatedLevel === "Period" && // only future/current periods
                                        actuals?.fiscalPeriodNbr <
                                        currentPeriodNbr
                                    ) &&
                                    !(
                                        actuals?.aggregatedLevel === "Weeks" && // only future/current weeks
                                        actuals?.fiscalWeekNbr < currentWeekNbr
                                    )
                                ) {
                                    let actualsFormatData =
                                        worksheetSubRowsOneDimentionalData(
                                            actuals,
                                            "Actual to Date",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    formttedData.push(actualsFormatData);
                                }
                                break;
                            case "Merch. Forecast":
                                if (adjustedForecast) {
                                    let adjustedFSCTFormatData =
                                        worksheetSubRowsOneDimentionalData(
                                            adjustedForecast,
                                            "Merch. Forecast",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    const ly = lastYearLine1.get(
                                        getLastTwoDigits(
                                            adjustedFSCTFormatData.fiscalWeekNbr
                                        )
                                    );
                                    if (
                                        ly &&
                                        adjustedFSCTFormatData?.aggregatedLevel ===
                                        "Weeks"
                                    ) {
                                        adjustedFSCTFormatData.line1PublicToSalesPct =
                                            calculatePercentageChange(
                                                lastYearLine1.get(
                                                    getLastTwoDigits(
                                                        adjustedFSCTFormatData.fiscalWeekNbr
                                                    )
                                                ),
                                                adjustedFSCTFormatData.line1PublicToSalesNbr
                                            );
                                    }
                                    formttedData.push(adjustedFSCTFormatData);
                                }
                                break;
                            case "WoW FCST variance":
                                if (previousWeekAF && adjustedForecast) {
                                    let WOWForecastVariance =
                                        worksheetSubRowsTwoDimentionalData(
                                            adjustedForecast,
                                            previousWeekAF,
                                            "WoW FCST var",
                                            aggregatedLevel,
                                            currentYearNbr,
                                            groupedItems
                                        );
                                    formttedData.push(WOWForecastVariance);
                                }
                                break;
                        }
                    });
                    previousWeekAF = adjustedForecast;
                } else {
                    if (subRows.includes("Last year actual")) {
                        let adjustedFSCTFormatData =
                            worksheetSubRowsOneDimentionalData(
                                getItemFromSubRow(
                                    groupedData[groupedItems],
                                    "actuals"
                                ),
                                "Last year actual",
                                aggregatedLevel,
                                previousYearNbr,
                                groupedItems,
                                currentYearNbr
                            );
                        formttedData.push(adjustedFSCTFormatData);
                    }
                    if (aggregatedLevel === "Weeks") {
                        const lastYearActual: any = getItemFromSubRow(
                            groupedData[groupedItems],
                            "actuals"
                        );
                        if (lastYearActual) {
                            lastYearLine1.set(
                                getLastTwoDigits(
                                    lastYearActual.fiscalWeekNbr
                                ),
                                lastYearActual.line1PublicToSalesNbr
                            );
                        }
                    }
                }
            }
            return formttedData;
        }
    };

    const getLastTwoDigits = (num: number) => {
        return num % 100;
    };

    const getItemFromSubRow = (items: [], subRow: string) => {
        return items.find((item: any) => item.subRow === subRow);
    };

    const calculatePercentageChange = (
        oldValue: number = 0,
        newValue: number
    ) => {
        let change = newValue - oldValue;
        let percentageChange = oldValue ? (change / oldValue) * 100 : 0;
        return percentageChange;
    };

    let calendarData: any = [];
    let previousYearCalendarData: any = [];
    let currentQTDWeekNumbers: any = [];
    let previousYearQTDWeekNumbers: any = [];
    let worksheetTableRawData: any = [];
    let worksheetTableRawflatData: any = [];
    let flatDataWithPeriodAndWeekNumbers: any = [];
    let worksheetTableFormatData: any = [];
    //Fetching current quarter details including periods, weeks under the quarter and previous year same date data
    const currentPreviousData = await getCalenderData();
    if (
        currentPreviousData?.previousYearSameDateData &&
        currentPreviousData?.previousYearSameDateData?.length > 0
    ) {
        //Fetching calender data to get the period numbers, week numbers and week ending dates for the previous year same quarter
        previousYearCalendarData = await calenderService?.getCalendarData({
            fiscalQuarterNumber:
                currentPreviousData?.previousYearSameDateData[0]
                    .fiscalQuarterNumber
        });
    }

    // Fetching week numbers for the quarter to date by comparing the current fiscal week number
    // with the week numbers in the calendar data
    if (
        currentPreviousData &&
        currentPreviousData?.currentQuarterData &&
        currentPreviousData?.currentQuarterData?.length > 0
    ) {
        currentPreviousData?.currentQuarterData?.map((item: any) => {
            if (
                adjustmentWorksheetReq?.currentFiscalWeekNbr &&
                item?.fiscalWeekNumber <=
                adjustmentWorksheetReq?.currentFiscalWeekNbr
            ) {
                currentQTDWeekNumbers.push(item?.fiscalWeekNumber);
            }
        });
    }
    // Fetching week numbers for the quarter to date by comparing the previous year same fiscal week number
    // with the week numbers in the previous year calendar data
    if (previousYearCalendarData && previousYearCalendarData?.length > 0) {
        previousYearCalendarData?.map((item: any) => {
            if (
                currentPreviousData?.previousYearSameDateData[0]
                    ?.fiscalWeekNumber &&
                item?.fiscalWeekNumber <=
                currentPreviousData?.previousYearSameDateData[0]
                    ?.fiscalWeekNumber
            ) {
                previousYearQTDWeekNumbers.push(item?.fiscalWeekNumber);
            }
        });
    }

    //Creating request object to fetch worksheet data for actuals, projection and forecast
    let worksheetTableReq = {
        smicCategoryIds: args.adjustmentWorksheetReq?.smicCategoryIds,
        divisionIds: args.adjustmentWorksheetReq?.divisionIds,
        deptIds: args.adjustmentWorksheetReq?.deptIds,
        fiscalCurrentQuarterNbrs: [args.adjustmentWorksheetReq.quarterNbr],
        fiscalPreviousQuarterNbrs: [
            currentPreviousData?.previousYearSameDateData[0].fiscalQuarterNumber
        ],
        currentQTDWeeksNbrs: [...new Set(currentQTDWeekNumbers)],
        previousQTDWeekNbrs: [...new Set(previousYearQTDWeekNumbers)]
    };
    //Asynchronus API call for getting data for actuals, projection and forecast Quarter to Date, Quarter, Period, Weeks aggregated data

    let workSheetData = await getWorksheetTablaRawData(worksheetTableReq);
    if (workSheetData?.worksheetTableRawData) {
        worksheetTableRawflatData = workSheetData?.worksheetTableRawData.flat();
    }

    calendarData = [
        ...currentPreviousData.currentQuarterData,
        ...previousYearCalendarData
    ];

    let groupedCalendarData = new Map(
        calendarData.map((item: any) => [
            item.fiscalWeekNumber,
            item.fiscalPeriodNumber
        ])
    );

    let periodToQuarterNbr = new Map(
        calendarData.map((item: any) => [
            item.fiscalPeriodNumber,
            item.fiscalQuarterNumber
        ])
    );

    let groupedWeekData = new Map(
        calendarData.map((item: any) => [
            item.fiscalWeekNumber,
            item.fiscalWeekEndDate
        ])
    );

    if (worksheetTableRawflatData && worksheetTableRawflatData?.length > 0) {
        let start = new Date().getTime();
        worksheetDataTimings.forEach((timing: any) => {
            let newData = worksheetTableRawflatData?.filter((flat: any) => {
                return flat.aggregatedLevel === timing.aggregatedLevel;
            });

            let formatData = getWorksheetTableFormatData(
                newData,
                timing.subRows,
                timing?.key,
                timing.aggregatedLevel,
                currentPreviousData.previousYearSameDateData[0]
                    .fiscalYearNumber,
                args?.adjustmentWorksheetReq?.currentFiscalYearNbr,
                args?.adjustmentWorksheetReq?.currentFiscalPeriodNbr,
                args.adjustmentWorksheetReq.currentFiscalWeekNbr
            );

            worksheetTableFormatData = [
                ...worksheetTableFormatData,
                ...formatData
            ];
        });
    }
    if (worksheetTableFormatData && worksheetTableFormatData?.length > 0) {
        flatDataWithPeriodAndWeekNumbers = worksheetTableFormatData.map(
            (d: any) => {
                if (!d.fiscalPeriodNbr && d.fiscalWeekNbr) {
                    return {
                        ...d,
                        fiscalPeriodNbr: groupedCalendarData.get(
                            +d.fiscalWeekNbr
                        ),
                        sortBy: groupedCalendarData.get(+d.fiscalWeekNbr),
                        fiscalWeekEnding: groupedWeekData.get(d.fiscalWeekNbr)
                    };
                } else if (d.fiscalPeriodNbr && d.fiscalWeekNbr === 0) {
                    return {
                        ...d,
                        fiscalQuarterNbr: periodToQuarterNbr.get(
                            +d.fiscalPeriodNbr
                        ),
                        sortBy: +d.fiscalPeriodNbr
                    };
                }
                return d;
            }
        );
    }

    let sortedData = getSortedData(flatDataWithPeriodAndWeekNumbers);
    let data = {
        adjustmentWorksheetData: sortedData,
        forecastData: workSheetData?.foreCastData
    };
    return data;
};

const getSortedData = (data: any) => {
    if (data && data.length > 0) {
        data.sort((a: any, b: any) => {
            // Sort by fiscalPeriodNbr first
            if (a.sortBy < b.sortBy) {
                return -1;
            }
            return 1;
        });
        return data;
    }
};
