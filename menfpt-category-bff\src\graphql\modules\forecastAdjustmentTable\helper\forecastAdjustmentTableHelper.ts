export const getWorksheetFormatData = (req: any, worksheetRawData: any) => {
    let worksheetData: any = [];
    if (worksheetRawData && worksheetRawData?.length > 0) {
        worksheetRawData.map((item: any) => {
            let data = {
                aggregatedLevel: req.aggregatedLevel,
                subRow: req.subRow,
                line1PublicToSalesNbr: item.line1PublicToSalesNbr,
                line5BookGrossProfitNbr: item.line5BookGrossProfitNbr,
                line5BookGrossProfitPct: item.line5BookGrossProfitPct,
                line5MarkDownsNbr: item.line5MarkDownsNbr,
                line5MarkDownsPct: item.line5MarkDownsPct,
                line5ShrinkNbr: item.line5ShrinkNbr,
                line5ShrinkPct: item.line5ShrinkPct,
                line5RealGrossProfitNbr: item.line5RealGrossProfitNbr,
                line5RealGrossProfitPct: item.line5RealGrossProfitPct,
                line6SuppliesPackagingNbr: item.line6SuppliesPackagingNbr
                    ? item.line6SuppliesPackagingNbr
                    : 0,
                line7RetailsAllowancesNbr: item.line7RetailsAllowancesNbr,
                line7RetailsAllowancesPct: item.line7RetailsAllowancesPct,
                line7RetailsSellingAllowancesNbr: item.line7RetailsSellingAllowancesNbr,
                line7RetailsSellingAllowancesPct: item.line7RetailsSellingAllowancesPct,
                line7RetailsNonSellingAllowancesNbr: item.line7RetailsNonSellingAllowancesNbr,
                line7RetailsNonSellingAllowancesPct: item.line7RetailsNonSellingAllowancesPct,
                line8RealGrossProfitNbr: item.line8RealGrossProfitNbr,
                line8RealGrossProfitPct: item.line8RealGrossProfitPct,
                comment: item?.comment,
                forecastType: item?.forecastType,
                lastUpdatedUserRole: item?.lastUpdatedUserRole,
                reason: item?.reason,
                state: item?.state,
                versionNbr: item?.versionNbr,
                createdTs: item?.createdTs,
                updatedTs: item?.updatedTs,
                sourceTs:item?.sourceTs,
                createdBy: item?.createdBy,
                updatedBy: item?.updatedBy,
                fiscalQuarterNbr: item?.fiscalQuarterNbr,
                fiscalPeriodNbr: item?.fiscalPeriodNbr,
                fiscalWeekNbr: item.fiscalPeriodNbr ? 0 : item.fiscalWeekNbr
            };

            worksheetData.push(data);
        });
    }

    return worksheetData;
};

export const worksheetSubRowsOneDimentionalData = (
    worksheetData: any,
    subRowType: string,
    aggregatedLevel: string,
    yearNbr: string,
    groupedItems: string,
    currentYearNbr?: string
): any => {
    let worksheetSubRow = {
        aggregatedLevel: aggregatedLevel,
        mainRow:
            aggregatedLevel === "Weeks"
                ? `${aggregatedLevel}${groupedItems
                    ?.toString()
                    .slice(yearNbr?.toString()?.length)}`
                : aggregatedLevel === "Period"
                    ? `${aggregatedLevel}${groupedItems
                        ?.toString()
                        .slice(yearNbr?.toString()?.length)}`
                    : aggregatedLevel,
        subRow: subRowType,
        line1PublicToSalesNbr: worksheetData?.line1PublicToSalesNbr
            ? worksheetData.line1PublicToSalesNbr
            : 0,
        line5BookGrossProfitNbr: worksheetData?.line5BookGrossProfitNbr
            ? worksheetData.line5BookGrossProfitNbr
            : 0,
        line5BookGrossProfitPct: worksheetData?.line5BookGrossProfitPct
            ? worksheetData.line5BookGrossProfitPct * 100
            : 0,
        line5MarkDownsNbr: worksheetData?.line5MarkDownsNbr
            ? worksheetData.line5MarkDownsNbr
            : 0,
        line5MarkDownsPct: worksheetData?.line5MarkDownsPct
            ? worksheetData.line5MarkDownsPct * 100
            : 0,
        line5ShrinkNbr: worksheetData?.line5ShrinkNbr
            ? worksheetData.line5ShrinkNbr
            : 0,
        line5ShrinkPct: worksheetData?.line5ShrinkPct
            ? worksheetData.line5ShrinkPct * 100
            : 0,
        line5RealGrossProfitNbr: worksheetData?.line5RealGrossProfitNbr
            ? worksheetData.line5RealGrossProfitNbr
            : 0,
        line5RealGrossProfitPct: worksheetData?.line5RealGrossProfitPct
            ? worksheetData.line5RealGrossProfitPct * 100
            : 0,
        line6SuppliesPackagingNbr: worksheetData?.line6SuppliesPackagingNbr
            ? worksheetData.line6SuppliesPackagingNbr
            : 0,
        line7RetailsAllowancesNbr: worksheetData?.line7RetailsAllowancesNbr
            ? worksheetData.line7RetailsAllowancesNbr
            : 0,
        line7RetailsAllowancesPct: worksheetData?.line7RetailsAllowancesPct
            ? worksheetData.line7RetailsAllowancesPct * 100
            : 0,
        line7RetailsSellingAllowancesNbr: worksheetData?.line7RetailsSellingAllowancesNbr
            ? worksheetData.line7RetailsSellingAllowancesNbr
            : 0,
        line7RetailsSellingAllowancesPct: worksheetData?.line7RetailsSellingAllowancesPct
            ? worksheetData.line7RetailsSellingAllowancesPct * 100
            : 0,
        line7RetailsNonSellingAllowancesNbr: worksheetData?.line7RetailsNonSellingAllowancesNbr
            ? worksheetData.line7RetailsNonSellingAllowancesNbr
            : 0,
        line7RetailsNonSellingAllowancesPct: worksheetData?.line7RetailsNonSellingAllowancesPct
            ? worksheetData.line7RetailsNonSellingAllowancesPct * 100
            : 0,
        line8RealGrossProfitNbr: worksheetData?.line8RealGrossProfitNbr
            ? worksheetData.line8RealGrossProfitNbr
            : 0,
        line8RealGrossProfitPct: worksheetData?.line8RealGrossProfitPct
            ? worksheetData.line8RealGrossProfitPct * 100
            : 0,
        updatedTs: worksheetData?.updatedTs,
        sourceTs:worksheetData?.sourceTs,
        fiscalYear: worksheetData?.fiscalYearNbr
            ? worksheetData?.fiscalYearNbr
            : 0,
        fiscalPeriodNbr:
            aggregatedLevel === "Period"
                ? worksheetData?.fiscalPeriodNbr
                    ? worksheetData?.fiscalPeriodNbr
                    : groupedItems
                : 0,
        fiscalWeekNbr:
            aggregatedLevel === "Weeks"
                ? worksheetData?.fiscalWeekNbr
                    ? worksheetData?.fiscalWeekNbr
                    : groupedItems
                : 0,
        sortBy: worksheetData?.fiscalQuarterNbr || 0,
        fiscalWeekEnding: worksheetData?.fiscalWeekEnding
            ? worksheetData.fiscalWeekEnding
            : "",
        comment: worksheetData?.comment ? worksheetData.comment : null,
        forecastType: worksheetData?.forecastType
            ? worksheetData?.forecastType
            : "",
        reason: worksheetData?.reason ? worksheetData.reason : null,
        state: worksheetData?.state ? worksheetData.state : null
    };
    // console.log("worksheetSubRow", worksheetSubRow);
    return worksheetSubRow;
};

const getSortByPeriodOrWeekNbr = (
    subRowType: string,
    groupedItem: any,
    Year: any,
    currentYear: any
) => {
    return subRowType === "Last year actual"
        ? `${currentYear}${groupedItem
            ?.toString()
            ?.slice(Year?.toString()?.length)}`
        : groupedItem;
};
export const worksheetSubRowsTwoDimentionalData = (
    firstData: any,
    secondData: any,
    subRowType: string,
    aggregatedLevel: string,
    yearNbr: string,
    groupedItems: string
) => {
    let worksheetSubRow = {
        aggregatedLevel: aggregatedLevel,
        mainRow:
            aggregatedLevel === "Weeks"
                ? `${aggregatedLevel}${groupedItems
                    ?.toString()
                    .slice(yearNbr?.toString()?.length)}`
                : aggregatedLevel === "Period"
                    ? `${aggregatedLevel}${groupedItems
                        ?.toString()
                        .slice(yearNbr?.toString()?.length)}`
                    : aggregatedLevel,
        subRow: subRowType,
        line1PublicToSalesNbr:
            firstData?.line1PublicToSalesNbr &&
                secondData?.line1PublicToSalesNbr
                ?
                firstData?.line1PublicToSalesNbr -
                secondData?.line1PublicToSalesNbr
                : 0,
        line5BookGrossProfitNbr:
            firstData?.line5BookGrossProfitNbr &&
                secondData?.line5BookGrossProfitNbr
                ?
                firstData?.line5BookGrossProfitNbr -
                secondData?.line5BookGrossProfitNbr
                : 0,
        line5BookGrossProfitPct:
            firstData?.line5BookGrossProfitPct &&
                secondData?.line5BookGrossProfitPct
                ?
                (firstData?.line5BookGrossProfitPct -
                    secondData?.line5BookGrossProfitPct) *
                100
                : 0,
        line5MarkDownsNbr:
            firstData?.line5MarkDownsNbr && secondData?.line5MarkDownsNbr
                ?
                firstData?.line5MarkDownsNbr -
                secondData?.line5MarkDownsNbr
                : 0,
        line5MarkDownsPct:
            firstData?.line5MarkDownsPct && secondData?.line5MarkDownsPct
                ?
                (firstData?.line5MarkDownsPct -
                    secondData?.line5MarkDownsPct) *
                100
                : 0,
        line5ShrinkNbr:
            firstData?.line5ShrinkNbr && secondData?.line5ShrinkNbr
                ?
                firstData?.line5ShrinkNbr - secondData?.line5ShrinkNbr
                : 0,
        line5ShrinkPct:
            firstData?.line5ShrinkPct && secondData?.line5ShrinkPct
                ?
                (firstData?.line5ShrinkPct - secondData?.line5ShrinkPct) *
                100
                : 0,
        line5RealGrossProfitNbr:
            firstData?.line5RealGrossProfitNbr &&
                secondData?.line5RealGrossProfitNbr
                ?
                firstData?.line5RealGrossProfitNbr -
                secondData?.line5RealGrossProfitNbr
                : 0,
        line5RealGrossProfitPct:
            firstData?.line5RealGrossProfitPct &&
                secondData?.line5RealGrossProfitPct
                ?
                (firstData?.line5RealGrossProfitPct -
                    secondData?.line5RealGrossProfitPct) *
                100
                : 0,
        line6SuppliesPackagingNbr:
            firstData?.line6SuppliesPackagingNbr &&
                secondData?.line6SuppliesPackagingNbr
                ?
                firstData?.line6SuppliesPackagingNbr -
                secondData?.line6SuppliesPackagingNbr
                : 0,
        line7RetailsAllowancesNbr:
            firstData?.line7RetailsAllowancesNbr &&
                secondData?.line7RetailsAllowancesNbr
                ?
                firstData?.line7RetailsAllowancesNbr -
                secondData?.line7RetailsAllowancesNbr
                : 0,
        line7RetailsAllowancesPct:
            firstData?.line7RetailsAllowancesPct &&
                secondData?.line7RetailsAllowancesPct
                ?
                (firstData?.line7RetailsAllowancesPct -
                    secondData?.line7RetailsAllowancesPct) *
                100
                : 0,
        line7RetailsSellingAllowancesNbr:
            firstData?.line7RetailsSellingAllowancesNbr &&
                secondData?.line7RetailsSellingAllowancesNbr
                ?
                firstData?.line7RetailsSellingAllowancesNbr -
                secondData?.line7RetailsSellingAllowancesNbr
                : 0,
        line7RetailsSellingAllowancesPct:
            firstData?.line7RetailsSellingAllowancesPct &&
                secondData?.line7RetailsSellingAllowancesPct
                ?
                (firstData?.line7RetailsSellingAllowancesPct -
                    secondData?.line7RetailsSellingAllowancesPct) *
                100
                : 0,
        line7RetailsNonSellingAllowancesNbr:
            firstData?.line7RetailsNonSellingAllowancesNbr &&
                secondData?.line7RetailsNonSellingAllowancesNbr
                ?
                firstData?.line7RetailsNonSellingAllowancesNbr -
                secondData?.line7RetailsNonSellingAllowancesNbr
                : 0,
        line7RetailsNonSellingAllowancesPct:
            firstData?.line7RetailsNonSellingAllowancesPct &&
                secondData?.line7RetailsNonSellingAllowancesPct
                ?
                (firstData?.line7RetailsNonSellingAllowancesPct -
                    secondData?.line7RetailsNonSellingAllowancesPct) *
                100
                : 0,
        line8RealGrossProfitNbr:
            firstData?.line8RealGrossProfitNbr &&
                secondData?.line8RealGrossProfitNbr
                ?
                firstData?.line8RealGrossProfitNbr -
                secondData?.line8RealGrossProfitNbr
                : 0,
        line8RealGrossProfitPct:
            firstData?.line8RealGrossProfitPct &&
                secondData?.line8RealGrossProfitPct
                ?
                (firstData?.line8RealGrossProfitPct -
                    secondData?.line8RealGrossProfitPct) *
                100
                : 0,
        updatedTs: firstData?.updatedTs,
        sourceTs: firstData?.sourceTs,
        fiscalYear: firstData?.fiscalYearNbr ? firstData?.fiscalYearNbr : 0,
        fiscalPeriodNbr:
            aggregatedLevel === "Period"
                ? firstData?.fiscalPeriodNbr
                    ? firstData?.fiscalPeriodNbr
                    : groupedItems
                : 0,

        fiscalWeekNbr:
            aggregatedLevel === "Weeks"
                ? firstData?.fiscalWeekNbr
                    ? firstData?.fiscalWeekNbr
                    : groupedItems
                : 0,

        sortBy: firstData?.fiscalQuarterNbr || 0,
        fiscalWeekEnding: firstData?.fiscalWeekEnding,
        comment: firstData?.comment ? firstData?.comment : "",
        forecastType: firstData?.forecastType ? firstData?.forecastType : "",
        reason: firstData?.reason ? firstData?.reason : "",
        state: firstData?.state ? firstData.state : ""
    };
    return worksheetSubRow;
};
