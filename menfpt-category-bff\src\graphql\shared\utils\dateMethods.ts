const getPreviousYearSameDate = (previousYearQuarterDate: any) => {
    let baseDate = previousYearQuarterDate ? new Date(previousYearQuarterDate) : new Date();
    baseDate.setFullYear(baseDate.getFullYear() - 1);
    let month = String(baseDate.getMonth() + 1).padStart(2, "0");
    let day = String(baseDate.getDate()).padStart(2, "0");
    let year = baseDate.getFullYear();
    return `${month}/${day}/${year}`;
};
export default getPreviousYearSameDate;
