export const worksheetTableRequest = {
    "adjustmentWorksheetReq": {
        "currentFiscalPeriodNbr": 202504,
        "currentFiscalWeekNbr": 202513,
        "currentFiscalYearNbr": 2025,
        "quarterNbr": 202501,
        "deptIds": ["3150000"],
        "divisionIds": [
            "25"
        ],
        "smicCategoryIds": [
            "8501",
            "8503",
            "8515",
            "8520",
            "8523",
            "8525",
            "8545",
            "8546",
            "8550",
            "8555",
            "8560",
            "8561",
            "8572",
            "8580",
            "8590"
        ]
    }
}

export const currentQuarterCalendarData = [
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202501,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202501,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "02/23/2025",
        fiscalWeekEndDate: "03/01/2025",
        fiscalPeriodStartDate: "02/23/2025",
        fiscalPeriodEndDate: "03/22/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202502,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202501,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "03/02/2025",
        fiscalWeekEndDate: "03/08/2025",
        fiscalPeriodStartDate: "02/23/2025",
        fiscalPeriodEndDate: "03/22/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202503,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202501,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "03/09/2025",
        fiscalWeekEndDate: "03/15/2025",
        fiscalPeriodStartDate: "02/23/2025",
        fiscalPeriodEndDate: "03/22/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202504,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202501,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "03/16/2025",
        fiscalWeekEndDate: "03/22/2025",
        fiscalPeriodStartDate: "02/23/2025",
        fiscalPeriodEndDate: "03/22/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202505,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202502,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "03/23/2025",
        fiscalWeekEndDate: "03/29/2025",
        fiscalPeriodStartDate: "03/23/2025",
        fiscalPeriodEndDate: "04/19/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202506,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202502,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "03/30/2025",
        fiscalWeekEndDate: "04/05/2025",
        fiscalPeriodStartDate: "03/23/2025",
        fiscalPeriodEndDate: "04/19/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202507,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202502,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "04/06/2025",
        fiscalWeekEndDate: "04/12/2025",
        fiscalPeriodStartDate: "03/23/2025",
        fiscalPeriodEndDate: "04/19/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202508,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202502,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "04/13/2025",
        fiscalWeekEndDate: "04/19/2025",
        fiscalPeriodStartDate: "03/23/2025",
        fiscalPeriodEndDate: "04/19/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202509,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202503,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "04/20/2025",
        fiscalWeekEndDate: "04/26/2025",
        fiscalPeriodStartDate: "04/20/2025",
        fiscalPeriodEndDate: "05/17/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202510,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202503,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "04/27/2025",
        fiscalWeekEndDate: "05/03/2025",
        fiscalPeriodStartDate: "04/20/2025",
        fiscalPeriodEndDate: "05/17/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202511,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202503,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "05/04/2025",
        fiscalWeekEndDate: "05/10/2025",
        fiscalPeriodStartDate: "04/20/2025",
        fiscalPeriodEndDate: "05/17/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202512,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202503,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "05/11/2025",
        fiscalWeekEndDate: "05/17/2025",
        fiscalPeriodStartDate: "04/20/2025",
        fiscalPeriodEndDate: "05/17/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202513,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202504,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "05/18/2025",
        fiscalWeekEndDate: "05/24/2025",
        fiscalPeriodStartDate: "05/18/2025",
        fiscalPeriodEndDate: "06/14/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202514,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202504,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "05/25/2025",
        fiscalWeekEndDate: "05/31/2025",
        fiscalPeriodStartDate: "05/18/2025",
        fiscalPeriodEndDate: "06/14/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202515,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202504,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "06/01/2025",
        fiscalWeekEndDate: "06/07/2025",
        fiscalPeriodStartDate: "05/18/2025",
        fiscalPeriodEndDate: "06/14/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2025,
        fiscalWeekNumber: 202516,
        fiscalQuarterNumber: 202501,
        fiscalPeriodNumber: 202504,
        fiscalQuarterStartDate: "02/23/2025",
        fiscalQuarterEndDate: "06/14/2025",
        fiscalWeekStartDate: "06/08/2025",
        fiscalWeekEndDate: "06/14/2025",
        fiscalPeriodStartDate: "05/18/2025",
        fiscalPeriodEndDate: "06/14/2025",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
]

export const previousQuarterCalendarInfo = [
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202413,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202404,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "05/19/2024",
        fiscalWeekEndDate: "05/25/2024",
        fiscalPeriodStartDate: "05/19/2024",
        fiscalPeriodEndDate: "06/15/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
]

export const previousQuarterCalendarData = [
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202401,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202401,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "02/25/2024",
        fiscalWeekEndDate: "03/02/2024",
        fiscalPeriodStartDate: "02/25/2024",
        fiscalPeriodEndDate: "03/23/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202402,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202401,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "03/03/2024",
        fiscalWeekEndDate: "03/09/2024",
        fiscalPeriodStartDate: "02/25/2024",
        fiscalPeriodEndDate: "03/23/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202403,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202401,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "03/10/2024",
        fiscalWeekEndDate: "03/16/2024",
        fiscalPeriodStartDate: "02/25/2024",
        fiscalPeriodEndDate: "03/23/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202404,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202401,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "03/17/2024",
        fiscalWeekEndDate: "03/23/2024",
        fiscalPeriodStartDate: "02/25/2024",
        fiscalPeriodEndDate: "03/23/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202405,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202402,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "03/24/2024",
        fiscalWeekEndDate: "03/30/2024",
        fiscalPeriodStartDate: "03/24/2024",
        fiscalPeriodEndDate: "04/20/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202406,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202402,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "03/31/2024",
        fiscalWeekEndDate: "04/06/2024",
        fiscalPeriodStartDate: "03/24/2024",
        fiscalPeriodEndDate: "04/20/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202407,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202402,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "04/07/2024",
        fiscalWeekEndDate: "04/13/2024",
        fiscalPeriodStartDate: "03/24/2024",
        fiscalPeriodEndDate: "04/20/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202408,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202402,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "04/14/2024",
        fiscalWeekEndDate: "04/20/2024",
        fiscalPeriodStartDate: "03/24/2024",
        fiscalPeriodEndDate: "04/20/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202409,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202403,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "04/21/2024",
        fiscalWeekEndDate: "04/27/2024",
        fiscalPeriodStartDate: "04/21/2024",
        fiscalPeriodEndDate: "05/18/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202410,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202403,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "04/28/2024",
        fiscalWeekEndDate: "05/04/2024",
        fiscalPeriodStartDate: "04/21/2024",
        fiscalPeriodEndDate: "05/18/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202411,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202403,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "05/05/2024",
        fiscalWeekEndDate: "05/11/2024",
        fiscalPeriodStartDate: "04/21/2024",
        fiscalPeriodEndDate: "05/18/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202412,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202403,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "05/12/2024",
        fiscalWeekEndDate: "05/18/2024",
        fiscalPeriodStartDate: "04/21/2024",
        fiscalPeriodEndDate: "05/18/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202413,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202404,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "05/19/2024",
        fiscalWeekEndDate: "05/25/2024",
        fiscalPeriodStartDate: "05/19/2024",
        fiscalPeriodEndDate: "06/15/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202414,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202404,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "05/26/2024",
        fiscalWeekEndDate: "06/01/2024",
        fiscalPeriodStartDate: "05/19/2024",
        fiscalPeriodEndDate: "06/15/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202415,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202404,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "06/02/2024",
        fiscalWeekEndDate: "06/08/2024",
        fiscalPeriodStartDate: "05/19/2024",
        fiscalPeriodEndDate: "06/15/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
    {
        fiscalYearNumber: 2024,
        fiscalWeekNumber: 202416,
        fiscalQuarterNumber: 202401,
        fiscalPeriodNumber: 202404,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024",
        fiscalWeekStartDate: "06/09/2024",
        fiscalWeekEndDate: "06/15/2024",
        fiscalPeriodStartDate: "05/19/2024",
        fiscalPeriodEndDate: "06/15/2024",
        createTimestamp: "2025-02-28T08:16:53.360Z",
        updateTimestamp: "2025-02-28T08:16:53.360Z",
    },
]

//for url / menfpt / forecast / aggregate / search reqbody  
const forecastAggregateResponse = {
    "message": "Records found",
    "data": [
        {
            "fiscalYearNbr": 2025,
            "fiscalPeriodNbr": 202504,
            "fiscalQuarterNbr": 202501,
            "fiscalWeekNbr": 202516,
            "smicCategoryId": 8501,
            "deptId": "3150000",
            "divisionId": "25",
            "line1PublicToSalesNbr": 133493.349610704,
            "line5BookGrossProfitNbr": 29469.648751419,
            "line5BookGrossProfitPct": 0.22075743,
            "line1BillOutGrossPct": 0.220006998,
            "line5MarkDownsNbr": -455.338473388,
            "line5MarkDownsPct": -0.003410945,
            "line5ShrinkNbr": -7143.895402977,
            "line5ShrinkPct": -0.053514991,
            "line4CostOfSalesNbr": -111622.934735649,
            "line5RealGrossProfitNbr": 21870.414875054,
            "line5RealGrossProfitPct": 0.163831494,
            "line6SuppliesPackagingNbr": -1146.252393847,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsSellingAllowancesPct": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesPct": 0,
            "line8RealGrossProfitNbr": 20724.162481207,
            "line8RealGrossProfitPct": 0.155244906,
            "forecastType": "DS",
            "state": "PUBLISHED",
            "createdTs": "2025-05-19T07:52:57.933Z",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
            "createdBy": "MENFPT System",
            "updatedBy": "MENFPT System",
            "retailDeptId": "315",
            "reason": "",
            "comment": "",
            "version": 0
        },
        {
            "fiscalYearNbr": 2025,
            "fiscalPeriodNbr": 202504,
            "fiscalQuarterNbr": 202501,
            "fiscalWeekNbr": 202516,
            "smicCategoryId": 8501,
            "deptId": "3150000",
            "divisionId": "25",
            "line1PublicToSalesNbr": 135423.177114899,
            "line5BookGrossProfitNbr": 45150.88426035,
            "line5BookGrossProfitPct": 0.333405885,
            "line1BillOutGrossPct": 0.331270752,
            "line5MarkDownsNbr": -434.264514078,
            "line5MarkDownsPct": -0.003206722,
            "line5ShrinkNbr": -7276.224758697,
            "line5ShrinkPct": -0.053729538,
            "line4CostOfSalesNbr": -108085.799861515,
            "line5RealGrossProfitNbr": 37440.394987575,
            "line5RealGrossProfitPct": 0.276469625,
            "line6SuppliesPackagingNbr": -1143.734251112,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsSellingAllowancesPct": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesPct": 0,
            "line8RealGrossProfitNbr": 36296.660736463,
            "line8RealGrossProfitPct": 0.268023993,
            "forecastType": "FA",
            "state": "PUBLISHED",
            "createdTs": "2025-05-19T07:54:19.193Z",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
            "createdBy": "MENFPT System",
            "updatedBy": "MENFPT System",
            "retailDeptId": "315",
            "reason": "",
            "comment": "",
            "version": 0
        }
    ],
    "timestamp": "2025-05-19T19:13:35.376Z"
}

//for url / menfpt / forecast / actuals / search reqbody  
const actualsResponseQtr = {
    "message": "Records found",
    "data": [
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 51758273.022415115,
            "line5BookGrossProfitNbr": 30112605.974362137,
            "line5BookGrossProfitPct": 0.581793,
            "line5MarkDownsNbr": -664772.540220713,
            "line5MarkDownsPct": -0.012843,
            "line5ShrinkNbr": -4455376.480264919,
            "line5ShrinkPct": -0.08608,
            "line4CostOfSalesNbr": -42147900.092472498,
            "line5RealGrossProfitNbr": 24992456.953876505,
            "line5RealGrossProfitPct": 0.482868,
            "line6SuppliesPackagingNbr": -405777.821944142,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 24586679.131932363,
            "line8RealGrossProfitPct": 0.475028,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        },
        {
            "fiscalQuarterNbr": 202401,
            "line1PublicToSalesNbr": 63410714.247278105,
            "line5BookGrossProfitNbr": 36769046.248864916,
            "line5BookGrossProfitPct": 0.579855,
            "line5MarkDownsNbr": -798397.718078127,
            "line5MarkDownsPct": -0.01259,
            "line5ShrinkNbr": -4320893.860910897,
            "line5ShrinkPct": -0.068141,
            "line4CostOfSalesNbr": 0,
            "line5RealGrossProfitNbr": 31649754.669875892,
            "line5RealGrossProfitPct": 0.499123,
            "line6SuppliesPackagingNbr": -451007.636147569,
            "line7RetailsAllowancesNbr": 12268.82,
            "line7RetailsAllowancesPct": 0.000193,
            "line8RealGrossProfitNbr": 31211015.853728323,
            "line8RealGrossProfitPct": 0.492204,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        }
    ],
    "timestamp": "2025-05-19T19:20:15.275Z"
}

//for url / menfpt / forecast / actuals / search reqbody  
const actualsResponseQTD = {
    "message": "Records found",
    "data": [
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 51758273.022415115,
            "line5BookGrossProfitNbr": 30112605.974362137,
            "line5BookGrossProfitPct": 0.581793,
            "line5MarkDownsNbr": -664772.540220713,
            "line5MarkDownsPct": -0.012843,
            "line5ShrinkNbr": -4455376.480264919,
            "line5ShrinkPct": -0.08608,
            "line4CostOfSalesNbr": -34382547.735367425,
            "line5RealGrossProfitNbr": 24992456.953876505,
            "line5RealGrossProfitPct": 0.482868,
            "line6SuppliesPackagingNbr": -405777.821944142,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 24586679.131932363,
            "line8RealGrossProfitPct": 0.475028,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        },
        {
            "fiscalQuarterNbr": 202401,
            "line1PublicToSalesNbr": 63410714.247278105,
            "line5BookGrossProfitNbr": 36769046.248864916,
            "line5BookGrossProfitPct": 0.579855,
            "line5MarkDownsNbr": -798397.718078127,
            "line5MarkDownsPct": -0.01259,
            "line5ShrinkNbr": -4320893.860910897,
            "line5ShrinkPct": -0.068141,
            "line4CostOfSalesNbr": 0,
            "line5RealGrossProfitNbr": 31649754.669875892,
            "line5RealGrossProfitPct": 0.499123,
            "line6SuppliesPackagingNbr": -451007.636147569,
            "line7RetailsAllowancesNbr": 12268.82,
            "line7RetailsAllowancesPct": 0.000193,
            "line8RealGrossProfitNbr": 31211015.853728323,
            "line8RealGrossProfitPct": 0.492204,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        }
    ],
    "timestamp": "2025-05-19T19:21:36.907Z"
}

//for url / menfpt / forecast / actuals / search reqbody  
const actualsResponsePrd = {
    "message": "Records found",
    "data": [
        {
            "fiscalPeriodNbr": 202501,
            "line1PublicToSalesNbr": 13828946.734645027,
            "line5BookGrossProfitNbr": 7904436.116278392,
            "line5BookGrossProfitPct": 0.571586,
            "line5MarkDownsNbr": -183473.447953311,
            "line5MarkDownsPct": -0.013267,
            "line5ShrinkNbr": -1408592.163526798,
            "line5ShrinkPct": -0.101858,
            "line4CostOfSalesNbr": -7104100.94894424,
            "line5RealGrossProfitNbr": 6312370.504798283,
            "line5RealGrossProfitPct": 0.45646,
            "line6SuppliesPackagingNbr": -105116.13888218,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 6207254.365916103,
            "line8RealGrossProfitPct": 0.448859,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        },
        {
            "fiscalPeriodNbr": 202401,
            "line1PublicToSalesNbr": 13816566.558263978,
            "line5BookGrossProfitNbr": 7890588.402838236,
            "line5BookGrossProfitPct": 0.571096,
            "line5MarkDownsNbr": -195336.657539084,
            "line5MarkDownsPct": -0.014137,
            "line5ShrinkNbr": -1041039.190362915,
            "line5ShrinkPct": -0.075347,
            "line4CostOfSalesNbr": 0,
            "line5RealGrossProfitNbr": 6654212.554936237,
            "line5RealGrossProfitPct": 0.481611,
            "line6SuppliesPackagingNbr": -118145.348190099,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 6536067.206746138,
            "line8RealGrossProfitPct": 0.47306,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        }
    ],
    "timestamp": "2025-05-19T19:22:22.089Z"
}

//for url / menfpt / forecast / actuals / search reqbody  
const actualsResponseWk = {
    "message": "Records found",
    "data": [
        {
            "fiscalWeekNbr": 202501,
            "line1PublicToSalesNbr": 3163472.278127713,
            "line5BookGrossProfitNbr": 1819993.943171227,
            "line5BookGrossProfitPct": 0.575315,
            "line5MarkDownsNbr": -56108.144510061,
            "line5MarkDownsPct": -0.017736,
            "line5ShrinkNbr": -283214.778062249,
            "line5ShrinkPct": -0.089526,
            "line4CostOfSalesNbr": -1754794.084395129,
            "line5RealGrossProfitNbr": 1480671.020598917,
            "line5RealGrossProfitPct": 0.468052,
            "line6SuppliesPackagingNbr": -22494.565597529,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 1458176.455001388,
            "line8RealGrossProfitPct": 0.460941,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        },
        {
            "fiscalWeekNbr": 202401,
            "line1PublicToSalesNbr": 3020379.852800774,
            "line5BookGrossProfitNbr": 1741611.156075156,
            "line5BookGrossProfitPct": 0.576619,
            "line5MarkDownsNbr": -43391.659209254,
            "line5MarkDownsPct": -0.014366,
            "line5ShrinkNbr": -303628.698533352,
            "line5ShrinkPct": -0.100526,
            "line4CostOfSalesNbr": 0,
            "line5RealGrossProfitNbr": 1394590.79833255,
            "line5RealGrossProfitPct": 0.461726,
            "line6SuppliesPackagingNbr": -24596.593207616,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 1369994.205124934,
            "line8RealGrossProfitPct": 0.453583,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        }
    ],
    "timestamp": "2025-05-19T19:25:57.567Z"
}

//for url / menfpt / forecast / projection / search reqbody  
const projectionResponseQtr = {
    "message": "Records found",
    "data": [
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 83582368.00936197,
            "line5BookGrossProfitNbr": 48997564.65808272,
            "line5BookGrossProfitPct": 0.586218,
            "line5MarkDownsNbr": -972438.195384615,
            "line5MarkDownsPct": -0.011634,
            "line5ShrinkNbr": -5845116.338019073,
            "line5ShrinkPct": -0.069932,
            "line4CostOfSalesNbr": -42147900.092472489,
            "line5RealGrossProfitNbr": 42180010.124679029,
            "line5RealGrossProfitPct": 0.504652,
            "line6SuppliesPackagingNbr": -616710.540261546,
            "line7RetailsAllowancesNbr": -0.261249378,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 41563299.3231681,
            "line8RealGrossProfitPct": 0.497273,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        }
    ],
    "timestamp": "2025-05-19T19:31:16.227Z"
}

//for url / menfpt / forecast / projection / search reqbody  
const projectionResponseQTD = {
    "message": "Records found",
    "data": [
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 69197128.040021055,
            "line5BookGrossProfitNbr": 40586013.639995162,
            "line5BookGrossProfitPct": 0.586527,
            "line5MarkDownsNbr": -853899.259937264,
            "line5MarkDownsPct": -0.01234,
            "line5ShrinkNbr": -4888538.681324942,
            "line5ShrinkPct": -0.070646,
            "line4CostOfSalesNbr": -34382547.735367419,
            "line5RealGrossProfitNbr": 34843575.698732954,
            "line5RealGrossProfitPct": 0.50354,
            "line6SuppliesPackagingNbr": -517741.292693649,
            "line7RetailsAllowancesNbr": -0.261249378,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 34325834.144789918,
            "line8RealGrossProfitPct": 0.496058,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        }
    ],
    "timestamp": "2025-05-19T19:34:37.850Z"
}


//for url / menfpt / forecast / projection / search reqbody  
const projectionResponsePrd = {
    "message": "Records found",
    "data": [
        {
            "fiscalPeriodNbr": 202501,
            "line1PublicToSalesNbr": 14458044.464551864,
            "line5BookGrossProfitNbr": 8464732.995117499,
            "line5BookGrossProfitPct": 0.585468,
            "line5MarkDownsNbr": -196312.887532455,
            "line5MarkDownsPct": -0.013578,
            "line5ShrinkNbr": -1074682.134188997,
            "line5ShrinkPct": -0.074331,
            "line4CostOfSalesNbr": -7104100.94894424,
            "line5RealGrossProfitNbr": 7193737.973396045,
            "line5RealGrossProfitPct": 0.497559,
            "line6SuppliesPackagingNbr": -121923.165809109,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 7071814.807586927,
            "line8RealGrossProfitPct": 0.489126,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        }
    ],
    "timestamp": "2025-05-19T19:57:55.052Z"
}

//for url / menfpt / forecast / projection / search reqbody  
const projectionResponseWk = {
    "message": "Records found",
    "data": [
        {
            "fiscalWeekNbr": 202501,
            "line1PublicToSalesNbr": 3413120.488070837,
            "line5BookGrossProfitNbr": 1947136.411233707,
            "line5BookGrossProfitPct": 0.570485,
            "line5MarkDownsNbr": -48420.117133874,
            "line5MarkDownsPct": -0.014186,
            "line5ShrinkNbr": -339861.727758627,
            "line5ShrinkPct": -0.099575,
            "line4CostOfSalesNbr": -1754794.084395129,
            "line5RealGrossProfitNbr": 1558854.566341203,
            "line5RealGrossProfitPct": 0.456724,
            "line6SuppliesPackagingNbr": -27244.442797675,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line8RealGrossProfitNbr": 1531610.123543523,
            "line8RealGrossProfitPct": 0.448741,
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        }
    ],
    "timestamp": "2025-05-19T19:59:42.285Z"
}

//for url / menfpt / forecast / aggregate / search reqbody  
const forecastResponseQtr = {
    "message": "Records found",
    "data": [
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 83582368.00936197,
            "line5BookGrossProfitNbr": 48997564.65808272,
            "line5BookGrossProfitPct": 0.586218,
            "line5MarkDownsNbr": -972438.195384615,
            "line5MarkDownsPct": -0.011634,
            "line5ShrinkNbr": -5845116.338019073,
            "line5ShrinkPct": -0.069932,
            "line4CostOfSalesNbr": -42147900.092472489,
            "line5RealGrossProfitNbr": 42180010.124679029,
            "line5RealGrossProfitPct": 0.504652,
            "line6SuppliesPackagingNbr": -616710.540261546,
            "line7RetailsAllowancesNbr": -0.261249378,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 41563299.3231681,
            "line8RealGrossProfitPct": 0.497273,
            "forecastType": "Base",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        },
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 82767651.644348588,
            "line5BookGrossProfitNbr": 32423652.280535618,
            "line5BookGrossProfitPct": 0.391743,
            "line5MarkDownsNbr": -998924.156921628,
            "line5MarkDownsPct": -0.012069,
            "line5ShrinkNbr": -5772443.975675487,
            "line5ShrinkPct": -0.069742,
            "line4CostOfSalesNbr": -57110226.379361537,
            "line5RealGrossProfitNbr": 25652284.147938503,
            "line5RealGrossProfitPct": 0.309931,
            "line6SuppliesPackagingNbr": -612031.691462056,
            "line7RetailsAllowancesNbr": -7.824174101,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 25040244.632302346,
            "line8RealGrossProfitPct": 0.302536,
            "forecastType": "DS",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        },
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 83616499.21426197,
            "line5BookGrossProfitNbr": 49017036.022206065,
            "line5BookGrossProfitPct": 0.586212,
            "line5MarkDownsNbr": -972922.396556224,
            "line5MarkDownsPct": -0.011635,
            "line5ShrinkNbr": -5848514.955298581,
            "line5ShrinkPct": -0.069944,
            "line4CostOfSalesNbr": -42265914.589034769,
            "line5RealGrossProfitNbr": 42195598.670351256,
            "line5RealGrossProfitPct": 0.504632,
            "line6SuppliesPackagingNbr": -616710.540264271,
            "line7RetailsAllowancesNbr": -0.261249378,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 41578887.868837607,
            "line8RealGrossProfitPct": 0.497256,
            "forecastType": "FA",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        }
    ],
    "timestamp": "2025-05-19T20:01:32.554Z"
}

//for url / menfpt / forecast / aggregate / search reqbody  
const forecastResponseQTD = {
    "message": "Records found",
    "data": [
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 68836372.56614334,
            "line5BookGrossProfitNbr": 26975711.866892597,
            "line5BookGrossProfitPct": 0.391881,
            "line5MarkDownsNbr": -881195.422392134,
            "line5MarkDownsPct": -0.012801,
            "line5ShrinkNbr": -4849033.113780094,
            "line5ShrinkPct": -0.070442,
            "line4CostOfSalesNbr": -47589095.956309351,
            "line5RealGrossProfitNbr": 21245483.330720369,
            "line5RealGrossProfitPct": 0.308637,
            "line6SuppliesPackagingNbr": -514659.765000182,
            "line7RetailsAllowancesNbr": -7.824174101,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 20730815.741546086,
            "line8RealGrossProfitPct": 0.30116,
            "forecastType": "DS",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        },
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 69197128.040021055,
            "line5BookGrossProfitNbr": 40586013.639995162,
            "line5BookGrossProfitPct": 0.586527,
            "line5MarkDownsNbr": -853899.259937264,
            "line5MarkDownsPct": -0.01234,
            "line5ShrinkNbr": -4888538.681324942,
            "line5ShrinkPct": -0.070646,
            "line4CostOfSalesNbr": -34382547.735367419,
            "line5RealGrossProfitNbr": 34843575.698732954,
            "line5RealGrossProfitPct": 0.50354,
            "line6SuppliesPackagingNbr": -517741.292693649,
            "line7RetailsAllowancesNbr": -0.261249378,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 34325834.144789918,
            "line8RealGrossProfitPct": 0.496058,
            "forecastType": "Base",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        },
        {
            "fiscalQuarterNbr": 202501,
            "line1PublicToSalesNbr": 69231259.244921055,
            "line5BookGrossProfitNbr": 40605485.004118507,
            "line5BookGrossProfitPct": 0.586519,
            "line5MarkDownsNbr": -854383.461108873,
            "line5MarkDownsPct": -0.012341,
            "line5ShrinkNbr": -4891937.29860445,
            "line5ShrinkPct": -0.07066,
            "line4CostOfSalesNbr": -34500562.231929699,
            "line5RealGrossProfitNbr": 34859164.244405181,
            "line5RealGrossProfitPct": 0.503517,
            "line6SuppliesPackagingNbr": -517741.292696374,
            "line7RetailsAllowancesNbr": -0.261249378,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 34341422.690459425,
            "line8RealGrossProfitPct": 0.496039,
            "forecastType": "FA",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        }
    ],
    "timestamp": "2025-05-19T20:02:52.624Z"
}

//for url / menfpt / forecast / aggregate / search reqbody  
const forecastResponsePrd = {
    "message": "Records found",
    "data": [
        {
            "fiscalPeriodNbr": 202501,
            "line1PublicToSalesNbr": 14458044.464551864,
            "line5BookGrossProfitNbr": 8464732.995117499,
            "line5BookGrossProfitPct": 0.585468,
            "line5MarkDownsNbr": -196312.887532455,
            "line5MarkDownsPct": -0.013578,
            "line5ShrinkNbr": -1074682.134188997,
            "line5ShrinkPct": -0.074331,
            "line4CostOfSalesNbr": -7104100.94894424,
            "line5RealGrossProfitNbr": 7193737.973396045,
            "line5RealGrossProfitPct": 0.497559,
            "line6SuppliesPackagingNbr": -121923.165809109,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 7071814.807586927,
            "line8RealGrossProfitPct": 0.489126,
            "forecastType": "Base",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        },
        {
            "fiscalPeriodNbr": 202501,
            "line1PublicToSalesNbr": 13755492.602602863,
            "line5BookGrossProfitNbr": 5366071.263802907,
            "line5BookGrossProfitPct": 0.390103,
            "line5MarkDownsNbr": -198970.296959694,
            "line5MarkDownsPct": -0.014464,
            "line5ShrinkNbr": -1027142.681197896,
            "line5ShrinkPct": -0.074671,
            "line4CostOfSalesNbr": -9614493.534232231,
            "line5RealGrossProfitNbr": 4139958.285645317,
            "line5RealGrossProfitPct": 0.300967,
            "line6SuppliesPackagingNbr": -118056.01687578,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 4021902.268769537,
            "line8RealGrossProfitPct": 0.292385,
            "forecastType": "DS",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": ""
        },
        {
            "fiscalPeriodNbr": 202501,
            "line1PublicToSalesNbr": 14492175.669451864,
            "line5BookGrossProfitNbr": 8484204.359240844,
            "line5BookGrossProfitPct": 0.585433,
            "line5MarkDownsNbr": -196797.088704064,
            "line5MarkDownsPct": -0.013579,
            "line5ShrinkNbr": -1078080.751468505,
            "line5ShrinkPct": -0.07439,
            "line4CostOfSalesNbr": -7222115.44550652,
            "line5RealGrossProfitNbr": 7209326.519068272,
            "line5RealGrossProfitPct": 0.497463,
            "line6SuppliesPackagingNbr": -121923.165811834,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 7087403.353256434,
            "line8RealGrossProfitPct": 0.48905,
            "forecastType": "FA",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        }

    ],
    "timestamp": "2025-05-19T20:03:45.492Z"
}

//for url / menfpt / forecast / aggregate / search reqbody  
const forecastResponseWk = {
    "message": "Records found",
    "data": [
        {
            "fiscalWeekNbr": 202501,
            "line1PublicToSalesNbr": 3413120.488070837,
            "line5BookGrossProfitNbr": 1947136.411233707,
            "line5BookGrossProfitPct": 0.570485,
            "line5MarkDownsNbr": -48420.117133874,
            "line5MarkDownsPct": -0.014186,
            "line5ShrinkNbr": -339861.727758627,
            "line5ShrinkPct": -0.099575,
            "line4CostOfSalesNbr": -1754794.084395129,
            "line5RealGrossProfitNbr": 1558854.566341203,
            "line5RealGrossProfitPct": 0.456724,
            "line6SuppliesPackagingNbr": -27244.442797675,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 1531610.123543523,
            "line8RealGrossProfitPct": 0.448741,
            "forecastType": "Base",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        },
        {
            "fiscalWeekNbr": 202501,
            "line1PublicToSalesNbr": 3223997.330837001,
            "line5BookGrossProfitNbr": 1240363.489853184,
            "line5BookGrossProfitPct": 0.384728,
            "line5MarkDownsNbr": -49504.297679971,
            "line5MarkDownsPct": -0.015354,
            "line5ShrinkNbr": -322442.019032692,
            "line5ShrinkPct": -0.100013,
            "line4CostOfSalesNbr": -2355580.157696479,
            "line5RealGrossProfitNbr": 868417.173140521,
            "line5RealGrossProfitPct": 0.26936,
            "line6SuppliesPackagingNbr": -26208.715765679,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 842208.457374842,
            "line8RealGrossProfitPct": 0.261231,
            "forecastType": "DS",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        },
        {
            "fiscalWeekNbr": 202501,
            "line1PublicToSalesNbr": 3447251.692970837,
            "line5BookGrossProfitNbr": 1966607.775357052,
            "line5BookGrossProfitPct": 0.570485,
            "line5MarkDownsNbr": -48904.318305483,
            "line5MarkDownsPct": -0.014186,
            "line5ShrinkNbr": -343260.345038135,
            "line5ShrinkPct": -0.099575,
            "line4CostOfSalesNbr": -1872808.580957409,
            "line5RealGrossProfitNbr": 1574443.11201343,
            "line5RealGrossProfitPct": 0.456724,
            "line6SuppliesPackagingNbr": -27244.4428004,
            "line7RetailsAllowancesNbr": 0,
            "line7RetailsAllowancesPct": 0,
            "line7RetailsSellingAllowancesNbr": 0,
            "line7RetailsNonSellingAllowancesNbr": 0,
            "line8RealGrossProfitNbr": 1547198.66921303,
            "line8RealGrossProfitPct": 0.44882,
            "forecastType": "FA",
            "state": "PUBLISHED",
            "updatedTs": "2025-05-19T07:52:57.933Z",
            "sourceTs": "",
        }
    ],
    "timestamp": "2025-05-19T20:05:59.089Z"
}

export const expectedResponse = [
    {
        aggregatedLevel: "Quarter to Date",
        mainRow: "Quarter to Date",
        subRow: "Last year actual",
        line1PublicToSalesNbr: 63410714.2472781,
        line5BookGrossProfitNbr: 36769046.24886492,
        line5BookGrossProfitPct: 57.9855,
        line5MarkDownsNbr: -798397.718078127,
        line5MarkDownsPct: -1.2590000000000001,
        line5ShrinkNbr: -4320893.860910897,
        line5ShrinkPct: -6.814099999999999,
        line5RealGrossProfitNbr: 31649754.669875894,
        line5RealGrossProfitPct: 49.9123,
        line6SuppliesPackagingNbr: -451007.636147569,
        line7RetailsAllowancesNbr: 12268.82,
        line7RetailsAllowancesPct: 0.0193,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 31211015.853728324,
        line8RealGrossProfitPct: 49.2204,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202401,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
    },
    {
        aggregatedLevel: "Period",
        mainRow: "Period01",
        subRow: "Last year actual",
        line1PublicToSalesNbr: 13816566.558263978,
        line5BookGrossProfitNbr: 7890588.402838236,
        line5BookGrossProfitPct: 57.10960000000001,
        line5MarkDownsNbr: -195336.657539084,
        line5MarkDownsPct: -1.4137,
        line5ShrinkNbr: -1041039.190362915,
        line5ShrinkPct: -7.5347,
        line5RealGrossProfitNbr: 6654212.554936237,
        line5RealGrossProfitPct: 48.161100000000005,
        line6SuppliesPackagingNbr: -118145.348190099,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 6536067.206746138,
        line8RealGrossProfitPct: 47.306,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202401,
        fiscalWeekNbr: 0,
        sortBy: 202401,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
        fiscalQuarterNbr: 202401,
    },
    {
        aggregatedLevel: "Weeks",
        mainRow: "Weeks01",
        subRow: "Last year actual",
        line1PublicToSalesNbr: 3020379.852800774,
        line5BookGrossProfitNbr: 1741611.156075156,
        line5BookGrossProfitPct: 57.6619,
        line5MarkDownsNbr: -43391.659209254,
        line5MarkDownsPct: -1.4366,
        line5ShrinkNbr: -303628.698533352,
        line5ShrinkPct: -10.0526,
        line5RealGrossProfitNbr: 1394590.79833255,
        line5RealGrossProfitPct: 46.1726,
        line6SuppliesPackagingNbr: -24596.593207616,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 1369994.205124934,
        line8RealGrossProfitPct: 45.3583,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202401,
        fiscalWeekNbr: 202401,
        sortBy: 202401,
        fiscalWeekEnding: "03/02/2024",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
    },
    {
        aggregatedLevel: "Quarter",
        mainRow: "Quarter",
        subRow: "FCST to PROJ",
        line1PublicToSalesNbr: 0,
        line5BookGrossProfitNbr: 0,
        line5BookGrossProfitPct: 0,
        line5MarkDownsNbr: 0,
        line5MarkDownsPct: 0,
        line5ShrinkNbr: 0,
        line5ShrinkPct: 0,
        line5RealGrossProfitNbr: 0,
        line5RealGrossProfitPct: 0,
        line6SuppliesPackagingNbr: 0,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 0,
        line8RealGrossProfitPct: 0,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: undefined,
        comment: "",
        forecastType: "FA",
        reason: "",
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Quarter",
        mainRow: "Quarter",
        subRow: "Projection",
        line1PublicToSalesNbr: 135423.177114899,
        line5BookGrossProfitNbr: 45150.88426035,
        line5BookGrossProfitPct: 33.3405885,
        line5MarkDownsNbr: -434.264514078,
        line5MarkDownsPct: -0.3206722,
        line5ShrinkNbr: -7276.224758697,
        line5ShrinkPct: -5.3729538,
        line5RealGrossProfitNbr: 37440.394987575,
        line5RealGrossProfitPct: 27.6469625,
        line6SuppliesPackagingNbr: -1143.734251112,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 36296.660736463,
        line8RealGrossProfitPct: 26.8023993,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "FA",
        reason: null,
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Quarter",
        mainRow: "Quarter",
        subRow: "DS Forecast",
        line1PublicToSalesNbr: 133493.349610704,
        line5BookGrossProfitNbr: 29469.648751419,
        line5BookGrossProfitPct: 22.075743,
        line5MarkDownsNbr: -455.338473388,
        line5MarkDownsPct: -0.34109449999999997,
        line5ShrinkNbr: -7143.895402977,
        line5ShrinkPct: -5.3514991,
        line5RealGrossProfitNbr: 21870.414875054,
        line5RealGrossProfitPct: 16.3831494,
        line6SuppliesPackagingNbr: -1146.252393847,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 20724.162481207,
        line8RealGrossProfitPct: 15.524490599999998,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "DS",
        reason: null,
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Quarter",
        mainRow: "Quarter",
        subRow: "Merch. Forecast",
        line1PublicToSalesNbr: 135423.177114899,
        line5BookGrossProfitNbr: 45150.88426035,
        line5BookGrossProfitPct: 33.3405885,
        line5MarkDownsNbr: -434.264514078,
        line5MarkDownsPct: -0.3206722,
        line5ShrinkNbr: -7276.224758697,
        line5ShrinkPct: -5.3729538,
        line5RealGrossProfitNbr: 37440.394987575,
        line5RealGrossProfitPct: 27.6469625,
        line6SuppliesPackagingNbr: -1143.734251112,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 36296.660736463,
        line8RealGrossProfitPct: 26.8023993,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "FA",
        reason: null,
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Quarter to Date",
        mainRow: "Quarter to Date",
        subRow: "FCST to PROJ",
        line1PublicToSalesNbr: 34131.20489999652,
        line5BookGrossProfitNbr: 19471.36412334442,
        line5BookGrossProfitPct: -0.0008000000000008001,
        line5MarkDownsNbr: -484.201171608991,
        line5MarkDownsPct: -0.00009999999999992654,
        line5ShrinkNbr: -3398.617279508151,
        line5ShrinkPct: -0.0014000000000000123,
        line5RealGrossProfitNbr: 15588.545672222972,
        line5RealGrossProfitPct: -0.0022999999999995246,
        line6SuppliesPackagingNbr: -0.0000027249916456639767,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 15588.54566950351,
        line8RealGrossProfitPct: -0.0018999999999991246,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: undefined,
        comment: "",
        forecastType: "FA",
        reason: "",
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Quarter to Date",
        mainRow: "Quarter to Date",
        subRow: "Projection",
        line1PublicToSalesNbr: 69197128.04002106,
        line5BookGrossProfitNbr: 40586013.639995165,
        line5BookGrossProfitPct: 58.6527,
        line5MarkDownsNbr: -853899.259937264,
        line5MarkDownsPct: -1.234,
        line5ShrinkNbr: -4888538.681324942,
        line5ShrinkPct: -7.0646,
        line5RealGrossProfitNbr: 34843575.69873296,
        line5RealGrossProfitPct: 50.354,
        line6SuppliesPackagingNbr: -517741.292693649,
        line7RetailsAllowancesNbr: -0.261249378,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 34325834.14478992,
        line8RealGrossProfitPct: 49.6058,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
    },
    {
        aggregatedLevel: "Quarter to Date",
        mainRow: "Quarter to Date",
        subRow: "DS Forecast",
        line1PublicToSalesNbr: 68836372.56614333,
        line5BookGrossProfitNbr: 26975711.8668926,
        line5BookGrossProfitPct: 39.1881,
        line5MarkDownsNbr: -881195.422392134,
        line5MarkDownsPct: -1.2801,
        line5ShrinkNbr: -4849033.113780094,
        line5ShrinkPct: -7.0442,
        line5RealGrossProfitNbr: 21245483.33072037,
        line5RealGrossProfitPct: 30.863699999999998,
        line6SuppliesPackagingNbr: -514659.765000182,
        line7RetailsAllowancesNbr: -7.824174101,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 20730815.741546087,
        line8RealGrossProfitPct: 30.116,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "DS",
        reason: null,
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Quarter to Date",
        mainRow: "Quarter to Date",
        subRow: "Merch. Forecast",
        line1PublicToSalesNbr: 69231259.24492106,
        line5BookGrossProfitNbr: 40605485.00411851,
        line5BookGrossProfitPct: 58.6519,
        line5MarkDownsNbr: -854383.461108873,
        line5MarkDownsPct: -1.2341,
        line5ShrinkNbr: -4891937.29860445,
        line5ShrinkPct: -7.066,
        line5RealGrossProfitNbr: 34859164.24440518,
        line5RealGrossProfitPct: 50.3517,
        line6SuppliesPackagingNbr: -517741.292696374,
        line7RetailsAllowancesNbr: -0.261249378,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 34341422.69045942,
        line8RealGrossProfitPct: 49.6039,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "FA",
        reason: null,
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Quarter to Date",
        mainRow: "Quarter to Date",
        subRow: "Actual to Date",
        line1PublicToSalesNbr: 51758273.02241512,
        line5BookGrossProfitNbr: 30112605.97436214,
        line5BookGrossProfitPct: 58.1793,
        line5MarkDownsNbr: -664772.540220713,
        line5MarkDownsPct: -1.2843,
        line5ShrinkNbr: -4455376.480264919,
        line5ShrinkPct: -8.608,
        line5RealGrossProfitNbr: 24992456.953876507,
        line5RealGrossProfitPct: 48.2868,
        line6SuppliesPackagingNbr: -405777.821944142,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 24586679.131932363,
        line8RealGrossProfitPct: 47.5028,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 0,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
    },
    {
        aggregatedLevel: "Period",
        mainRow: "Period01",
        subRow: "ACT to PROJ",
        line1PublicToSalesNbr: -629097.7299068365,
        line5BookGrossProfitNbr: -560296.8788391063,
        line5BookGrossProfitPct: -1.388199999999995,
        line5MarkDownsNbr: 12839.439579144004,
        line5MarkDownsPct: 0.03110000000000005,
        line5ShrinkNbr: -333910.0293378008,
        line5ShrinkPct: -2.752700000000001,
        line5RealGrossProfitNbr: -881367.4685977623,
        line5RealGrossProfitPct: -4.1099,
        line6SuppliesPackagingNbr: 16807.026926929,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: -864560.4416708238,
        line8RealGrossProfitPct: -4.0267,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: undefined,
        comment: "",
        forecastType: "",
        reason: "",
        state: "",
        fiscalQuarterNbr: 202501,
    },
    {
        aggregatedLevel: "Period",
        mainRow: "Period01",
        subRow: "FCST to PROJ",
        line1PublicToSalesNbr: 34131.204900000244,
        line5BookGrossProfitNbr: 19471.364123346284,
        line5BookGrossProfitPct: -0.0035000000000007248,
        line5MarkDownsNbr: -484.201171608991,
        line5MarkDownsPct: -0.00010000000000010001,
        line5ShrinkNbr: -3398.6172795079183,
        line5ShrinkPct: -0.005900000000000349,
        line5RealGrossProfitNbr: 15588.545672226697,
        line5RealGrossProfitPct: -0.009599999999998499,
        line6SuppliesPackagingNbr: -0.0000027249916456639767,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 15588.545669507235,
        line8RealGrossProfitPct: -0.0076000000000020496,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: undefined,
        comment: "",
        forecastType: "FA",
        reason: "",
        state: "PUBLISHED",
        fiscalQuarterNbr: 202501,
    },
    {
        aggregatedLevel: "Period",
        mainRow: "Period01",
        subRow: "Projection",
        line1PublicToSalesNbr: 14458044.464551864,
        line5BookGrossProfitNbr: 8464732.995117499,
        line5BookGrossProfitPct: 58.5468,
        line5MarkDownsNbr: -196312.887532455,
        line5MarkDownsPct: -1.3578,
        line5ShrinkNbr: -1074682.134188997,
        line5ShrinkPct: -7.4331,
        line5RealGrossProfitNbr: 7193737.973396045,
        line5RealGrossProfitPct: 49.7559,
        line6SuppliesPackagingNbr: -121923.165809109,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 7071814.807586927,
        line8RealGrossProfitPct: 48.9126,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
        fiscalQuarterNbr: 202501,
    },
    {
        aggregatedLevel: "Period",
        mainRow: "Period01",
        subRow: "DS Forecast",
        line1PublicToSalesNbr: 13755492.602602864,
        line5BookGrossProfitNbr: 5366071.263802907,
        line5BookGrossProfitPct: 39.0103,
        line5MarkDownsNbr: -198970.296959694,
        line5MarkDownsPct: -1.4464,
        line5ShrinkNbr: -1027142.681197896,
        line5ShrinkPct: -7.4671,
        line5RealGrossProfitNbr: 4139958.285645317,
        line5RealGrossProfitPct: 30.0967,
        line6SuppliesPackagingNbr: -118056.01687578,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 4021902.268769537,
        line8RealGrossProfitPct: 29.238500000000002,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "DS",
        reason: null,
        state: "PUBLISHED",
        fiscalQuarterNbr: 202501,
    },
    {
        aggregatedLevel: "Period",
        mainRow: "Period01",
        subRow: "Merch. Forecast",
        line1PublicToSalesNbr: 14492175.669451864,
        line5BookGrossProfitNbr: 8484204.359240845,
        line5BookGrossProfitPct: 58.543299999999995,
        line5MarkDownsNbr: -196797.088704064,
        line5MarkDownsPct: -1.3579,
        line5ShrinkNbr: -1078080.751468505,
        line5ShrinkPct: -7.439,
        line5RealGrossProfitNbr: 7209326.519068272,
        line5RealGrossProfitPct: 49.7463,
        line6SuppliesPackagingNbr: -121923.165811834,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 7087403.353256434,
        line8RealGrossProfitPct: 48.905,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "FA",
        reason: null,
        state: "PUBLISHED",
        fiscalQuarterNbr: 202501,
    },
    {
        aggregatedLevel: "Period",
        mainRow: "Period01",
        subRow: "Actuals",
        line1PublicToSalesNbr: 13828946.734645028,
        line5BookGrossProfitNbr: 7904436.116278392,
        line5BookGrossProfitPct: 57.15860000000001,
        line5MarkDownsNbr: -183473.447953311,
        line5MarkDownsPct: -1.3267,
        line5ShrinkNbr: -1408592.163526798,
        line5ShrinkPct: -10.1858,
        line5RealGrossProfitNbr: 6312370.504798283,
        line5RealGrossProfitPct: 45.646,
        line6SuppliesPackagingNbr: -105116.13888218,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 6207254.365916103,
        line8RealGrossProfitPct: 44.8859,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 0,
        sortBy: 202501,
        fiscalWeekEnding: "",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
        fiscalQuarterNbr: 202501,
    },
    {
        aggregatedLevel: "Weeks",
        mainRow: "Weeks01",
        subRow: "ACT to PROJ",
        line1PublicToSalesNbr: -249648.2099431241,
        line5BookGrossProfitNbr: -127142.46806248021,
        line5BookGrossProfitPct: 0.4830000000000001,
        line5MarkDownsNbr: -7688.027376186998,
        line5MarkDownsPct: -0.35499999999999976,
        line5ShrinkNbr: 56646.94969637797,
        line5ShrinkPct: 1.0049000000000001,
        line5RealGrossProfitNbr: -78183.5457422859,
        line5RealGrossProfitPct: 1.1328000000000005,
        line6SuppliesPackagingNbr: 4749.877200146002,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: -73433.66854213504,
        line8RealGrossProfitPct: 1.2199999999999989,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 202501,
        sortBy: 202501,
        fiscalWeekEnding: "03/01/2025",
        comment: "",
        forecastType: "",
        reason: "",
        state: "",
    },
    {
        aggregatedLevel: "Weeks",
        mainRow: "Weeks01",
        subRow: "Projection",
        line1PublicToSalesNbr: 3413120.488070837,
        line5BookGrossProfitNbr: 1947136.411233707,
        line5BookGrossProfitPct: 57.048500000000004,
        line5MarkDownsNbr: -48420.117133874,
        line5MarkDownsPct: -1.4186,
        line5ShrinkNbr: -339861.727758627,
        line5ShrinkPct: -9.9575,
        line5RealGrossProfitNbr: 1558854.566341203,
        line5RealGrossProfitPct: 45.6724,
        line6SuppliesPackagingNbr: -27244.442797675,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 1531610.123543523,
        line8RealGrossProfitPct: 44.8741,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 202501,
        sortBy: 202501,
        fiscalWeekEnding: "03/01/2025",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
    },
    {
        aggregatedLevel: "Weeks",
        mainRow: "Weeks01",
        subRow: "DS Forecast",
        line1PublicToSalesNbr: 3223997.330837001,
        line5BookGrossProfitNbr: 1240363.489853184,
        line5BookGrossProfitPct: 38.4728,
        line5MarkDownsNbr: -49504.297679971,
        line5MarkDownsPct: -1.5353999999999999,
        line5ShrinkNbr: -322442.019032692,
        line5ShrinkPct: -10.0013,
        line5RealGrossProfitNbr: 868417.173140521,
        line5RealGrossProfitPct: 26.936,
        line6SuppliesPackagingNbr: -26208.715765679,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 842208.457374842,
        line8RealGrossProfitPct: 26.1231,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 202501,
        sortBy: 202501,
        fiscalWeekEnding: "03/01/2025",
        comment: null,
        forecastType: "DS",
        reason: null,
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Weeks",
        mainRow: "Weeks01",
        subRow: "Base",
        line1PublicToSalesNbr: 3413120.488070837,
        line5BookGrossProfitNbr: 1947136.411233707,
        line5BookGrossProfitPct: 57.048500000000004,
        line5MarkDownsNbr: -48420.117133874,
        line5MarkDownsPct: -1.4186,
        line5ShrinkNbr: -339861.727758627,
        line5ShrinkPct: -9.9575,
        line5RealGrossProfitNbr: 1558854.566341203,
        line5RealGrossProfitPct: 45.6724,
        line6SuppliesPackagingNbr: -27244.442797675,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 1531610.123543523,
        line8RealGrossProfitPct: 44.8741,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 202501,
        sortBy: 202501,
        fiscalWeekEnding: "03/01/2025",
        comment: null,
        forecastType: "Base",
        reason: null,
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Weeks",
        mainRow: "Weeks01",
        subRow: "Merch. Forecast",
        line1PublicToSalesNbr: 3447251.692970837,
        line1PublicToSalesPct: 14.13305150258595,
        line5BookGrossProfitNbr: 1966607.775357052,
        line5BookGrossProfitPct: 57.048500000000004,
        line5MarkDownsNbr: -48904.318305483,
        line5MarkDownsPct: -1.4186,
        line5ShrinkNbr: -343260.345038135,
        line5ShrinkPct: -9.9575,
        line5RealGrossProfitNbr: 1574443.11201343,
        line5RealGrossProfitPct: 45.6724,
        line6SuppliesPackagingNbr: -27244.4428004,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 1547198.66921303,
        line8RealGrossProfitPct: 44.882,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 202501,
        sortBy: 202501,
        fiscalWeekEnding: "03/01/2025",
        comment: null,
        forecastType: "FA",
        reason: null,
        state: "PUBLISHED",
    },
    {
        aggregatedLevel: "Weeks",
        mainRow: "Weeks01",
        subRow: "Actuals",
        line1PublicToSalesNbr: 3163472.278127713,
        line5BookGrossProfitNbr: 1819993.943171227,
        line5BookGrossProfitPct: 57.5315,
        line5MarkDownsNbr: -56108.144510061,
        line5MarkDownsPct: -1.7735999999999998,
        line5ShrinkNbr: -283214.778062249,
        line5ShrinkPct: -8.9526,
        line5RealGrossProfitNbr: 1480671.020598917,
        line5RealGrossProfitPct: 46.8052,
        line6SuppliesPackagingNbr: -22494.565597529,
        line7RetailsAllowancesNbr: 0,
        line7RetailsAllowancesPct: 0,
        line7RetailsSellingAllowancesNbr: 0,
        line7RetailsSellingAllowancesPct: 0,
        line7RetailsNonSellingAllowancesNbr: 0,
        line7RetailsNonSellingAllowancesPct: 0,
        line8RealGrossProfitNbr: 1458176.455001388,
        line8RealGrossProfitPct: 46.0941,
        updatedTs: "2025-05-19T07:52:57.933Z",
        sourceTs: "",
        fiscalYear: 0,
        fiscalPeriodNbr: 202501,
        fiscalWeekNbr: 202501,
        sortBy: 202501,
        fiscalWeekEnding: "03/01/2025",
        comment: null,
        forecastType: "",
        reason: null,
        state: null,
    },
]

const mockMap: any = {
    "Quater#actuals": actualsResponseQtr,
    "Quarter to Date#actuals": actualsResponseQTD,
    "Period#actuals": actualsResponsePrd,
    "Weeks#actuals": actualsResponseWk,
    "Quater#projection": projectionResponseQtr,
    "Quarter to Date#projection": projectionResponseQTD,
    "Period#projection": projectionResponsePrd,
    "Weeks#projection": projectionResponseWk,
    "Quater#forecast": forecastResponseQtr,
    "Quarter to Date#forecast": forecastResponseQTD,
    "Period#forecast": forecastResponsePrd,
    "Weeks#forecast": forecastResponseWk
}

export const getServiceMocks = (req: any) => {
    if (mockMap.hasOwnProperty(`${req.aggregatedLevel}#${req.endPoint}`)) {
        return mockMap[`${req.aggregatedLevel}#${req.endPoint}`];
    }
    return forecastAggregateResponse;
}