import { GraphQLError } from "graphql";
import { Injectable } from "graphql-modules";
import { MSP_DATA_POST_FAILURE } from "../../shared/constants/errorCodes";

import { BaseAPIService } from "../../shared/classes/BaseAPI.service";

@Injectable()
export class RoleMappingService extends BaseAPIService {
    async getWorkSheetFilter(payload: any): Promise<any> {
        let response;
        let req = { ...payload, fetchAll: true };
        try {
            response = await this.post(`desk-user/search`, {
                body: req
            });

            return response?.data?.data;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }

    // async fetchdepartmentCategory(payload: any): Promise<any> {
    //     let response;
    //     let req = { pageNo: 0, pageSize: 1000 };
    //     try {
    //         response = await this.post(`category/search`, {
    //             body: req
    //         });
    //         // console.log("response", response?.data?.data);
    //         response = response?.data?.data;
    //         // return response;
    //     } catch (error) {
    //         throw new GraphQLError("Error while fetching data", {
    //             extensions: {
    //                 errorType: MSP_DATA_POST_FAILURE,
    //                 ...this.createErrorPayload(error)
    //             }
    //         });
    //     }
    //     // console.log("response", response);
    //     return response;
    // }
}
