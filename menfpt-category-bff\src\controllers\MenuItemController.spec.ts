import { Request, Response } from "express";
import menuItems from "../resource/menuItem.json";

// Mock the MenuItemService before any imports
jest.mock("../services/MenuItemService", () => ({
    __esModule: true,
    default: {
        getMenuItems: jest.fn()
    }
}));

// Import the controller after mocking using require
const { getMenuItems } = require("./MenuItemController");
const MenuItemService = require("../services/MenuItemService").default;

describe("MenuItemController", () => {
    let req: Partial<Request>;
    let res: Partial<Response>;
    let jsonMock: jest.Mock;
    let statusMock: jest.Mock;
    let sendMock: jest.Mock;

    beforeEach(() => {
        req = {};
        jsonMock = jest.fn();
        sendMock = jest.fn();
        statusMock = jest.fn(() => ({ send: sendMock })) as any;
        res = { json: jsonMock, status: statusMock };
        jest.clearAllMocks();
    });

    it("should extract and pass authorization header to service", async () => {
        const mockToken = "Bearer test.jwt.token";
        req.headers = { authorization: mockToken } as any;
        
        const mockGetMenuItems = MenuItemService.getMenuItems as jest.Mock;
        mockGetMenuItems.mockResolvedValue(menuItems);
        await getMenuItems(req as Request, res as Response);
        expect(mockGetMenuItems).toHaveBeenCalledWith(mockToken);
        expect(jsonMock).toHaveBeenCalledWith(menuItems);
    });

    it("should handle array authorization header", async () => {
        const mockToken = "Bearer test.jwt.token";
        req.headers = { authorization: [mockToken] } as any;
        
        const mockGetMenuItems = MenuItemService.getMenuItems as jest.Mock;
        mockGetMenuItems.mockResolvedValue(menuItems);
        await getMenuItems(req as Request, res as Response);
        expect(mockGetMenuItems).toHaveBeenCalledWith(mockToken);
        expect(jsonMock).toHaveBeenCalledWith(menuItems);
    });

    it("should handle Authorization header (capital A)", async () => {
        const mockToken = "Bearer test.jwt.token";
        req.headers = { Authorization: mockToken } as any;
        
        const mockGetMenuItems = MenuItemService.getMenuItems as jest.Mock;
        mockGetMenuItems.mockResolvedValue(menuItems);
        await getMenuItems(req as Request, res as Response);
        expect(mockGetMenuItems).toHaveBeenCalledWith(mockToken);
        expect(jsonMock).toHaveBeenCalledWith(menuItems);
    });

    it("should handle errors and return 500", async () => {
        const mockGetMenuItems = MenuItemService.getMenuItems as jest.Mock;
        mockGetMenuItems.mockImplementation(() => {
            throw new Error("fail");
        });
        await getMenuItems(req as Request, res as Response);
        expect(statusMock).toHaveBeenCalledWith(500);
        expect(sendMock).toHaveBeenCalledWith("Internal Server Issue");
    });
});
