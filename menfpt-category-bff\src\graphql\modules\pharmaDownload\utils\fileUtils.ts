/**
 * Utility functions for file operations and content type handling
 */

/**
 * Gets content type based on file extension
 */
export function getContentTypeFromFileName(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();

    const contentTypes: { [key: string]: string } = {
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };

    return contentTypes[extension || ''] || 'application/octet-stream';
}

/**
 * Converts a readable stream to buffer
 */
export async function streamToBuffer(stream: NodeJS.ReadableStream): Promise<Buffer> {
    const chunks: Buffer[] = [];
    for await (const chunk of stream) {
        chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
    }
    return Buffer.concat(chunks);
}

/**
 * Validates if a filename is safe for processing
 */
export function isValidFileName(fileName: string): boolean {
    if (!fileName || fileName.trim() === '') {
        return false;
    }

    // Check for path traversal attempts
    if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
        return false;
    }

    return true;
}
