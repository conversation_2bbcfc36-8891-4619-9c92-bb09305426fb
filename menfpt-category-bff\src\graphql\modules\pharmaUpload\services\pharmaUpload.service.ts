import { Injectable } from "graphql-modules";
import axios from "axios";
import { pharmacyContainerClient, isConnectionAvailable } from "../../pharmaUpload/Connection";

@Injectable()
export class PharmaUploadService {
    async getUploadedDocuments(user: string, fiscalWeekNbr: string, fetchAll: boolean) {
        try {
            const forecastEndpoint = process.env.FORECAST_ENDPOINT?.replace(/\/$/, "");
            const apiUrl = `${forecastEndpoint}/jobs/fetch`;
            const response = await axios.post(apiUrl, {
                user,
                fiscalWeekNbr,
                fetchAll: false
            }, {
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const jobs = response.data.data || [];
            const firstTwoFiles = jobs.slice(0, 2)
                .map((job: any) => job.fileName)
                .filter((fileName: string) => !!fileName)
                .join(", ");

            const uniqueFileNames: string[] = Array.from(
                new Set(
                    jobs
                        .map((job: any) => job.fileName)
                        .filter((fileName: string) => !!fileName)
                )
            );

            // Check if Azure connection is available
            if (!isConnectionAvailable()) {
                throw new Error("Azure Blob Storage connection is not available. Please check environment variables.");
            }

            const uploadedDocuments = await Promise.all(
                uniqueFileNames.map(async (fileName: string) => {
                    try {
                        const blobName = `pharmacy/${fileName}`;
                        const blobClient = pharmacyContainerClient!.getBlobClient(blobName);
                        const exists = await blobClient.exists();

                        if (exists) {
                            const buffer = await blobClient.downloadToBuffer();
                            const fileContent = buffer.toString("base64");

                            // Find the job details for this fileName to get updatedTs
                            const jobDetail = jobs.find((job: any) => job.fileName === fileName);

                            return {
                                fileName,
                                fileContent,
                                files: firstTwoFiles,
                                updatedTs: jobDetail?.updatedTs
                            };
                        } else {
                            return null;
                        }
                    } catch (error) {
                        console.error(`Error processing file ${fileName}:`, error);
                        return null;
                    }
                })
            );

            return {
                uploadedDocuments: uploadedDocuments.filter(Boolean),
            };
        } catch (error) {
            throw error;
        }
    }
}