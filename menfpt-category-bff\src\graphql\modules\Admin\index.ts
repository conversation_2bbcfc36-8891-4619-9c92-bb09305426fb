import { createModule } from "graphql-modules";
import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";
import { AdminProvider } from "./providers/Admin.provider";
import { resolvers } from "./resolvers/Admin.resolver";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.graphql"));

export const adminModule = createModule({
  id: "admin-module",
  dirname: __dirname,
  typeDefs: typeDefs,
  resolvers: resolvers,
  providers: [AdminProvider],
});