type PharmaFileInfo {
    fileName: String!
    fullPath: String!
    size: Int!
    lastModified: String!
}

type PharmaFileListResponse {
    success: Boolean!
    files: [PharmaFileInfo!]!
    error: String
}

type PharmaDownloadUrlResponse {
    success: Boolean!
    downloadUrl: String
    expiresAt: String
    isBFFProxied: Boolean!
    error: String
}

type Query {
    # List all available pharmacy files for download
    listPharmacyFiles: PharmaFileListResponse!
    
    # Generate a download URL for a specific pharmacy file
    # Returns either a SAS URL (if using shared key auth) or BFF-proxied URL (if using Azure AD auth)
    generatePharmacyFileDownloadUrl(fileName: String!, expiryMinutes: Int): PharmaDownloadUrlResponse!
}
