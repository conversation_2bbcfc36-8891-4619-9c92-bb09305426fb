name: PRScan checking
on:
  workflow_dispatch:
  push:
    branches: ["master", "develop"]
  pull_request:
    branches: ["master", "develop"]
jobs:
  call-workflow:
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/PRScan_node.yml@v4
    with:
      NodeVersion: 24
      command: "npm ci && npm run build && npm run test"
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
