import "reflect-metadata";
import { FileUploadPharmaProvider } from "../fileUploadPharma.provider";
import { FileUploadPharmaService } from "../../services/fileUploadPharma.service";

// Mock the Connection module to prevent module-level execution
jest.mock("../../../pharmaUpload/Connection", () => ({
    pharmacyContainerClient: {
        getBlobClient: jest.fn(),
        exists: jest.fn().mockResolvedValue(true),
        listBlobsFlat: jest.fn().mockReturnValue([])
    },
    checkConnection: jest.fn().mockResolvedValue(true),
    listPharmacyBlobs: jest.fn().mockResolvedValue([])
}));

describe("FileUploadPharmaProvider", () => {
    it("should provide FileUploadPharmaService", () => {
        expect(FileUploadPharmaProvider.provide).toBe(FileUploadPharmaService);
        expect(FileUploadPharmaProvider.useClass).toBe(FileUploadPharmaService);
    });

    it("should have correct provider structure", () => {
        expect(FileUploadPharmaProvider).toHaveProperty("provide");
        expect(FileUploadPharmaProvider).toHaveProperty("useClass");
    });

    it("should be a valid dependency injection provider", () => {
        // Verify that the provider can be used for dependency injection
        const providerToken = FileUploadPharmaProvider.provide;
        const serviceClass = FileUploadPharmaProvider.useClass;

        expect(providerToken).toBeDefined();
        expect(serviceClass).toBeDefined();
        expect(typeof serviceClass).toBe("function");
    });

    it("should have provide token pointing to the service class", () => {
        expect(FileUploadPharmaProvider.provide).toBe(FileUploadPharmaService);
    });

    it("should have useClass pointing to the service class", () => {
        expect(FileUploadPharmaProvider.useClass).toBe(FileUploadPharmaService);
    });

    it("should provide the same class for both provide and useClass", () => {
        expect(FileUploadPharmaProvider.provide).toBe(FileUploadPharmaProvider.useClass);
    });

    it("should be instantiable through the useClass property", () => {
        const ServiceClass = FileUploadPharmaProvider.useClass;
        const instance = new ServiceClass();
        expect(instance).toBeInstanceOf(FileUploadPharmaService);
    });

    it("should have all required properties for DI framework", () => {
        const provider = FileUploadPharmaProvider;

        // Check that it has the structure expected by dependency injection frameworks
        expect(Object.keys(provider)).toContain("provide");
        expect(Object.keys(provider)).toContain("useClass");
        expect(Object.keys(provider)).toHaveLength(2);
    });

    it("should reference a constructor function for useClass", () => {
        const { useClass } = FileUploadPharmaProvider;

        // Verify it's a constructor function
        expect(typeof useClass).toBe("function");
        expect(useClass.prototype).toBeDefined();
        expect(useClass.prototype.constructor).toBe(useClass);
    });

    it("should be serializable for configuration purposes", () => {
        // Test that the provider can be serialized (important for some DI frameworks)
        const serialized = JSON.stringify({
            provide: FileUploadPharmaProvider.provide.name,
            useClass: FileUploadPharmaProvider.useClass.name
        });

        expect(serialized).toContain("FileUploadPharmaService");
    });
});
