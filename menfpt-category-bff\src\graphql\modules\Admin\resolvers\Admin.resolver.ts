import { AdminProvider } from "../providers/Admin.provider";

export const resolvers = {
  Query: {
    testADConnection: async (
      _source: any,
      _args: any,
      { injector }: { injector: any } 
    ) => {
      console.log("Executing testADConnection resolver...");
      const adminProvider = injector.get(AdminProvider); 
      return adminProvider.checkADGroupConnection();
    },
  },
};
