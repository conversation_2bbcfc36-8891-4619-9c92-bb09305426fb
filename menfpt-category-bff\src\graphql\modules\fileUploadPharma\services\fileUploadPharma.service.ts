import { GraphQLError } from "graphql";
import { Injectable } from "graphql-modules";
import { BaseAPIService } from "../../../shared/classes/BaseAPI.service";
import { MSP_DATA_POST_FAILURE } from "../../../shared/constants/errorCodes";
import { pharmacyContainerClient, } from "../../pharmaUpload/Connection";

@Injectable()
export class FileUploadPharmaService extends BaseAPIService {

    async fileUploadToBlob(fileUploadReq: {
        fileName: string;
        uploadDataPersistenceEnabled: boolean;
        user: string;
        fileContent?: number[] | string[];
        fileBuffer?: Buffer;
        fiscalWeekNbr: number
    }): Promise<any> {
        try {
            const fileBuffer = this.prepareFileBuffer(fileUploadReq);
            this.validateFileBufferSize(fileBuffer, fileUploadReq.fileName);
            this.validateFileFormat(fileBuffer, fileUploadReq.fileName);

            await this.uploadFileToBlob(fileUploadReq.fileName, fileBuffer);
            await this.processFileForForecast({
                fileName: fileUploadReq.fileName,
                uploadDataPersistenceEnabled: fileUploadReq.uploadDataPersistenceEnabled,
                user: fileUploadReq.user,
                fiscalWeekNbr: fileUploadReq.fiscalWeekNbr
            });

            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                details: error
            };
        }
    }

    private prepareFileBuffer(fileUploadReq: {
        fileContent?: number[] | string[];
        fileBuffer?: Buffer;
    }): Buffer {
        let fileBuffer = fileUploadReq.fileBuffer;

        if (fileUploadReq.fileContent && Array.isArray(fileUploadReq.fileContent)) {
            fileBuffer = this.convertFileContentToBuffer(fileUploadReq.fileContent);
        }

        if (!fileBuffer || fileBuffer.length === 0) {
            throw new Error('No valid file buffer provided for upload');
        }

        return fileBuffer;
    }

    private convertFileContentToBuffer(fileContent: number[] | string[]): Buffer {
        try {
            const numericArray = this.convertToNumericArray(fileContent);
            return Buffer.from(new Uint8Array(numericArray));
        } catch (uint8Error) {
            return this.fallbackBufferConversion(fileContent);
        }
    }

    private convertToNumericArray(fileContent: number[] | string[]): number[] {
        if (typeof fileContent[0] === 'string') {
            return (fileContent as string[]).map(str => {
                const num = parseInt(str, 10);
                if (isNaN(num)) {
                    throw new Error(`Invalid numeric string: ${str}`);
                }
                return num;
            });
        }
        return fileContent as number[];
    }

    private fallbackBufferConversion(fileContent: number[] | string[]): Buffer {
        try {
            if (typeof fileContent[0] === 'number') {
                return Buffer.from(fileContent as number[]);
            } else {
                throw new Error('Cannot convert string array directly to Buffer');
            }
        } catch (directError) {
            throw new Error(`Buffer conversion failed: ${directError}`);
        }
    }

    private validateFileBufferSize(fileBuffer: Buffer, fileName: string): void {
        const extension = fileName.toLowerCase();
        const isExcelFile = extension.endsWith('.xlsx') || extension.endsWith('.xls');

        if (isExcelFile && fileBuffer.length < 1000) {
            throw new Error(
                `Excel file is too small (${fileBuffer.length} bytes). ` +
                'This indicates the file was not properly read. Excel files are typically at least 10KB.'
            );
        }
    }

    private validateFileFormat(fileBuffer: Buffer, fileName: string): void {
        const isValidFile = this.validateFileBuffer(fileBuffer, fileName);
        if (!isValidFile) {
            throw new Error('Invalid file format detected. Please ensure you are uploading a valid Excel file.');
        }
    }
    private async uploadFileToBlob(fileName: string, fileBuffer?: Buffer): Promise<string> {
        try {
            if (!fileBuffer) {
                throw new Error('No file buffer provided for upload');
            }
            const fullBlobPath = `pharmacy/${fileName}`;
            const blobClient = pharmacyContainerClient.getBlobClient(fullBlobPath);
            const blockBlobClient = blobClient.getBlockBlobClient();
            const mimeType = this.getMimeTypeFromFileName(fileName);
            await blockBlobClient.upload(fileBuffer, fileBuffer.length, {
                blobHTTPHeaders: {
                    blobContentType: mimeType
                }
            });

            return blobClient.url;
        } catch (error) {
            throw new Error(`Failed to upload file to blob storage: ${error}`);
        }
    }
    private async processFileForForecast(payload: {
        fileName: string;
        uploadDataPersistenceEnabled: boolean;
        user: string;
        fiscalWeekNbr: number;
    }): Promise<any> {
        let response;
        try {
            const requestBody = {
                fileName: payload.fileName,
                uploadDataPersistenceEnabled: payload.uploadDataPersistenceEnabled,
                user: payload.user,
                fiscalWeekNbr: payload.fiscalWeekNbr,
            };

            response = await this.post(`forecast/file/process`, {
                body: requestBody
            });

            return {
                data: response.data,
                status: response.status || 200
            };
        } catch (error) {
            let errorMessage = "Error while processing file for forecast";
            throw new GraphQLError(errorMessage, {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }
    private getMimeTypeFromFileName(fileName: string): string {
        const extension = fileName.split('.').pop()?.toLowerCase();
        let mimeType: string;
        switch (extension) {
            case 'xlsx':
                mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                break;
            case 'xls':
                mimeType = 'application/vnd.ms-excel';
                break;
            default:
                mimeType = 'application/octet-stream';
                break;
        }
        return mimeType;
    }
    private validateFileBuffer(fileBuffer: Buffer, fileName: string): boolean {
        try {
            if (!fileBuffer || fileBuffer.length === 0) {
                return false;
            }
            if (fileName.toLowerCase().endsWith('.xlsx')) {
                const header = fileBuffer.slice(0, 2);

                if (header[0] !== 0x50 || header[1] !== 0x4B) {
                    return false;
                }
            } else if (fileName.toLowerCase().endsWith('.xls')) {
                const header = fileBuffer.slice(0, 4);

                if (header[0] !== 0xD0 || header[1] !== 0xCF ||
                    header[2] !== 0x11 || header[3] !== 0xE0) {
                    return false;
                }
            }

            return true;
        } catch (error) {
            return false;
        }
    }

}
