/**
 * Azure Blob Storage operations for pharmacy files
 */

import { pharmacyContainerClient, isConnectionAvailable, getPharmacyFolderPrefix } from '../../pharmaUpload/Connection';
import { PharmaFileInfo } from '../types/pharmaTypes';

/**
 * Lists all available pharmacy files in the blob storage
 */
export async function listBlobFiles(): Promise<PharmaFileInfo[]> {
    if (!isConnectionAvailable()) {
        throw new Error('Azure Blob Storage connection not available');
    }

    const files: PharmaFileInfo[] = [];
    const prefix = getPharmacyFolderPrefix();

    try {
        for await (const blob of pharmacyContainerClient.listBlobsFlat({ prefix })) {
            // Skip the folder itself, only include actual files
            if (blob.name !== prefix && !blob.name.endsWith('/')) {
                files.push({
                    fileName: blob.name.replace(prefix, ''), // Remove the pharmacy/ prefix
                    fullPath: blob.name,
                    size: blob.properties.contentLength || 0,
                    lastModified: blob.properties.lastModified || new Date()
                });
            }
        }
        return files;
    } catch (error) {
        console.error('Error listing pharmacy files:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        throw new Error(`Failed to list pharmacy files: ${errorMessage}`);
    }
}

/**
 * Checks if a blob file exists
 */
export async function checkBlobExists(fileName: string): Promise<boolean> {
    try {
        const prefix = getPharmacyFolderPrefix();
        const blobName = `${prefix}${fileName}`;
        const blobClient = pharmacyContainerClient.getBlobClient(blobName);
        return await blobClient.exists();
    } catch (error) {
        console.error(`Error checking file existence for '${fileName}':`, error);
        return false;
    }
}

/**
 * Gets a blob client for a specific file
 */
export function getBlobClient(fileName: string) {
    const prefix = getPharmacyFolderPrefix();
    const blobName = `${prefix}${fileName}`;
    return pharmacyContainerClient.getBlobClient(blobName);
}

/**
 * Finds similar filenames for better error reporting
 */
export async function findSimilarFileNames(targetFileName: string): Promise<string[]> {
    if (!isConnectionAvailable()) {
        return [];
    }

    const similarFiles: string[] = [];
    const prefix = getPharmacyFolderPrefix();
    const targetBase = targetFileName.toLowerCase().replace(/[^a-z0-9]/g, '');

    try {
        for await (const blob of pharmacyContainerClient.listBlobsFlat({ prefix })) {
            if (blob.name !== prefix && !blob.name.endsWith('/')) {
                const fileName = blob.name.replace(prefix, '');
                const fileBase = fileName.toLowerCase().replace(/[^a-z0-9]/g, '');

                // Check for exact match or similar match
                if (fileName === targetFileName || fileBase.includes(targetBase.substring(0, 10))) {
                    similarFiles.push(fileName);
                }
            }
        }
    } catch (error) {
        console.error('Error finding similar files:', error);
    }

    return similarFiles;
}
