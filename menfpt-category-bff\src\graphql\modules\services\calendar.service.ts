import { GraphQLError } from "graphql";
import { Injectable } from "graphql-modules";
import { DisplayDateRes } from "src/graphql/__generated__/gql-ts-types";
import { MSP_DATA_GET_FAILURE } from "../../shared/constants/errorCodes";

import { BaseAPIService } from "../../shared/classes/BaseAPI.service";

@Injectable()
export class CalendarService extends BaseAPIService {
    async getCalendarData(displayDateReq: any): Promise<any> {
        let response;
        try {
            let requrl = `calendar/search?`;
            if (displayDateReq?.date) {
                requrl = requrl + `fiscal_date=${displayDateReq?.date}`;
            }
            if (displayDateReq?.fiscalYearNumber) {
                requrl =
                    requrl +
                    `&fiscal_year_nbr=${displayDateReq?.fiscalYearNumber}`;
            }
            if (displayDateReq?.fiscalQuarterNumber) {
                requrl =
                    requrl +
                    `&fiscal_quarter_nbr=${displayDateReq?.fiscalQuarterNumber}`;
            }
            if (displayDateReq?.fiscalPeriodNumber) {
                requrl =
                    requrl +
                    `&fiscal_period_nbr=${displayDateReq?.fiscalPeriodNumber}`;
            }
            if (displayDateReq?.fiscalWeekNumber) {
                requrl =
                    requrl +
                    `&fiscal_week_nbr=${displayDateReq?.fiscalWeekNumber}`;
            }
            response = await this.get(requrl);
            response = getFormatedData(response.data);
        } catch (error) {
            throw new GraphQLError("Error while fetching calendar data", {
                extensions: {
                    errorType: MSP_DATA_GET_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
        return response;
    }
}

const getFormatedData = (data: any) => {
    let formttedData: any = [];
    if (data?.data && data?.data?.length > 0) {
        data?.data?.forEach((element: any) => {
            formttedData.push({
                fiscalYearNumber: element?.fiscal_year_nbr,
                fiscalWeekNumber: element?.fiscal_week_nbr,
                fiscalQuarterNumber: element?.fiscal_quarter_nbr,
                fiscalPeriodNumber: element?.fiscal_period_nbr,
                fiscalQuarterStartDate: element?.fiscal_quarter_start_date,
                fiscalQuarterEndDate: element?.fiscal_quarter_end_date,
                fiscalWeekStartDate: element?.fiscal_week_start_date,
                fiscalWeekEndDate: element?.fiscal_week_end_date,
                fiscalPeriodStartDate: element?.fiscal_period_start_date,
                fiscalPeriodEndDate: element?.fiscal_period_end_date,
                createTimestamp: element?.create_ts,
                updateTimestamp: element?.update_ts,
                fiscalPeriodLockoutDate: element?.fiscal_period_lockout_date,
                fiscalPeriodCertificationDate:
                    element?.fiscal_period_certification_date
            });
        });
    }

    return formttedData;
};
