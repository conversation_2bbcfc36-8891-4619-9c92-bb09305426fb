import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";

import { createModule } from "graphql-modules";
import {
    CalendarServiceProvider,
    ForecastProvider
} from "./providers/forecastAdjustmentTable.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));
const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const ForecastAdjustmentTableModule = createModule({
    id: "ForecastAdjustmentTable",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [CalendarServiceProvider, ForecastProvider]
});
export default ForecastAdjustmentTableModule;
