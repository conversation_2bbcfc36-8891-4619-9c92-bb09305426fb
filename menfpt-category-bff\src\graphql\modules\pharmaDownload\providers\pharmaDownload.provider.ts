import { Injectable } from 'graphql-modules';
import { PharmaDownloadService } from '../services/pharmaDownload.service';

@Injectable()
export class PharmaDownloadProvider {
    constructor(private pharmaDownloadService: PharmaDownloadService) { }

    async listPharmacyFiles() {
        try {
            const files = await this.pharmaDownloadService.listPharmacyFiles();
            return {
                success: true,
                files: files.map(file => ({
                    ...file,
                    lastModified: file.lastModified.toISOString()
                })),
                error: null
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return {
                success: false,
                files: [],
                error: errorMessage
            };
        }
    }

    async generatePharmacyFileDownloadUrl(fileName: string, expiryMinutes: number = 60) {
        try {
            const downloadUrl = await this.pharmaDownloadService.generateDownloadUrl(fileName, expiryMinutes);

            // Check if this is a BFF-proxied URL (contains our BFF path)
            const isBFFProxied = downloadUrl ? downloadUrl.includes('/menfpt-category-bff/api/pharmacy/download/') : false;

            // For BFF-proxied URLs, expiry is not applicable since they're not time-limited
            const expiresAt = isBFFProxied ? null : (() => {
                const expires = new Date();
                expires.setMinutes(expires.getMinutes() + expiryMinutes);
                return expires.toISOString();
            })();

            return {
                success: true,
                downloadUrl,
                expiresAt,
                isBFFProxied,
                error: null
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return {
                success: false,
                downloadUrl: null,
                expiresAt: null,
                isBFFProxied: false,
                error: errorMessage
            };
        }
    }
}
