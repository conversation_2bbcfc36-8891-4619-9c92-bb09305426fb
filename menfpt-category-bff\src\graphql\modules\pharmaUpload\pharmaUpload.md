# Pharma Upload GraphQL Module

This module provides GraphQL endpoints for managing pharma upload functionality in the MENFPT Category UI application.

## Overview

The pharma upload module consists of one main query:

1. **getUploadedDocuments** - Retrieves uploaded documents (called on user action)

## GraphQL Schema

### Input Types

#### GetUploadedDocumentsReq

```graphql
input GetUploadedDocumentsReq {
    documentsCount: Int
}
```

### Response Types

#### GetUploadedDocumentsRes

```graphql
type GetUploadedDocumentsRes {
    uploadedDocuments: [UploadedDocument]
}

type UploadedDocument {
    fileId: String
    actualFile: Upload
}

scalar Upload
```

## API Endpoints

### Get Uploaded Documents

**Purpose**: Retrieve documents that have been uploaded. This is called on user action (e.g., when user clicks to view documents).

**GraphQL Query**:

```graphql
query GetUploadedDocuments($getUploadedDocumentsReq: GetUploadedDocumentsReq) {
    getUploadedDocuments(getUploadedDocumentsReq: $getUploadedDocumentsReq) {
        uploadedDocuments {
            fileId
            actualFile
        }
    }
}
```

**Request Payload Example**:

```json
{
    "getUploadedDocumentsReq": {
        "documentsCount": 10
    }
}
```

**Response Payload Example**:

```json
{
    "data": {
        "getUploadedDocuments": {
            "uploadedDocuments": [
                {
                    "fileId": "file1",
                    "actualFile": {
                        "filename": "test.pdf",
                        "mimetype": "application/pdf",
                        "encoding": "7bit",
                        "data": "base64data"
                    }
                }
            ]
        }
    }
}
```

**HTTP Request**:

```
GET /pharma-upload/documents?documents_count=10
```

## Error Handling

The endpoint follows the standard error handling pattern used in the BFF:

**Error Response Example**:

```json
{
    "errors": [
        {
            "message": "Error while fetching uploaded documents",
            "extensions": {
                "errorType": "MSP_DATA_GET_FAILURE",
                "httpStatus": 500,
                "errorCode": "INTERNAL_SERVER_ERROR",
                "name": "Error",
                "message": "Internal server error",
                "url": "https://api.example.com/pharma-upload/documents"
            }
        }
    ]
}
```

## Implementation Details

### Service Layer

-   **PharmaUploadService**: Extends BaseAPIService and handles the HTTP requests
-   **Data Formatting**: Converts snake_case API responses to camelCase GraphQL responses
-   **Error Handling**: Uses GraphQLError with proper error codes and payloads

### Resolver Layer

-   **PharmaUploadResolver**: Contains resolver functions for the query
-   **Context Injection**: Uses dependency injection to access the service
-   **Request Validation**: Validates input parameters before processing

### Module Structure

```
pharmaUpload/
├── gqlTypes/
│   └── pharmaUpload.graphql
├── providers/
│   └── pharmaUpload.provider.ts
├── resolvers/
│   ├── index.ts
│   └── pharmaUpload.resolver.ts
├── services/
│   └── pharmaUpload.service.ts
├── index.ts
└── README.md
```

## Usage in Frontend

### User Action (getUploadedDocuments)

```typescript
// Called when user clicks to view documents
const { data, loading, error } = useQuery(GET_UPLOADED_DOCUMENTS, {
    variables: {
        getUploadedDocumentsReq: {
            documentsCount: 10
        }
    }
});
```

## Environment Configuration

The pharma upload endpoints should be configured in the environment variables:

```env
PHARMA_UPLOAD_ENDPOINT=https://api.example.com/menfpt/pharma-upload
```

The endpoint will be automatically mapped by the BaseAPIService when the URL contains "pharma-upload".
