import resolvers from './index';
import { PharmaDownloadProvider } from '../providers/pharmaDownload.provider';

// Mock the PharmaDownloadProvider
jest.mock('../providers/pharmaDownload.provider');

describe('PharmaDownload Resolvers', () => {
    let mockPharmaDownloadProvider: jest.Mocked<PharmaDownloadProvider>;
    let mockInjector: any;

    beforeEach(() => {
        // Clear all mocks
        jest.clearAllMocks();

        // Create mocked provider
        mockPharmaDownloadProvider = {
            listPharmacyFiles: jest.fn(),
            generatePharmacyFileDownloadUrl: jest.fn()
        } as unknown as jest.Mocked<PharmaDownloadProvider>;

        // Create mock injector
        mockInjector = {
            get: jest.fn().mockReturnValue(mockPharmaDownloadProvider)
        };
    });

    describe('Query.listPharmacyFiles', () => {
        it('should successfully list pharmacy files', async () => {
            const mockResponse = {
                success: true,
                files: [
                    {
                        fileName: 'pharmacy_file_1.xlsx',
                        fullPath: 'pharmacy/pharmacy_file_1.xlsx',
                        size: 1024,
                        lastModified: '2025-08-05T10:00:00.000Z'
                    },
                    {
                        fileName: 'pharmacy_file_2.xlsx',
                        fullPath: 'pharmacy/pharmacy_file_2.xlsx',
                        size: 2048,
                        lastModified: '2025-08-05T11:00:00.000Z'
                    }
                ],
                error: null
            };

            mockPharmaDownloadProvider.listPharmacyFiles.mockResolvedValue(mockResponse);

            const result = await resolvers.Query.listPharmacyFiles(
                {},  // parent
                {},  // args
                { injector: mockInjector }  // context
            );

            expect(result).toEqual(mockResponse);
            expect(mockInjector.get).toHaveBeenCalledWith(PharmaDownloadProvider);
            expect(mockPharmaDownloadProvider.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should handle error response from provider', async () => {
            const mockErrorResponse = {
                success: false,
                files: [],
                error: 'Azure Blob Storage connection failed'
            };

            mockPharmaDownloadProvider.listPharmacyFiles.mockResolvedValue(mockErrorResponse);

            const result = await resolvers.Query.listPharmacyFiles(
                {},
                {},
                { injector: mockInjector }
            );

            expect(result).toEqual(mockErrorResponse);
            expect(mockPharmaDownloadProvider.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should handle empty files list', async () => {
            const mockEmptyResponse = {
                success: true,
                files: [],
                error: null
            };

            mockPharmaDownloadProvider.listPharmacyFiles.mockResolvedValue(mockEmptyResponse);

            const result = await resolvers.Query.listPharmacyFiles(
                {},
                {},
                { injector: mockInjector }
            );

            expect(result).toEqual(mockEmptyResponse);
            expect(mockPharmaDownloadProvider.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should propagate exceptions from provider', async () => {
            const error = new Error('Unexpected provider error');
            mockPharmaDownloadProvider.listPharmacyFiles.mockRejectedValue(error);

            await expect(
                resolvers.Query.listPharmacyFiles(
                    {},
                    {},
                    { injector: mockInjector }
                )
            ).rejects.toThrow('Unexpected provider error');

            expect(mockPharmaDownloadProvider.listPharmacyFiles).toHaveBeenCalledTimes(1);
        });

        it('should handle injector failure', async () => {
            const failingInjector = {
                get: jest.fn().mockImplementation(() => {
                    throw new Error('Injector failed to get provider');
                })
            };

            await expect(
                resolvers.Query.listPharmacyFiles(
                    {},
                    {},
                    { injector: failingInjector }
                )
            ).rejects.toThrow('Injector failed to get provider');

            expect(failingInjector.get).toHaveBeenCalledWith(PharmaDownloadProvider);
        });
    });

    describe('Query.generatePharmacyFileDownloadUrl', () => {
        const fileName = 'test_pharmacy_file.xlsx';

        it('should generate download URL with default expiry', async () => {
            const mockResponse = {
                success: true,
                downloadUrl: 'https://storage.blob.core.windows.net/container/pharmacy/test_pharmacy_file.xlsx?sv=...',
                expiresAt: '2025-08-21T14:00:00.000Z',
                isBFFProxied: false,
                error: null
            };

            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockResolvedValue(mockResponse);

            const result = await resolvers.Query.generatePharmacyFileDownloadUrl(
                {},  // parent
                { fileName },  // args
                { injector: mockInjector }  // context
            );

            expect(result).toEqual(mockResponse);
            expect(mockInjector.get).toHaveBeenCalledWith(PharmaDownloadProvider);
            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith(fileName, undefined);
        });

        it('should generate download URL with custom expiry', async () => {
            const expiryMinutes = 120;
            const mockResponse = {
                success: true,
                downloadUrl: 'https://storage.blob.core.windows.net/container/pharmacy/test_pharmacy_file.xlsx?sv=...',
                expiresAt: '2025-08-21T15:00:00.000Z',
                isBFFProxied: false,
                error: null
            };

            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockResolvedValue(mockResponse);

            const result = await resolvers.Query.generatePharmacyFileDownloadUrl(
                {},
                { fileName, expiryMinutes },
                { injector: mockInjector }
            );

            expect(result).toEqual(mockResponse);
            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith(fileName, expiryMinutes);
        });

        it('should handle BFF-proxied URL response', async () => {
            const mockResponse = {
                success: true,
                downloadUrl: '/menfpt-category-bff/api/pharmacy/download/test_pharmacy_file.xlsx',
                expiresAt: null,
                isBFFProxied: true,
                error: null
            };

            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockResolvedValue(mockResponse);

            const result = await resolvers.Query.generatePharmacyFileDownloadUrl(
                {},
                { fileName },
                { injector: mockInjector }
            );

            expect(result).toEqual(mockResponse);
            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith(fileName, undefined);
        });

        it('should handle error response from provider', async () => {
            const mockErrorResponse = {
                success: false,
                downloadUrl: null,
                expiresAt: null,
                isBFFProxied: false,
                error: 'File not found in pharmacy folder'
            };

            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockResolvedValue(mockErrorResponse);

            const result = await resolvers.Query.generatePharmacyFileDownloadUrl(
                {},
                { fileName },
                { injector: mockInjector }
            );

            expect(result).toEqual(mockErrorResponse);
            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith(fileName, undefined);
        });

        it('should propagate exceptions from provider', async () => {
            const error = new Error('Unexpected provider error');
            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockRejectedValue(error);

            await expect(
                resolvers.Query.generatePharmacyFileDownloadUrl(
                    {},
                    { fileName },
                    { injector: mockInjector }
                )
            ).rejects.toThrow('Unexpected provider error');

            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith(fileName, undefined);
        });

        it('should handle missing fileName argument', async () => {
            const mockResponse = {
                success: false,
                downloadUrl: null,
                expiresAt: null,
                isBFFProxied: false,
                error: 'Invalid fileName parameter'
            };

            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockResolvedValue(mockResponse);

            const result = await resolvers.Query.generatePharmacyFileDownloadUrl(
                {},
                { fileName: undefined },
                { injector: mockInjector }
            );

            expect(result).toEqual(mockResponse);
            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith(undefined, undefined);
        });

        it('should handle empty fileName argument', async () => {
            const mockResponse = {
                success: false,
                downloadUrl: null,
                expiresAt: null,
                isBFFProxied: false,
                error: 'Invalid fileName parameter'
            };

            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockResolvedValue(mockResponse);

            const result = await resolvers.Query.generatePharmacyFileDownloadUrl(
                {},
                { fileName: '' },
                { injector: mockInjector }
            );

            expect(result).toEqual(mockResponse);
            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith('', undefined);
        });

        it('should handle zero expiry minutes', async () => {
            const mockResponse = {
                success: true,
                downloadUrl: 'https://storage.blob.core.windows.net/container/pharmacy/test.xlsx?sv=...',
                expiresAt: '2025-08-21T13:00:00.000Z',
                isBFFProxied: false,
                error: null
            };

            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockResolvedValue(mockResponse);

            const result = await resolvers.Query.generatePharmacyFileDownloadUrl(
                {},
                { fileName, expiryMinutes: 0 },
                { injector: mockInjector }
            );

            expect(result).toEqual(mockResponse);
            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith(fileName, 0);
        });

        it('should handle negative expiry minutes', async () => {
            const mockResponse = {
                success: true,
                downloadUrl: 'https://storage.blob.core.windows.net/container/pharmacy/test.xlsx?sv=...',
                expiresAt: '2025-08-21T12:30:00.000Z',
                isBFFProxied: false,
                error: null
            };

            mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl.mockResolvedValue(mockResponse);

            const result = await resolvers.Query.generatePharmacyFileDownloadUrl(
                {},
                { fileName, expiryMinutes: -30 },
                { injector: mockInjector }
            );

            expect(result).toEqual(mockResponse);
            expect(mockPharmaDownloadProvider.generatePharmacyFileDownloadUrl).toHaveBeenCalledWith(fileName, -30);
        });

        it('should handle injector failure', async () => {
            const failingInjector = {
                get: jest.fn().mockImplementation(() => {
                    throw new Error('Injector failed to get provider');
                })
            };

            await expect(
                resolvers.Query.generatePharmacyFileDownloadUrl(
                    {},
                    { fileName },
                    { injector: failingInjector }
                )
            ).rejects.toThrow('Injector failed to get provider');

            expect(failingInjector.get).toHaveBeenCalledWith(PharmaDownloadProvider);
        });
    });
});
