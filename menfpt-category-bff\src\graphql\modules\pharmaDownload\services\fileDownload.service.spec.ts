import { FileDownloadService } from './fileDownload.service';
import { DownloadResult } from '../types/pharmaTypes';

// Mock the dependency modules
jest.mock('../../pharmaUpload/Connection', () => ({
    isConnectionAvailable: jest.fn(),
}));

jest.mock('../utils/blobUtils', () => ({
    getBlobClient: jest.fn(),
    findSimilarFileNames: jest.fn()
}));

jest.mock('../utils/fileUtils', () => ({
    getContentTypeFromFileName: jest.fn(),
    streamToBuffer: jest.fn(),
    isValidFileName: jest.fn()
}));

describe('FileDownloadService', () => {
    let service: FileDownloadService;
    let mockIsConnectionAvailable: jest.Mock;
    let mockGetBlobClient: jest.Mock;
    let mockFindSimilarFileNames: jest.Mock;
    let mockGetContentTypeFromFileName: jest.Mock;
    let mockStreamToBuffer: jest.Mock;
    let mockIsValidFileName: jest.Mock;

    beforeEach(() => {
        // Get mocked functions
        const { isConnectionAvailable } = require('../../pharmaUpload/Connection');
        const { getBlobClient, findSimilarFileNames } = require('../utils/blobUtils');
        const { getContentTypeFromFileName, streamToBuffer, isValidFileName } = require('../utils/fileUtils');

        mockIsConnectionAvailable = isConnectionAvailable as jest.Mock;
        mockGetBlobClient = getBlobClient as jest.Mock;
        mockFindSimilarFileNames = findSimilarFileNames as jest.Mock;
        mockGetContentTypeFromFileName = getContentTypeFromFileName as jest.Mock;
        mockStreamToBuffer = streamToBuffer as jest.Mock;
        mockIsValidFileName = isValidFileName as jest.Mock;

        service = new FileDownloadService();

        // Reset mocks
        jest.clearAllMocks();
    });

    describe('downloadFile', () => {
        it('should return error for invalid filename', async () => {
            mockIsValidFileName.mockReturnValue(false);

            const result = await service.downloadFile('invalid/../file.xlsx');

            expect(result.success).toBe(false);
            expect(result.error).toBe('Invalid filename provided');
            expect(mockIsValidFileName).toHaveBeenCalledWith('invalid/../file.xlsx');
        });

        it('should return error when connection is not available', async () => {
            mockIsValidFileName.mockReturnValue(true);
            mockIsConnectionAvailable.mockReturnValue(false);

            const result = await service.downloadFile('test.xlsx');

            expect(result.success).toBe(false);
            expect(result.error).toBe('Azure Blob Storage connection not available');
        });

        it('should return error when file does not exist', async () => {
            mockIsValidFileName.mockReturnValue(true);
            mockIsConnectionAvailable.mockReturnValue(true);
            mockFindSimilarFileNames.mockResolvedValue(['similar1.xlsx', 'similar2.xlsx']);

            const mockBlobClient = {
                exists: jest.fn().mockResolvedValue(false)
            };

            mockGetBlobClient.mockReturnValue(mockBlobClient);

            const result = await service.downloadFile('nonexistent.xlsx');

            expect(result.success).toBe(false);
            expect(result.error).toContain("File 'nonexistent.xlsx' not found in pharmacy folder");
            expect(result.error).toContain('Similar files found: similar1.xlsx, similar2.xlsx');
        });

        it('should download file successfully', async () => {
            mockIsValidFileName.mockReturnValue(true);
            mockIsConnectionAvailable.mockReturnValue(true);

            const mockFileData = Buffer.from('test file content');
            const mockStream = {};

            const mockBlobClient = {
                exists: jest.fn().mockResolvedValue(true),
                download: jest.fn().mockResolvedValue({
                    readableStreamBody: mockStream
                }),
                getProperties: jest.fn().mockResolvedValue({
                    contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                })
            };

            mockGetBlobClient.mockReturnValue(mockBlobClient);
            mockStreamToBuffer.mockResolvedValue(mockFileData);

            const result = await service.downloadFile('test.xlsx');

            expect(result.success).toBe(true);
            expect(result.data).toEqual(mockFileData);
            expect(result.fileName).toBe('test.xlsx');
            expect(result.contentType).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            expect(mockStreamToBuffer).toHaveBeenCalledWith(mockStream);
        });

        it('should fallback to getContentTypeFromFileName when properties.contentType is not available', async () => {
            mockIsValidFileName.mockReturnValue(true);
            mockIsConnectionAvailable.mockReturnValue(true);

            const mockFileData = Buffer.from('test file content');
            const mockStream = {};

            const mockBlobClient = {
                exists: jest.fn().mockResolvedValue(true),
                download: jest.fn().mockResolvedValue({
                    readableStreamBody: mockStream
                }),
                getProperties: jest.fn().mockResolvedValue({
                    contentType: null
                })
            };

            mockGetBlobClient.mockReturnValue(mockBlobClient);
            mockStreamToBuffer.mockResolvedValue(mockFileData);
            mockGetContentTypeFromFileName.mockReturnValue('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

            const result = await service.downloadFile('test.xlsx');

            expect(result.success).toBe(true);
            expect(result.contentType).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            expect(mockGetContentTypeFromFileName).toHaveBeenCalledWith('test.xlsx');
        });

        it('should handle download errors', async () => {
            mockIsValidFileName.mockReturnValue(true);
            mockIsConnectionAvailable.mockReturnValue(true);

            const mockBlobClient = {
                exists: jest.fn().mockResolvedValue(true),
                download: jest.fn().mockRejectedValue(new Error('Download failed'))
            };

            mockGetBlobClient.mockReturnValue(mockBlobClient);

            const result = await service.downloadFile('test.xlsx');

            expect(result.success).toBe(false);
            expect(result.error).toBe('Failed to download file: Download failed');
        });
    });
});
