import "reflect-metadata";
import PharmaUploadResolvers from "./index";
import { PharmaUploadResolver } from "./pharmaUpload.resolver";

describe("PharmaUploadResolvers", () => {
    describe("Resolver Configuration", () => {
        it("Should be exported as default export", () => {
            expect(PharmaUploadResolvers).toBeDefined();
        });

        it("Should have Query property", () => {
            expect(PharmaUploadResolvers).toHaveProperty('Query');
        });

        it("Should have getUploadedDocuments resolver", () => {
            expect(PharmaUploadResolvers.Query).toHaveProperty('getUploadedDocuments');
        });
    });

    describe("Resolver Mapping", () => {
        it("Should map getUploadedDocuments to PharmaUploadResolver.getUploadedDocuments", () => {
            expect(PharmaUploadResolvers.Query.getUploadedDocuments).toBe(
                PharmaUploadResolver.getUploadedDocuments
            );
        });

        it("Should have correct function references", () => {
            expect(typeof PharmaUploadResolvers.Query.getUploadedDocuments).toBe('function');
        });
    });

    describe("Resolver Structure", () => {
        it("Should have exactly one Query resolver", () => {
            const queryResolvers = Object.keys(PharmaUploadResolvers.Query);
            expect(queryResolvers).toHaveLength(1);
        });

        it("Should have correct resolver names", () => {
            const queryResolvers = Object.keys(PharmaUploadResolvers.Query);
            expect(queryResolvers).toContain('getUploadedDocuments');
        });

        it("Should not have any other properties besides Query", () => {
            const resolverKeys = Object.keys(PharmaUploadResolvers);
            expect(resolverKeys).toEqual(['Query']);
        });
    });

    describe("Resolver Functionality", () => {
        it("Should have async resolver functions", () => {
            const getUploadedDocumentsFunction = PharmaUploadResolvers.Query.getUploadedDocuments;
            
            expect(typeof getUploadedDocumentsFunction).toBe('function');
        });

        it("Should have same function signatures as original resolver", () => {
            const originalGetUploadedDocuments = PharmaUploadResolver.getUploadedDocuments;
            
            expect(PharmaUploadResolvers.Query.getUploadedDocuments).toBe(originalGetUploadedDocuments);
        });
    });

    describe("Export Consistency", () => {
        it("Should maintain consistent resolver references", () => {
            const resolvers1 = PharmaUploadResolvers;
            const resolvers2 = PharmaUploadResolvers;
            
            expect(resolvers1.Query.getUploadedDocuments).toBe(resolvers2.Query.getUploadedDocuments);
        });

        it("Should not be null or undefined", () => {
            expect(PharmaUploadResolvers).not.toBeNull();
            expect(PharmaUploadResolvers).not.toBeUndefined();
        });

        it("Should be an object", () => {
            expect(typeof PharmaUploadResolvers).toBe('object');
        });
    });

    describe("GraphQL Schema Compatibility", () => {
        it("Should have resolvers that match GraphQL schema", () => {
            // These resolver names should match the Query type in the GraphQL schema
            const expectedResolvers = ['getUploadedDocuments'];
            const actualResolvers = Object.keys(PharmaUploadResolvers.Query);
            
            expectedResolvers.forEach(resolverName => {
                expect(actualResolvers).toContain(resolverName);
            });
        });

        it("Should have resolver functions that can be called", () => {
            const getUploadedDocuments = PharmaUploadResolvers.Query.getUploadedDocuments;
            
            expect(typeof getUploadedDocuments).toBe('function');
        });
    });

    describe("Module Integration", () => {
        it("Should work with GraphQL Modules framework", () => {
            // Test that the resolvers can be used in a GraphQL module
            expect(PharmaUploadResolvers).toHaveProperty('Query');
            expect(PharmaUploadResolvers.Query).toHaveProperty('getUploadedDocuments');
        });

        it("Should have resolvers that can be loaded by loadFilesSync", () => {
            // The resolvers should be in a format that can be loaded by the GraphQL tools
            expect(PharmaUploadResolvers).toBeDefined();
            expect(PharmaUploadResolvers.Query).toBeDefined();
        });
    });
}); 