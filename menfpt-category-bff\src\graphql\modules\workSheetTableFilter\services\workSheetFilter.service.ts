import { GraphQLError } from "graphql";
import { Injectable } from "graphql-modules";
import { WorkSheetFilterReq } from "src/graphql/__generated__/gql-ts-types";
import { BaseAPIService } from "../../../shared/classes/BaseAPI.service";
import { MSP_DATA_POST_FAILURE } from "../../../shared/constants/errorCodes";

@Injectable()
export class WorkSheetFilterService extends BaseAPIService {
    async getWorkSheetFilter(payload: WorkSheetFilterReq): Promise<any> {
        let response;
        console.log("payload", payload);
        try {
            let res = await this.post(`desk-user/search`, {
                body: payload
            });
            response = res.data;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
        return response;
    }
}
