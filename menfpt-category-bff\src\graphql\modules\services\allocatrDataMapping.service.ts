import { GraphQLError } from "graphql";
import { Injectable } from "graphql-modules";
import { MSP_DATA_POST_FAILURE } from "../../shared/constants/errorCodes";

import { BaseAPIService } from "../../shared/classes/BaseAPI.service";
import { AggregateApiData } from "../allocatrDashboardTable/resolvers/dashboardTableHelper";

@Injectable()
export class AllocatrDataMappingService extends BaseAPIService {
    async getForecastDataForDashboard(payload: any): Promise<AggregateApiData[]> {
        let response;
        let req = { ...payload, fetchAll: true };
        try {
            response = await this.post(`aggregate/search/v2`, {
                body: req
            });
            // If response?.data is missing or falsy, return []
            if (!response?.data) {
                return [];
            }
            return response?.data?.data;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }


    async getProjectionDataForDashboard(payload: any): Promise<AggregateApiData[]> {
        let response;
        let req = { ...payload, fetchAll: true };
        try {
            response = await this.post(`projection/aggregate/search`, {
                body: req
            });
            return response?.data?.data;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }

    async getActualDataForDashboard(payload: any): Promise<AggregateApiData[]> {
        let response;
        // Append previous year periods to fiscalPeriodNbrs ONLY for this method
        let req = { ...payload };
        if (Array.isArray(req.fiscalPeriodNbrs) && req.fiscalPeriodNbrs.length > 0) {
            const prevYearPeriods = req.fiscalPeriodNbrs
                .map((nbr: number) => {
                    const year = Math.floor(nbr / 100);
                    const period = nbr % 100;
                    return (year - 1) * 100 + period;
                })
                .filter((nbr: number) => !req.fiscalPeriodNbrs.includes(nbr));
            req.fiscalPeriodNbrs = [...req.fiscalPeriodNbrs, ...prevYearPeriods];
        }
        try {
            response = await this.post(`actuals/aggregate/search`, {
                body: req
            });
            return response?.data?.data;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }
}
