import * as jobRunner from "./jobRunner";
import * as connection from "./connection";
import * as indexModule from "./index";

describe("EPBCSSyncMonitor index module", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("getDataFromDatabricks", () => {
        it("should call fetchDatabricksJobData and return result", async () => {
            const mockResult = { foo: "bar" };
            jest.spyOn(jobRunner, "fetchDatabricksJobData").mockResolvedValue(
                mockResult
            );
            const result = await indexModule.getDataFromDatabricks("job1");
            expect(result).toBe(mockResult);
            expect(jobRunner.fetchDatabricksJobData).toHaveBeenCalledWith(
                "job1"
            );
        });
    });

    describe("getDataWithStatusFromDatabricks", () => {
        it("should call fetchDatabricksJobDataWithStatus and return result", async () => {
            const mockResult = { foo: "bar" };
            jest.spyOn(
                jobRunner,
                "fetchDatabricksJobDataWithStatus"
            ).mockResolvedValue(mockResult);
            const result = await indexModule.getDataWithStatusFromDatabricks(
                "job2"
            );
            expect(result).toBe(mockResult);
            expect(
                jobRunner.fetchDatabricksJobDataWithStatus
            ).toHaveBeenCalledWith("job2");
        });
    });

    describe("listAllDatabricksJobs", () => {
        it("should return jobs from listAllJobs", async () => {
            jest.spyOn(connection, "getAccessToken").mockResolvedValue("token");
            jest.spyOn(connection, "listAllJobs").mockResolvedValue([
                "jobA",
                "jobB"
            ]);
            const result = await indexModule.listAllDatabricksJobs();
            expect(result).toEqual(["jobA", "jobB"]);
        });
        it("should throw error if getAccessToken fails", async () => {
            jest.spyOn(connection, "getAccessToken").mockRejectedValue(
                new Error("fail")
            );
            await expect(indexModule.listAllDatabricksJobs()).rejects.toThrow(
                "fail"
            );
        });
    });

    describe("getAllDatabricksJobsData", () => {
        it("should return jobs data for all jobs", async () => {
            jest.spyOn(connection, "getAccessToken").mockResolvedValue("token");
            jest.spyOn(connection, "listAllJobs").mockResolvedValue({
                jobs: [
                    { job_id: 1, settings: { name: "job1" } },
                    { job_id: 2, settings: { name: "job2" } }
                ]
            });
            jest.spyOn(connection, "fetchJobData").mockImplementation(
                async (jobId) => ({ output: jobId })
            );
            const result = await indexModule.getAllDatabricksJobsData();
            expect(result).toEqual({
                job1: { output: 1 },
                job2: { output: 2 }
            });
        });
        it("should throw error if no jobs found", async () => {
            jest.spyOn(connection, "getAccessToken").mockResolvedValue("token");
            jest.spyOn(connection, "listAllJobs").mockResolvedValue({
                jobs: []
            });
            await expect(
                indexModule.getAllDatabricksJobsData()
            ).rejects.toThrow("No jobs found in the workspace");
        });
    });

    describe("getCronScheduleInfo", () => {
        it("should return formatted cron info", () => {
            Object.defineProperty(connection, "MONDAY_CRON", {
                value: "0 8 * * *",
                configurable: true
            });
            Object.defineProperty(connection, "THURSDAY_CRON", {
                value: "0 12 * * *",
                configurable: true
            });
            Object.defineProperty(connection, "FRIDAY_CRON", {
                value: "0 16 * * *",
                configurable: true
            });
            jest.spyOn(connection, "parseCronHours").mockImplementation(
                (cron) => {
                    if (cron === "0 8 * * *") return [8];
                    if (cron === "0 12 * * *") return [12];
                    if (cron === "0 16 * * *") return [16];
                    return [];
                }
            );
            jest.spyOn(
                connection,
                "formatCronHourToReadableTime"
            ).mockImplementation((h) => `${h}:00 AM`);
            const result = indexModule.getCronScheduleInfo();
            expect(result).toEqual({
                monday: "8:00 AM",
                thursday: "12:00 AM",
                friday: "16:00 AM"
            });
        });
        it("should return raw cron if format is false", () => {
            Object.defineProperty(connection, "MONDAY_CRON", {
                value: "raw1",
                configurable: true
            });
            Object.defineProperty(connection, "THURSDAY_CRON", {
                value: "raw2",
                configurable: true
            });
            Object.defineProperty(connection, "FRIDAY_CRON", {
                value: "raw3",
                configurable: true
            });
            const result = indexModule.getCronScheduleInfo(false);
            expect(result).toEqual({
                monday: "raw1",
                thursday: "raw2",
                friday: "raw3"
            });
        });
    });

    describe("getScheduledTimeInfo", () => {
        it("should return scheduled time info", () => {
            Object.defineProperty(connection, "MONDAY_CRON", {
                value: "0 8 * * *",
                configurable: true
            });
            Object.defineProperty(connection, "THURSDAY_CRON", {
                value: "0 12 * * *",
                configurable: true
            });
            Object.defineProperty(connection, "FRIDAY_CRON", {
                value: "0 16 * * *",
                configurable: true
            });
            jest.spyOn(connection, "parseCronHours").mockImplementation(
                (cron) => {
                    if (cron === "0 8 * * *") return [8];
                    if (cron === "0 12 * * *") return [12];
                    if (cron === "0 16 * * *") return [16];
                    return [];
                }
            );
            jest.spyOn(
                connection,
                "formatCronHourToReadableTime"
            ).mockImplementation((h) => `${h}:00 AM`);
            const result = indexModule.getScheduledTimeInfo();
            expect(result).toEqual({
                Monday: "8:00 AM",
                Thursday: "12:00 AM",
                Friday: "16:00 AM"
            });
        });
    });

    describe("getJobRunsFromDatabricks", () => {
        it("should return job runs for a job id", async () => {
            jest.spyOn(connection, "getAccessToken").mockResolvedValue("token");
            jest.spyOn(connection, "listJobRuns").mockImplementation(() => {
                // debug log to confirm mock is used
                // eslint-disable-next-line no-console
                console.log("MOCK listJobRuns called");
                return Promise.resolve({
                    data: {
                        runs: [
                            {
                                run_id: 1,
                                start_time: 123456789,
                                state: {
                                    life_cycle_state: "TERMINATED",
                                    result_state: "SUCCESS"
                                },
                                job_id: 123
                            },
                            {
                                run_id: 2,
                                start_time: 987654321,
                                state: {
                                    life_cycle_state: "TERMINATED",
                                    result_state: "FAILED"
                                },
                                job_id: 123
                            }
                        ]
                    }
                });
            });
            jest.spyOn(connection, "formatTimestamp").mockReturnValue(
                "formatted"
            );
            jest.spyOn(connection, "fetchRunStatus").mockResolvedValue(
                "status"
            );
            jest.spyOn(connection, "fetchRunOutput").mockResolvedValue({
                notebook_output: "output",
                result_data: {}
            });
            jest.spyOn(
                connection,
                "fetchJobRunsWithFormatting"
            ).mockResolvedValue({ runs: [{ run_id: 1 }, { run_id: 2 }] });
            const result = await indexModule.getJobRunsFromDatabricks(123, 5);
            expect(result).toHaveProperty("runs");
            expect(Array.isArray(result.runs)).toBe(true);
            expect(result.runs.length).toBe(2);
        });
        it("should resolve job name to id and return job runs", async () => {
            jest.spyOn(connection, "getAccessToken").mockResolvedValue("token");
            jest.spyOn(connection, "findJobIdByName").mockResolvedValue(456);
            jest.spyOn(connection, "listJobRuns").mockImplementation(() => {
                // debug log to confirm mock is used
                // eslint-disable-next-line no-console
                console.log("MOCK listJobRuns called");
                return Promise.resolve({
                    data: {
                        runs: [
                            {
                                run_id: 3,
                                start_time: 111111111,
                                state: {
                                    life_cycle_state: "TERMINATED",
                                    result_state: "SUCCESS"
                                },
                                job_id: 456
                            },
                            {
                                run_id: 4,
                                start_time: 222222222,
                                state: {
                                    life_cycle_state: "TERMINATED",
                                    result_state: "FAILED"
                                },
                                job_id: 456
                            }
                        ]
                    }
                });
            });
            jest.spyOn(connection, "formatTimestamp").mockReturnValue(
                "formatted"
            );
            jest.spyOn(connection, "fetchRunStatus").mockResolvedValue(
                "status"
            );
            jest.spyOn(connection, "fetchRunOutput").mockResolvedValue({
                notebook_output: "output",
                result_data: {}
            });
            jest.spyOn(
                connection,
                "fetchJobRunsWithFormatting"
            ).mockResolvedValue({ runs: [{ run_id: 3 }, { run_id: 4 }] });
            const result = await indexModule.getJobRunsFromDatabricks(
                "jobName",
                2
            );
            expect(result).toHaveProperty("runs");
            expect(Array.isArray(result.runs)).toBe(true);
            expect(result.runs.length).toBe(2);
        });
        it("should throw error if access token fails", async () => {
            jest.spyOn(connection, "getAccessToken").mockRejectedValue(
                new Error("fail")
            );
            await expect(
                indexModule.getJobRunsFromDatabricks(1)
            ).rejects.toThrow("fail");
        });
        it("should throw error if listJobRuns fails", async () => {
            jest.spyOn(connection, "getAccessToken").mockResolvedValue("token");
            jest.spyOn(
                connection,
                "fetchJobRunsWithFormatting"
            ).mockRejectedValue(new Error("Failed to list job runs"));
            await expect(
                indexModule.getJobRunsFromDatabricks(123, 5)
            ).rejects.toThrow("Failed to list job runs");
        });
    });
});
