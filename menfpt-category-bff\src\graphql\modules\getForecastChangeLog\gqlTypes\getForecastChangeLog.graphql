input ForecastChangeLogReq{
  keyAttributeName: String,
  keyAttributeValue: [String],
  divisionId: [String],
}
type AdjustedFields {
    fieldName: String
    oldValue: String
    newValue: String
}
type UpdatedMetrics {
    fiscalWeekNbrs:String
    keyAttributeName: String      
    keyAttributeValue: String
    reason: String
    comment: String
    adjustedFields: [AdjustedFields]
}
type ForecastChangeLog {
    updatedTimestamp: String
     updatedBy:String
     editedColumns:String
    updatedMetrics: [UpdatedMetrics]
}
type Query {
    getForecastChangeLog(
        forecastChangeLogReqest: ForecastChangeLogReq
    ): [ForecastChangeLog]
}
