import "reflect-metadata";
import { CalendarService } from "../calendar.service";
import { GraphQLError } from "graphql";
import { MSP_DATA_GET_FAILURE } from "../../../shared/constants/errorCodes";
jest.mock("axios");
jest.mock("../../../shared/classes/BaseAPI.service");

const CalendarSearchAPIResponse = {
    message: "Records found",
    data: [
        {
            fiscal_year_nbr: 2024,
            fiscal_week_nbr: 202401,
            fiscal_quarter_nbr: 202401,
            fiscal_period_nbr: 202401,
            fiscal_quarter_start_date: "02/25/2024",
            fiscal_quarter_end_date: "06/15/2024",
            fiscal_week_start_date: "02/25/2024",
            fiscal_week_end_date: "03/02/2024",
            fiscal_period_start_date: "02/25/2024",
            fiscal_period_end_date: "03/23/2024",
            create_ts: "2025-02-18T08:43:04.543Z",
            update_ts: "2025-02-18T08:43:04.543Z"
        }
    ],
    timestamp: "2025-02-23T12:57:03.303Z"
};

const QuartersInYearAPIResponse = {
    message: "Records found",
    data: [
        {
            fiscal_year_nbr: 2024,
            fiscal_quarter_nbr: 202401,
            fiscal_quarter_start_date: "02/25/2024",
            fiscal_quarter_end_date: "06/15/2024",
            create_ts: "2025-02-18T08:43:04.543Z",
            update_ts: "2025-02-18T08:43:04.543Z"
        },
        {
            fiscal_year_nbr: 2024,
            fiscal_quarter_nbr: 202402,
            fiscal_quarter_start_date: "06/16/2024",
            fiscal_quarter_end_date: "09/14/2024",
            create_ts: "2025-02-18T08:43:04.543Z",
            update_ts: "2025-02-18T08:43:04.543Z"
        },
        {
            fiscal_year_nbr: 2024,
            fiscal_quarter_nbr: 202403,
            fiscal_quarter_start_date: "09/15/2024",
            fiscal_quarter_end_date: "12/14/2024",
            create_ts: "2025-02-18T08:43:04.543Z",
            update_ts: "2025-02-18T08:43:04.543Z"
        },
        {
            fiscal_year_nbr: 2024,
            fiscal_quarter_nbr: 202404,
            fiscal_quarter_start_date: "12/15/2024",
            fiscal_quarter_end_date: "02/22/2025",
            create_ts: "2025-02-18T08:43:04.543Z",
            update_ts: "2025-02-18T08:43:04.543Z"
        }
    ],
    timestamp: "2025-02-23T12:57:03.303Z"
};

describe("CalendarService", () => {
    let service: CalendarService;
    beforeEach(() => {
        service = new CalendarService();
    });

    describe("To get Year number, quarter number, week number and period number ", () => {
        it("should return calendar search response", async () => {
            const response = {
                data: CalendarSearchAPIResponse
            };
            (
                service.get as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getCalendarData({
                fiscalYearNumber: 2024,
                fiscalQuarterNumber: 202401,
                fiscalWeekNumber: 202401,
                fiscalPeriodNumber: 202401,
                date: "01/02/2024"
            });

            expect(result).toEqual([
                {
                    fiscalYearNumber: 2024,
                    fiscalWeekNumber: 202401,
                    fiscalQuarterNumber: 202401,
                    fiscalPeriodNumber: 202401,
                    fiscalQuarterStartDate: "02/25/2024",
                    fiscalQuarterEndDate: "06/15/2024",
                    fiscalWeekStartDate: "02/25/2024",
                    fiscalWeekEndDate: "03/02/2024",
                    fiscalPeriodStartDate: "02/25/2024",
                    fiscalPeriodEndDate: "03/23/2024",
                    createTimestamp: "2025-02-18T08:43:04.543Z",
                    updateTimestamp: "2025-02-18T08:43:04.543Z"
                }
            ]);
        });

        it("should return all quarters for a fiscal year", async () => {
            const response = {
                data: QuartersInYearAPIResponse
            };
            (
                service.get as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getCalendarData({
                fiscalYearNumber: 2024
            });

            expect(result).toEqual([
                {
                    fiscalYearNumber: 2024,
                    fiscalQuarterNumber: 202401,
                    fiscalQuarterStartDate: "02/25/2024",
                    fiscalQuarterEndDate: "06/15/2024",
                    createTimestamp: "2025-02-18T08:43:04.543Z",
                    updateTimestamp: "2025-02-18T08:43:04.543Z"
                },
                {
                    fiscalYearNumber: 2024,
                    fiscalQuarterNumber: 202402,
                    fiscalQuarterStartDate: "06/16/2024",
                    fiscalQuarterEndDate: "09/14/2024",
                    createTimestamp: "2025-02-18T08:43:04.543Z",
                    updateTimestamp: "2025-02-18T08:43:04.543Z"
                },
                {
                    fiscalYearNumber: 2024,
                    fiscalQuarterNumber: 202403,
                    fiscalQuarterStartDate: "09/15/2024",
                    fiscalQuarterEndDate: "12/14/2024",
                    createTimestamp: "2025-02-18T08:43:04.543Z",
                    updateTimestamp: "2025-02-18T08:43:04.543Z"
                },
                {
                    fiscalYearNumber: 2024,
                    fiscalQuarterNumber: 202404,
                    fiscalQuarterStartDate: "12/15/2024",
                    fiscalQuarterEndDate: "02/22/2025",
                    createTimestamp: "2025-02-18T08:43:04.543Z",
                    updateTimestamp: "2025-02-18T08:43:04.543Z"
                }
            ]);

            // Verify the correct URL was constructed
            expect(service.get).toHaveBeenCalledWith(
                "calendar/search?&fiscal_year_nbr=2024"
            );
        });

        it("should handle empty response data", async () => {
            const response = {
                data: {
                    message: "No records found",
                    data: [],
                    timestamp: "2025-02-23T12:57:03.303Z"
                }
            };
            (
                service.get as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getCalendarData({
                fiscalYearNumber: 2025
            });

            expect(result).toEqual([]);
        });

        it("should handle response with missing data property", async () => {
            const response = {
                data: {
                    message: "No records found",
                    timestamp: "2025-02-23T12:57:03.303Z"
                }
            };
            (
                service.get as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getCalendarData({
                fiscalYearNumber: 2025
            });

            expect(result).toEqual([]);
        });

        it("should handle response with empty data array", async () => {
            const response = {
                data: {
                    message: "Records found",
                    data: [],
                    timestamp: "2025-02-23T12:57:03.303Z"
                }
            };
            (
                service.get as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getCalendarData({
                fiscalYearNumber: 2025
            });

            expect(result).toEqual([]);
        });

        it("should handle response with null data", async () => {
            const response = {
                data: {
                    message: "No records found",
                    data: null,
                    timestamp: "2025-02-23T12:57:03.303Z"
                }
            };
            (
                service.get as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getCalendarData({
                fiscalYearNumber: 2025
            });

            expect(result).toEqual([]);
        });

        it("should throw an error with the correct extensions when get request fails", async () => {
            const error = new Error("Error while fetching calendar data");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "SOME_ERROR_CODE",
                name: "SOME_NAME",
                message: "Could not fetch data",
                url: ""
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(
                mockErrorPayload
            );

            (
                service.get as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            try {
                await service.getCalendarData({
                    date: "null"
                });
            } catch (err) {
                const error = err as GraphQLError;
                expect(err).toBeInstanceOf(GraphQLError);
                expect(error.extensions).toEqual({
                    errorType: MSP_DATA_GET_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });
});
