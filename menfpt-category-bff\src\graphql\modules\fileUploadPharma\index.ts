import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";
import { createModule } from "graphql-modules";
import { FileUploadPharmaProvider } from "./providers/fileUploadPharma.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gglTypes/*.(graphql)"));
const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const FileUploadPharmaModule = createModule({
    id: "FileUploadPharma",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [FileUploadPharmaProvider]
});

export default FileUploadPharmaModule;
