// import { DefaultAzureCredential } from "@azure/identity";
// import { BlobServiceClient, StorageSharedKeyCredential } from "@azure/storage-blob";
// import dotenv from "dotenv";
// import path from "path";
// const filePath = path.resolve(__dirname, "../../.env.development");
// dotenv.config({ path: filePath });



// const containerName= process.env.AZURE_STORAGE_CONTAINER_NAME || ""
// const pdfBlobName= process.env.AZURE_STORAGE_PDF_BLOB_NAME || ""

// const credential = new DefaultAzureCredential();

// // Create credentials and client
// const blobServiceClient = new BlobServiceClient(
//   `https://${accountName}.blob.core.windows.net`,
//   credential
// );


// const azureBlobService = {
//   /**
//    * Get PDF data from Azure Blob Storage
//    */
//   async getPdfFromBlob(): Promise<Buffer | null> {
//     try {
//       const containerClient = blobServiceClient.getContainerClient(containerName);
//       const blobClient = containerClient.getBlobClient(pdfBlobName);
      
//       // Download blob content
//       const downloadResponse = await blobClient.download();
      
//       if (!downloadResponse.readableStreamBody) {
//         console.error("No readable stream available for the blob");
//         return null;
//       }
      
//       // Convert stream to buffer
//       return await streamToBuffer(downloadResponse.readableStreamBody);
//     } catch (error) {
//       console.error("Error retrieving PDF from Azure Blob:", error);
//       return null;
//     }
//   }
// };

// /**
//  * Convert a readable stream to a buffer
//  */
// async function streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
//   return new Promise((resolve, reject) => {
//     const chunks: Buffer[] = [];
//     readableStream.on("data", (data) => {
//       chunks.push(Buffer.isBuffer(data) ? data : Buffer.from(data));
//     });
//     readableStream.on("end", () => {
//       resolve(Buffer.concat(chunks));
//     });
//     readableStream.on("error", reject);
//   });
// }

// export default azureBlobService;