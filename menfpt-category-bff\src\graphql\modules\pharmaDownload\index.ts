import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";
import { createModule } from "graphql-modules";
import { PharmaDownloadProvider } from "./providers/pharmaDownload.provider";
import { PharmaDownloadService } from "./services/pharmaDownload.service";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));
const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const PharmaDownloadModule = createModule({
    id: "PharmaDownload",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [PharmaDownloadProvider, PharmaDownloadService]
});

export default PharmaDownloadModule;
