import {
    getBaseAggregatedData,
    sumDataByAggregationRange,
    rollUpDataFromWeek,
    FieldNames,
    AggregationRanges,
    AggregationLevels,
    aggregationLevelFields
} from "../dashboardTableHelper";

describe("dashboardTableHelper", () => {
    const mockWeekToPeriodMap = new Map<number, number>([
        [1, 1],
        [2, 1],
        [3, 1],
        [4, 1],
        [5, 2]
    ]);

    const mockData = {
        proj: {
            [FieldNames.LINE_1]: 100,
            [FieldNames.LINE_5_BOOK_GROSS_PROFIT]: 10,
            [FieldNames.LINE_5_MARKDOWNS]: 5,
            [FieldNames.LINE_5_SHRINK]: 2,
            [FieldNames.LINE_6]: 3,
            [FieldNames.LINE_7]: 4
        },
        actual: {
            [FieldNames.LINE_1]: 120,
            [FieldNames.LY_LINE_1]: 110,
            [FieldNames.LINE_5_BOOK_GROSS_PROFIT]: 12,
            [FieldNames.LINE_5_MARKDOWNS]: 6,
            [FieldNames.LINE_5_SHRINK]: 2.5,
            [FieldNames.LINE_6]: 3.5,
            [FieldNames.LINE_7]: 4.5
        },
        fcst: {},
        projKeeper: {},
        actualKeepr: {},
        fcstKeeper: {}
    };

    describe("getBaseAggregatedData", () => {
        it("should calculate aggregated data correctly for a week", () => {
            const result = getBaseAggregatedData(
                mockData.proj,
                mockData.actual,
                mockData.fcst,
                mockData.projKeeper,
                mockData.actualKeepr,
                mockData.fcstKeeper,
                "week",
                1,
                mockWeekToPeriodMap
            );

            expect(result.id).toBe("week-1");
            expect(result.line1Projection).toBe(100);
            expect(result.lastYear).toBe(110);
            expect(result.actualOrForecast).toBe(120);
            expect(result.weekNumber).toBe(1);
            expect(result.periodNumber).toBe(1);
        });

        it("should handle zero last year sales for idPercentage", () => {
            const modifiedActual = {
                ...mockData.actual,
                [FieldNames.LY_LINE_1]: 0
            };
            const result = getBaseAggregatedData(
                mockData.proj,
                modifiedActual,
                mockData.fcst,
                mockData.projKeeper,
                mockData.actualKeepr,
                mockData.fcstKeeper,
                "week",
                1,
                mockWeekToPeriodMap
            );
            expect(result.idPercentage).toBe(0);
        });

        it("should calculate for period type", () => {
            const result = getBaseAggregatedData(
                mockData.proj,
                mockData.actual,
                mockData.fcst,
                mockData.projKeeper,
                mockData.actualKeepr,
                mockData.fcstKeeper,
                "period",
                2,
                mockWeekToPeriodMap
            );
            expect(result.id).toBe("period-2");
            expect(result.periodNumber).toBe(2);
            expect(result.quarterNumber).toBeUndefined();
        });

        it("should calculate for quarter type (default branch)", () => {
            const result = getBaseAggregatedData(
                mockData.proj,
                mockData.actual,
                mockData.fcst,
                mockData.projKeeper,
                mockData.actualKeepr,
                mockData.fcstKeeper,
                "quarter",
                3,
                mockWeekToPeriodMap
            );
            expect(result.id).toBe("quarter-3");
            expect(result.quarterNumber).toBe(3);
            expect(result.periodNumber).toBeUndefined();
        });

        it("should handle all zero/empty input data", () => {
            const result = getBaseAggregatedData(
                {},
                {},
                {},
                {},
                {},
                {},
                "week",
                1,
                mockWeekToPeriodMap
            );
            expect(result.line1Projection).toBe(0);
            expect(result.lastYear).toBe(0);
            expect(result.actualOrForecast).toBe(0);
            expect(result.idPercentage).toBe(0);
        });

        it("should handle negative and large values", () => {
            const proj = { [FieldNames.LINE_1]: -1000000 };
            const actual = {
                [FieldNames.LINE_1]: 9999999,
                [FieldNames.LY_LINE_1]: -9999999
            };
            const result = getBaseAggregatedData(
                proj,
                actual,
                {},
                {},
                {},
                {},
                "week",
                1,
                mockWeekToPeriodMap
            );
            expect(result.line1Projection).toBe(-1000000);
            expect(result.lastYear).toBe(-9999999);
            expect(result.actualOrForecast).toBe(9999999);
        });

        it("should calculate sub-lines and line5/6/7/8 correctly", () => {
            const result = getBaseAggregatedData(
                mockData.proj,
                mockData.actual,
                mockData.fcst,
                mockData.projKeeper,
                mockData.actualKeepr,
                mockData.fcstKeeper,
                "week",
                1,
                mockWeekToPeriodMap
            );
            expect(result.bookGrossProfit).toBeDefined();
            expect(result.markdown).toBeDefined();
            expect(result.shrink).toBeDefined();
            expect(result.line5).toBeDefined();
            expect(result.line6).toBeDefined();
            expect(result.line7).toBeDefined();
            expect(result.line8).toBeDefined();
        });
    });

    describe("sumDataByAggregationRange", () => {
        it("should sum data by week correctly", () => {
            const apiData: any[] = [
                { fiscalWeekNbr: 1, totalLine1Sales: 100 },
                { fiscalWeekNbr: 1, totalLine1Sales: 50 },
                { fiscalWeekNbr: 2, totalLine1Sales: 200 }
            ];
            const result: any[] = sumDataByAggregationRange(
                apiData,
                true,
                AggregationRanges.WEEK
            );
            const week1 = result.find((r: any) => r.fiscalWeekNbr === 1);
            const week2 = result.find((r: any) => r.fiscalWeekNbr === 2);
            expect(week1?.totalLine1Sales).toBe(150);
            expect(week2?.totalLine1Sales).toBe(200);
        });

        it("should sum data by period correctly", () => {
            const apiData: any[] = [
                { fiscalPeriodNbr: 1, totalLine1Sales: 10 },
                { fiscalPeriodNbr: 1, totalLine1Sales: 20 },
                { fiscalPeriodNbr: 2, totalLine1Sales: 30 }
            ];
            const result: any[] = sumDataByAggregationRange(
                apiData,
                true,
                AggregationRanges.PERIOD
            );
            const period1 = result.find((r: any) => r.fiscalPeriodNbr === 1);
            const period2 = result.find((r: any) => r.fiscalPeriodNbr === 2);
            expect(period1?.totalLine1Sales).toBe(30);
            expect(period2?.totalLine1Sales).toBe(30);
        });

        it("should sum data by quarter correctly", () => {
            const apiData: any[] = [
                { fiscalQuarterNbr: 1, totalLine1Sales: 5 },
                { fiscalQuarterNbr: 1, totalLine1Sales: 15 },
                { fiscalQuarterNbr: 2, totalLine1Sales: 25 }
            ];
            const result: any[] = sumDataByAggregationRange(
                apiData,
                true,
                AggregationRanges.QUARTER
            );
            const quarter1 = result.find((r: any) => r.fiscalQuarterNbr === 1);
            const quarter2 = result.find((r: any) => r.fiscalQuarterNbr === 2);
            expect(quarter1?.totalLine1Sales).toBe(20);
            expect(quarter2?.totalLine1Sales).toBe(25);
        });

        it("should handle empty input", () => {
            const result: any[] = sumDataByAggregationRange(
                [],
                true,
                AggregationRanges.WEEK
            );
            expect(result).toEqual([]);
        });

        it("should handle missing fields and non-numeric values", () => {
            const apiData: any[] = [
                { fiscalWeekNbr: 1, totalLine1Sales: "not-a-number" },
                { fiscalWeekNbr: 1 },
                { fiscalWeekNbr: 2, totalLine1Sales: 10 }
            ];
            const result: any[] = sumDataByAggregationRange(
                apiData,
                true,
                AggregationRanges.WEEK
            );
            const week2 = result.find((r: any) => r.fiscalWeekNbr === 2);
            expect(week2?.totalLine1Sales).toBe(10);
        });

        it("should work with isTotal = false", () => {
            const apiData: any[] = [
                { fiscalWeekNbr: 1, totalLine1Sales: 1 },
                { fiscalWeekNbr: 1, totalLine1Sales: 2 }
            ];
            const result: any[] = sumDataByAggregationRange(
                apiData,
                false,
                AggregationRanges.WEEK
            );
            const week1 = result.find((r: any) => r.fiscalWeekNbr === 1);
            expect(week1?.deptId).not.toBe("Total");
        });
    });

    describe("rollUpDataFromWeek", () => {
        it("should roll up weekly data to period/quarter correctly", () => {
            const actualData: any[] = [
                {
                    totalLine1Sales: 100,
                    fiscalWeekNbr: 1,
                    deptId: "D1",
                    divisionId: "Div1",
                    storeIndicator: "S"
                },
                {
                    totalLine1Sales: 200,
                    fiscalWeekNbr: 5,
                    deptId: "D1",
                    divisionId: "Div1",
                    storeIndicator: "S"
                }
            ];
            const forecastData: any[] = [];
            const allDeptIds: string[] = ["D1"];
            const allWeekNumbers: number[] = [1, 5];

            const result: any[] = rollUpDataFromWeek(
                actualData,
                forecastData,
                "Period",
                allDeptIds,
                allWeekNumbers,
                mockWeekToPeriodMap
            );

            const period1 = result.find(
                (r: any) => r.fiscalPeriodNbr === 1 && r.deptId === "D1"
            );
            const period2 = result.find(
                (r: any) => r.fiscalPeriodNbr === 2 && r.deptId === "D1"
            );
            expect(period1?.totalLine1Sales).toBe(100);
            expect(period2?.totalLine1Sales).toBe(200);
        });

        it("should handle forecast data if actual is missing", () => {
            const actualData: any[] = [];
            const forecastData: any[] = [
                {
                    totalLine1Sales: 50,
                    fiscalWeekNbr: 1,
                    deptId: "D2",
                    divisionId: "Div2",
                    storeIndicator: "S"
                }
            ];
            const allDeptIds: string[] = ["D2"];
            const allWeekNumbers: number[] = [1];
            const result: any[] = rollUpDataFromWeek(
                actualData,
                forecastData,
                "Period",
                allDeptIds,
                allWeekNumbers,
                mockWeekToPeriodMap
            );
            const period1 = result.find(
                (r: any) => r.fiscalPeriodNbr === 1 && r.deptId === "D2"
            );
            expect(period1?.totalLine1Sales).toBe(50);
        });

        it("should handle both actual and forecast missing", () => {
            const result: any[] = rollUpDataFromWeek(
                [],
                [],
                "Period",
                ["D3"],
                [1],
                mockWeekToPeriodMap
            );
            expect(result.length).toBe(1);
            expect((result[0] as any).totalLine1Sales).toBe(0);
        });

        it("should handle fiscalIds as a number (quarter)", () => {
            const actualData: any[] = [
                {
                    totalLine1Sales: 100,
                    fiscalWeekNbr: 1,
                    deptId: "D4",
                    divisionId: "Div4",
                    storeIndicator: "S"
                }
            ];
            const forecastData: any[] = [];
            const allDeptIds: string[] = ["D4"];
            const allWeekNumbers: number[] = [1];
            const result: any[] = rollUpDataFromWeek(
                actualData,
                forecastData,
                "Quarter",
                allDeptIds,
                allWeekNumbers,
                7 // quarter number
            );
            const quarter = result.find(
                (r: any) => r.fiscalQuarterNbr === 7 && r.deptId === "D4"
            );
            expect(quarter?.totalLine1Sales).toBe(100);
        });

        it("should handle fiscalIds as Map with missing week", () => {
            const actualData = [
                {
                    totalLine1Sales: 10,
                    fiscalWeekNbr: 2,
                    deptId: "D1",
                    divisionId: "Div1",
                    storeIndicator: "S"
                }
            ];
            const weekToPeriodMap = new Map([[1, 1]]); // week 2 missing
            const result = rollUpDataFromWeek(
                actualData,
                [],
                "Period",
                ["D1"],
                [2],
                weekToPeriodMap
            );
            if (
                result.length > 0 &&
                typeof result[0] === "object" &&
                result[0] !== null &&
                "fiscalPeriodNbr" in result[0]
            ) {
                expect((result[0] as any).fiscalPeriodNbr).toBe(0);
            } else {
                fail("Result[0] is not an object with fiscalPeriodNbr");
            }
        });

        it("should return an object with default values when no actual or forecast data is present", () => {
            const deptId = "D1";
            const weekNumber = 1;
            const result = rollUpDataFromWeek(
                [], // actualData
                [], // forecastData
                "Week",
                [deptId],
                [weekNumber],
                new Map()
            );
            expect(result.length).toBe(1);
            const obj = result[0] as any;
            expect(obj.aggregationLevel).toBe("Week");
            expect(obj.deptId).toBe(deptId);
            expect(obj.divisionId).toBe("");
            expect(obj.storeIndicator).toBe("");
            expect(obj.totalLine1Sales).toBe(0);
            expect(obj.totalLine4CostOfSales).toBe(0);
            expect(obj.totalLine5BookGrossProfit).toBe(0);
            expect(obj.totalLine5MarkDowns).toBe(0);
            expect(obj.totalLine5Shrink).toBe(0);
            expect(obj.totalLine5RealGrossProfit).toBe(0);
            expect(obj.totalLine6SuppliesPackaging).toBe(0);
            expect(obj.totalLine7RetailAllowances).toBe(0);
            expect(obj.totalLine8RealGrossProfit).toBe(0);
            expect(obj.lastyearTotalSales).toBe(0);
        });
    });

    describe("Enum and Constant Exports", () => {
        it("should have correct AggregationLevels enum values", () => {
            expect(AggregationLevels.ACTUALS).toBe("actuals");
            expect(AggregationLevels.FORECAST).toBe("forecast");
            expect(AggregationLevels.PROJECTION).toBe("projection");
        });
        it("should have correct aggregationLevelFields mapping", () => {
            expect(aggregationLevelFields[AggregationRanges.PERIOD]).toBe(
                "fiscalPeriodNbr"
            );
            expect(aggregationLevelFields[AggregationRanges.WEEK]).toBe(
                "fiscalWeekNbr"
            );
            expect(aggregationLevelFields[AggregationRanges.QUARTER]).toBe(
                "fiscalQuarterNbr"
            );
        });
    });

    describe("sumDataByAggregationRange edge cases", () => {
        it("should not mutate input data", () => {
            const apiData = [
                { fiscalWeekNbr: 1, totalLine1Sales: 10, deptId: "D1" },
                { fiscalWeekNbr: 1, totalLine1Sales: 20, deptId: "D1" }
            ];
            const copy = JSON.parse(JSON.stringify(apiData));
            sumDataByAggregationRange(apiData, true, AggregationRanges.WEEK);
            expect(apiData).toEqual(copy);
        });
        it("should handle missing aggregationLevel gracefully", () => {
            const apiData = [{ totalLine1Sales: 10, deptId: "D1" }];
            const result = sumDataByAggregationRange(apiData, true, "");
            expect(Array.isArray(result)).toBe(true);
        });
    });

    describe("rollUpDataFromWeek edge cases", () => {
        it("should handle empty allDeptIds and allWeekNumbers", () => {
            const result = rollUpDataFromWeek(
                [],
                [],
                "Period",
                [],
                [],
                new Map()
            );
            expect(result).toEqual([]);
        });
        it("should handle fiscalIds as Map with missing week", () => {
            const actualData = [
                {
                    totalLine1Sales: 10,
                    fiscalWeekNbr: 2,
                    deptId: "D1",
                    divisionId: "Div1",
                    storeIndicator: "S"
                }
            ];
            const weekToPeriodMap = new Map([[1, 1]]); // week 2 missing
            const result = rollUpDataFromWeek(
                actualData,
                [],
                "Period",
                ["D1"],
                [2],
                weekToPeriodMap
            );
            if (
                result.length > 0 &&
                typeof result[0] === "object" &&
                result[0] !== null &&
                "fiscalPeriodNbr" in result[0]
            ) {
                expect((result[0] as any).fiscalPeriodNbr).toBe(0);
            } else {
                fail("Result[0] is not an object with fiscalPeriodNbr");
            }
        });
    });
});
