import "reflect-metadata";
import { testkit, gql, MockedApplication } from "graphql-modules";
import { gqlApp } from "../modules/index";
import { ForecastService } from "../modules/services/forecast.service";
import SaveAdjustmentModule from "../modules/saveAdjustment";
import { SaveAdjustmentProvider } from "../modules/saveAdjustment/providers/saveAdjustment.provider";

const CalculateResponse = {
    data: {
        data: [
            {
                "fiscalWeekNbr": 202501,
                "smicCategoryId": 3425,
                "deptId": "311",
                "divisionId": "30",
                "line1PublicToSalesNbr": 17739.994579258,
                "line5BookGrossProfitNbr": 7060.824084272,
                "line5BookGrossProfitPct": 0.398017263,
                "line5MarkDownsNbr": -941731.850128703,
                "line5MarkDownsPct": -53.0852388889564,
                "line5ShrinkNbr": -751.347951053,
                "line5ShrinkPct": -0.042353336,
                "line4CostOfSalesNbr": -953162.368574742,
                "line5RealGrossProfitNbr": -935422.373995484,
                "line5RealGrossProfitPct": -52.7295749621706,
                "line6SuppliesPackagingNbr": 0.142718256,
                "line7RetailsAllowancesNbr": 0,
                "line7RetailsAllowancesPct": 0,
                "line8RealGrossProfitNbr": -934671.168762687,
                "line8RealGrossProfitPct": -52.6872296711706,
                "forecastType": "FA",
                "state": "PUBLISHED",
                "createdBy": "mshai15",
                "updatedBy": "mshai15",
                "reason": "Extreme Weather",
                "comment": "Reviewed"
            }
        ]
    }
};
const SaveCategoryAdjustmentResponse = {
    data: {
        success: true,
        message: "Success"
    }
}

const updateForecastSnapshotResponse = {
    data: {
        'message': 'Records Updated',
        'timestamp': "2025-01-29T20:49:02.739Z"
    }
};

const adjustment = {
    "deptId": "301",
    "divisionIds": [
        "30"
    ],
    "smicCategoryIds": [
        3425
    ],
    "updatedBy": "mshai15",
    "weeks": [
        {
            "fiscalWeekNbr": 202501,
            "editedColumns": "salesToPublic|shrink",
            "reason": "Extreme Weather",
            "comment": "Reviewed",
            "newAggregatedData": {
                "line1PublicToSalesNbr": 550764.923,
                "line1PublicToSalesPct": 0.34657,
                "line5BookGrossProfitNbr": 579151.7687,
                "line5BookGrossProfitPct": 0.2657,
                "line5MarkDownsNbr": -362170.8836,
                "line5MarkDownsPct": 0.1662,
                "line5ShrinkNbr": -82974.9683,
                "line5ShrinkPct": 0.0381,
                "line6SuppliesPackagingNbr": 0,
                "line6SuppliesPackagingPct": 0,
                "line7RetailsAllowancesNbr": 255097.6657,
            },
            "previousAggregatedData": {
                "line1PublicToSalesNbr": 550764.923,
                "line1PublicToSalesPct": 0.34657,
                "line5BookGrossProfitNbr": 579151.7687,
                "line5BookGrossProfitPct": 0.2657,
                "line5MarkDownsNbr": -362170.8836,
                "line5MarkDownsPct": 0.1662,
                "line5ShrinkNbr": -82974.9683,
                "line5ShrinkPct": 0.0381,
                "line6SuppliesPackagingNbr": 0,
                "line6SuppliesPackagingPct": 0,
                "line7RetailsAllowancesNbr": 255097.6657,
            }
        }
    ],
    "deskId": null
};

describe("SaveAdjustment", () => {
    let service: ForecastService;
    beforeEach(() => {
        service = new ForecastService();
    });
    describe("Testing positive scenarios for saveAdjustment details", () => {
        let app: MockedApplication;
        beforeAll(() => {
            app = testkit.mockApplication(gqlApp).replaceModule(
                testkit.mockModule(SaveAdjustmentModule, {
                    providers: [
                        {
                            provide: SaveAdjustmentProvider.provide,
                            useValue: {
                                SaveAdjustment() {
                                    return {};
                                },
                                CalculateAdjustment() {
                                    return CalculateResponse;
                                },
                                SaveCategoryAdjustment() {
                                    return SaveCategoryAdjustmentResponse;
                                },
                                UpdateForecastSnapshot() {
                                    return updateForecastSnapshotResponse;
                                }
                            }
                        }
                    ]
                })
            );
        });
        it("Should return the correct response for saveAdjustment query", async () => {

            const result = await testkit.execute(app, {
                document: gql`
                query SaveAdjustment($adjustment: Adjustment) {
                            saveAdjustment(adjustment: $adjustment) {
                                success
                                message
                            }
                        }
                            `,
                contextValue: {},
                variableValues: { adjustment }
            });
            expect(result?.data?.saveAdjustment).toEqual({
                success: true,
                message: "Success"
            });
        });
    });
});
