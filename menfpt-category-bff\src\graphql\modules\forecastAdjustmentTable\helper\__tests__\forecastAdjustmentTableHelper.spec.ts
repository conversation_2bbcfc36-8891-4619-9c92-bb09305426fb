import {
    getWorksheetFormatData,
    worksheetSubRowsOneDimentionalData,
    worksheetSubRowsTwoDimentionalData,
} from '../forecastAdjustmentTableHelper';
import { expectedResponse, firstData, secondData, worksheetRawData } from '../__mocks__/forecastAdjustmentTableHelper-mock';

describe('forecastAdjustmentTableHelper', () => {
    describe('getWorksheetFormatData', () => {
        it('should format worksheet data correctly', () => {
            const req = { aggregatedLevel: 'Weeks', subRow: 'Actuals' };
            const _worksheetRawData = worksheetRawData;

            const result = getWorksheetFormatData(req, _worksheetRawData);
            expect(result).toHaveLength(1);
            expect(result[0]).toMatchObject(expectedResponse);
        });

        it('should return empty array if worksheetRawData is empty', () => {
            expect(getWorksheetFormatData({}, [])).toEqual([]);
        });
    });

    describe('worksheetSubRowsOneDimentionalData', () => {
        it('should format one dimensional data for Weeks', () => {
            const worksheetData = worksheetRawData[0];
            const result = worksheetSubRowsOneDimentionalData(
                worksheetData,
                'Actuals',
                'Weeks',
                '2024',
                '202401'
            );
            expect(result.aggregatedLevel).toBe('Weeks');
            expect(result.mainRow).toBe('Weeks01');
            expect(result.subRow).toBe('Actuals');
            expect(result.line5BookGrossProfitPct).toBe(10);
            expect(result.line7RetailsAllowancesPct).toBe(50);
            expect(result.fiscalYear).toBe(2024);
            expect(result.fiscalWeekNbr).toBe(4);
        });

        it('should handle missing worksheetData fields gracefully', () => {
            const result = worksheetSubRowsOneDimentionalData(
                {},
                'Actuals',
                'Weeks',
                '2024',
                '202401'
            );
            expect(result.line1PublicToSalesNbr).toBe(0);
        });
    });

    describe('worksheetSubRowsTwoDimentionalData', () => {
        it('should calculate differences between firstData and secondData', () => {
            const _firstData = firstData;
            const _secondData = secondData;
            const result = worksheetSubRowsTwoDimentionalData(
                _firstData,
                _secondData,
                'Actuals',
                'Weeks',
                '2024',
                '202401'
            );
            expect(result.line1PublicToSalesNbr).toBe(9);
            expect(result.line5BookGrossProfitPct).toBeCloseTo(40); // (0.5 - 0.1) * 100
            expect(result.line7RetailsAllowancesPct).toBeCloseTo(9); // (0.1 - 0.01) * 100
            expect(result.fiscalYear).toBe(2024);
            expect(result.fiscalWeekNbr).toBe(4);
        });

        it('should handle missing fields gracefully', () => {
            const result = worksheetSubRowsTwoDimentionalData(
                {},
                {},
                'Actuals',
                'Weeks',
                '2024',
                '202401'
            );
            expect(result.line1PublicToSalesNbr).toBe(0);
        });
    });
});