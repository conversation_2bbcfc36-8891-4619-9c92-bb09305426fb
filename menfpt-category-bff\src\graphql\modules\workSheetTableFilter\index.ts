import { loadFilesSync } from "@graphql-tools/load-files";
import { createModule } from "graphql-modules";
import { join } from "path";
import { WorkSheetFilterProvider } from "./providers/workSheetFilter.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));

const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));
const WorkSheetFilterModule = createModule({
    id: "ForecastAdjustmentWorkSheet",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [WorkSheetFilterProvider]
});
export default WorkSheetFilterModule;
