import { BlobServiceClient } from "@azure/storage-blob";
import { ClientSecretCredential } from "@azure/identity";

// Mock Azure SDKs before any imports
const mockExists = jest.fn();
const mockListBlobsFlat = jest.fn();
const mockGetContainerClient = jest.fn();
const mockBlobServiceClient = jest.fn();
const mockClientSecretCredential = jest.fn();

jest.mock("@azure/storage-blob", () => ({
    BlobServiceClient: jest.fn().mockImplementation(() => ({
        getContainerClient: mockGetContainerClient
    }))
}));

jest.mock("@azure/identity", () => ({
    ClientSecretCredential: jest.fn().mockImplementation(() => ({}))
}));

jest.mock("dotenv", () => ({
    config: jest.fn()
}));

describe("Azure Blob Storage Client", () => {
    let originalEnv: NodeJS.ProcessEnv;

    beforeAll(() => {
        // Save original environment
        originalEnv = { ...process.env };
    });

    afterAll(() => {
        // Restore original environment
        process.env = originalEnv;
    });

    beforeEach(() => {
        jest.clearAllMocks();

        // Clear the require cache to allow fresh module loading
        jest.resetModules();

        // Set up default mock implementations
        mockGetContainerClient.mockReturnValue({
            exists: mockExists,
            listBlobsFlat: mockListBlobsFlat
        });

        (BlobServiceClient as any).mockImplementation(() => ({
            getContainerClient: mockGetContainerClient
        }));

        (ClientSecretCredential as jest.Mock).mockImplementation(() => ({}));
    });

    describe("with valid environment variables", () => {
        beforeEach(() => {
            // Set up environment variables before requiring module
            process.env.AZURE_BLOB_STORAGE_ACCOUNT_NAME = "testaccount";
            process.env.AZURE_BLOB_STORAGE_CONTAINER_NAME = "testcontainer";
            process.env.DATABRICKS_CLIENT_ID = "testclientid";
            process.env.DATABRICKS_CLIENT_SECRET = "testclientsecret";
            process.env.DATABRICKS_TENANT_ID = "testtenantid";
        });

        afterEach(() => {
            // Clean up environment variables
            delete process.env.AZURE_BLOB_STORAGE_ACCOUNT_NAME;
            delete process.env.AZURE_BLOB_STORAGE_CONTAINER_NAME;
            delete process.env.DATABRICKS_CLIENT_ID;
            delete process.env.DATABRICKS_CLIENT_SECRET;
            delete process.env.DATABRICKS_TENANT_ID;
        });

        describe("isConnectionAvailable", () => {
            it("should return true if pharmacyContainerClient is initialized", () => {
                const ConnectionModule = require("./Connection");
                expect(ConnectionModule.isConnectionAvailable()).toBe(true);
            });
        });

        describe("getPharmacyFolderPrefix", () => {
            it("should return correct folder prefix", () => {
                const ConnectionModule = require("./Connection");
                expect(ConnectionModule.getPharmacyFolderPrefix()).toBe("pharmacy/");
            });
        });

        describe("checkConnection", () => {
            it("should return true if container exists", async () => {
                mockExists.mockResolvedValue(true);
                const ConnectionModule = require("./Connection");

                const result = await ConnectionModule.checkConnection();
                expect(result).toBe(true);
                expect(mockExists).toHaveBeenCalled();
            });

            it("should throw error if exists() fails", async () => {
                const testError = new Error("Connection failed");
                mockExists.mockRejectedValue(testError);

                const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { });
                const ConnectionModule = require("./Connection");

                await expect(ConnectionModule.checkConnection()).rejects.toThrow("Connection failed");
                expect(consoleErrorSpy).toHaveBeenCalledWith("Azure Blob connection error:", testError);

                consoleErrorSpy.mockRestore();
            });
        });

        describe("listPharmacyBlobs", () => {
            it("should return list of blob names with prefix", async () => {
                const mockBlobs = [
                    { name: "pharmacy/file1.txt" },
                    { name: "pharmacy/file2.csv" },
                ];

                // Create an async iterator mock
                const asyncIterator = async function* () {
                    for (const blob of mockBlobs) {
                        yield blob;
                    }
                };

                mockListBlobsFlat.mockReturnValue(asyncIterator());
                const ConnectionModule = require("./Connection");

                const result = await ConnectionModule.listPharmacyBlobs();
                expect(result).toEqual(["pharmacy/file1.txt", "pharmacy/file2.csv"]);
                expect(mockListBlobsFlat).toHaveBeenCalledWith({ prefix: "pharmacy/" });
            });

            it("should throw error if listBlobsFlat iteration fails", async () => {
                const testError = new Error("Listing failed");
                const errorIterator = async function* () {
                    throw testError;
                };

                mockListBlobsFlat.mockReturnValue(errorIterator());
                const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { });
                const ConnectionModule = require("./Connection");

                await expect(ConnectionModule.listPharmacyBlobs()).rejects.toThrow("Listing failed");
                expect(consoleErrorSpy).toHaveBeenCalledWith("Error listing pharmacy blobs:", testError);

                consoleErrorSpy.mockRestore();
            });
        });

        describe("Azure client initialization", () => {
            it("should create ClientSecretCredential with correct parameters", () => {
                // This test verifies that the Azure client is properly initialized
                // Since we're mocking the constructors, we test indirectly by checking 
                // that the connection is available when env vars are set
                const ConnectionModule = require("./Connection");
                expect(ConnectionModule.isConnectionAvailable()).toBe(true);
            });

            it("should create BlobServiceClient with correct URL and credential", () => {
                // This test verifies that the Azure client is properly initialized
                // Since we're mocking the constructors, we test indirectly by checking 
                // that the connection is available when env vars are set
                const ConnectionModule = require("./Connection");
                expect(ConnectionModule.isConnectionAvailable()).toBe(true);
            });

            it("should get container client with correct container name", () => {
                // Reset mocks to track calls
                jest.clearAllMocks();

                // Force re-initialization by deleting cached module
                delete require.cache[require.resolve("./Connection")];

                // Require module which should trigger constructor calls
                require("./Connection");

                expect(mockGetContainerClient).toHaveBeenCalledWith("testcontainer");
            });
        });
    });

    describe("without valid environment variables", () => {
        beforeEach(() => {
            // Ensure environment variables are not set
            delete process.env.AZURE_BLOB_STORAGE_ACCOUNT_NAME;
            delete process.env.AZURE_BLOB_STORAGE_CONTAINER_NAME;
            delete process.env.DATABRICKS_CLIENT_ID;
            delete process.env.DATABRICKS_CLIENT_SECRET;
            delete process.env.DATABRICKS_TENANT_ID;
        });

        describe("isConnectionAvailable", () => {
            it("should return false if pharmacyContainerClient is null", () => {
                const ConnectionModule = require("./Connection");
                expect(ConnectionModule.isConnectionAvailable()).toBe(false);
            });
        });

        describe("checkConnection", () => {
            it("should return false if pharmacyContainerClient is null", async () => {
                const ConnectionModule = require("./Connection");
                const result = await ConnectionModule.checkConnection();
                expect(result).toBe(false);
            });
        });

        describe("listPharmacyBlobs", () => {
            it("should return undefined if pharmacyContainerClient is null", async () => {
                const ConnectionModule = require("./Connection");
                const result = await ConnectionModule.listPharmacyBlobs();
                expect(result).toBeUndefined();
            });
        });

        describe("environment variable validation", () => {
            it("should log warning when environment variables are missing", () => {
                const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => { });

                require("./Connection");

                expect(consoleWarnSpy).toHaveBeenCalledWith("Missing required Azure environment variables. Connection will not be available.");
                expect(ClientSecretCredential).not.toHaveBeenCalled();
                expect(BlobServiceClient).not.toHaveBeenCalled();

                consoleWarnSpy.mockRestore();
            });
        });
    });
});
