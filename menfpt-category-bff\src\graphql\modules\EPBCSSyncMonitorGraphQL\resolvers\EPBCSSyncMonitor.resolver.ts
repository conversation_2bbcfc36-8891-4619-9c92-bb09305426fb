// @ts-nocheck
import {
    DEFAULT_JOB_NAME,
    getJobRunsFromDatabricks,
    parseCronHours,
    formatCronHourToReadableTime,
    MONDAY_CRON,
    THURSDAY_CRON,
    FRIDAY_CRON
} from "../EPBCSSyncMonitor";
import {
    formatDateString,
    getFiscalWeekDates,
    checkJobRunAtTime,
    parseTimeString,
    parseSessionDateTime,
    parseRunDateTime,
    getWeekNumberOfDate,
    processDaySchedule,
    getPreviousWeekMondaySync,
    getDefaultWeeks,
    getWeeksForFiscalPeriod,
    logPeriodFormattedRuns,
    findRunsInDateRange,
    groupRunsByDate
} from "../helpers/EPBCSSyncMonitorHelper";
import { getPacificDate } from '../../../Util';
import { getWeeksForFiscalPeriod, getWeekNumberOfDate } from '../../../EPBCSSyncMonitor_WeeksDateUtility';

// Day names array - single source of truth
const DAYS_OF_WEEK = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
const DEFAULT_LIMIT = 25;

function getDefaultWeeks(numWeeks = 4) {
    const weeks = [];
    const today = new Date();

    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());

    for (let i = 0; i < numWeeks; i++) {
        const weekStartDate = new Date(startOfWeek);
        weekStartDate.setDate(startOfWeek.getDate() - (7 * i));

        const weekEndDate = new Date(weekStartDate);
        weekEndDate.setDate(weekStartDate.getDate() + 6);

        weeks.push({
            weekNumber: i + 1,
            weekStartDate: formatDateString(weekStartDate),
            weekEndDate: formatDateString(weekEndDate),
            lastRun: null
        });
    }

    return weeks.reverse();
}

function findNextSync(syncSessions, fiscalPeriodStartDate, fiscalPeriodEndDate, fiscalWeeks) {
    if (!syncSessions || syncSessions.length === 0) {
        return null;
    }

    const now = new Date();
    let isLastFiscalWeek = false;
    let lastFiscalWeek = null;
    if (fiscalWeeks && fiscalWeeks.length > 0) {
        lastFiscalWeek = fiscalWeeks[fiscalWeeks.length - 1];
        const [startMonth, startDay, startYear] = lastFiscalWeek.weekStartDate.split('/').map(Number);
        const [endMonth, endDay, endYear] = lastFiscalWeek.weekEndDate.split('/').map(Number);
        const weekStart = new Date(startYear, startMonth - 1, startDay);
        const weekEnd = new Date(endYear, endMonth - 1, endDay);
        weekStart.setHours(0, 0, 0, 0);
        weekEnd.setHours(23, 59, 59, 999);
        isLastFiscalWeek = now >= weekStart && now <= weekEnd;
    }

    if (isLastFiscalWeek && lastFiscalWeek) {
        const [startMonth, startDay, startYear] = lastFiscalWeek.weekStartDate.split('/').map(Number);
        const [endMonth, endDay, endYear] = lastFiscalWeek.weekEndDate.split('/').map(Number);
        const weekStart = new Date(startYear, startMonth - 1, startDay);
        const weekEnd = new Date(endYear, endMonth - 1, endDay);
        weekStart.setHours(0, 0, 0, 0);
        weekEnd.setHours(23, 59, 59, 999);

        const weekSessions = syncSessions.filter(session => {
            const sessionDate = parseRunDateTime(session.date, "12:00 PM");
            return sessionDate >= weekStart && sessionDate <= weekEnd;
        });

        const scheduledTimes = {
            'Monday': parseCronHours(MONDAY_CRON).map(formatCronHourToReadableTime),
            'Thursday': parseCronHours(THURSDAY_CRON).map(formatCronHourToReadableTime),
            'Friday': parseCronHours(FRIDAY_CRON).map(formatCronHourToReadableTime)
        };
        let expectedSessionsCount = 0;
        for (let i = 0; i < 7; i++) {
            const currentDay = new Date(weekStart);
            currentDay.setDate(weekStart.getDate() + i);
            if (currentDay > now) continue;
            const dayName = DAYS_OF_WEEK[currentDay.getDay()];
            if (scheduledTimes[dayName]) {
                expectedSessionsCount += scheduledTimes[dayName].length;
            }
        }
        const completedSessionsCount = weekSessions.filter(
            session => session.status === 'Complete'
        ).length;

        if (completedSessionsCount === expectedSessionsCount && expectedSessionsCount > 0) {
            return {
                sync_day: 'Done for Week',
                sync_time: null,
                week_number: lastFiscalWeek.weekNumber
            };
        }
    }
    const sessionsWithDates = syncSessions.map(session => {
        const parsedDateTime = parseSessionDateTime(session);
        return {
            ...session,
            parsedDateTime
        };
    }).filter(session => session.parsedDateTime !== null);
    const sortedSessions = sessionsWithDates.sort((a, b) =>
        a.parsedDateTime.getTime() - b.parsedDateTime.getTime()
    );
    for (const session of sortedSessions) {
        if (session.status === 'Processing') {
            return {
                sync_date: session.date,
                sync_day: 'Processing',
                sync_time: null
            };
        }
    }
    const today = new Date(now);
    today.setHours(0, 0, 0, 0);
    const skippedSessions = sortedSessions.filter(session => {
        const sessionDate = new Date(session.parsedDateTime);
        sessionDate.setHours(0, 0, 0, 0);
        return sessionDate.getTime() <= today.getTime() &&
            (!session.sync_enabled || !session.status);
    });
    const futureStartedSession = sortedSessions.find(session =>
        session.parsedDateTime > now &&
        (session.status === 'Processing' || session.status === 'Complete')
    );
    if (skippedSessions.length > 0) {
        const todaySessions = sortedSessions.filter(session => {
            const sessionDate = new Date(session.parsedDateTime);
            sessionDate.setHours(0, 0, 0, 0);
            return sessionDate.getTime() === today.getTime();
        });

        const lastCompletedToday = todaySessions
            .filter(s => s.status === 'Complete')
            .sort((a, b) => a.parsedDateTime - b.parsedDateTime)
            .pop();

        if (lastCompletedToday) {
            const nextToday = todaySessions.find(s =>
                s.parsedDateTime > lastCompletedToday.parsedDateTime &&
                (!s.status || s.status !== 'Complete')
            );
            if (nextToday) {
                return {
                    sync_date: nextToday.date,
                    sync_day: nextToday.day,
                    sync_time: nextToday.time
                };
            }
        }
        const firstSkipped = skippedSessions[0];
        return {
            sync_date: firstSkipped.date,
            sync_day: firstSkipped.day,
            sync_time: firstSkipped.time
        };
    }

    for (const session of sortedSessions) {
        if (
            session.parsedDateTime > now &&
            (!session.status || session.status !== 'Complete')
        ) {
            return {
                sync_date: session.date,
                sync_day: session.day,
                sync_time: session.time
            };
        }
    }
    return null;
}

function findLastSync(syncHistory, syncSessions) {
    const now = new Date();
    // console.log("[findLastSync] Looking for last completed sync");

    if (syncSessions && syncSessions.length > 0) {
        const pastSessions = syncSessions.filter(session => {
            const sessionDateTime = parseSessionDateTime(session);
            return sessionDateTime && sessionDateTime <= now;
        });

        // console.log("[findLastSync] Past sessions:", pastSessions.length);

        const completedSessions = pastSessions.filter(session =>
            session.status === 'Complete' || session.status === 'Fail'
        );

        if (completedSessions.length > 0) {
            const sortedCompletedSessions = [...completedSessions].sort((a, b) => {
                const dateA = parseSessionDateTime(a);
                const dateB = parseSessionDateTime(b);
                if (!dateA || !dateB) return 0;
                return dateB.getTime() - dateA.getTime();
            });

            const lastCompleted = sortedCompletedSessions[0];
            // console.log("[findLastSync] Found completed session:", lastCompleted);
            return {
                sync_day: lastCompleted.day,
                sync_time: lastCompleted.time
            };
        }
    }

    if (syncHistory && syncHistory.runs && syncHistory.runs.length > 0) {
        const pastRuns = syncHistory.runs.filter(run => {
            if (!run.start_time || run.start_time === 'N/A' || run.start_time === 'Invalid Date') {
                return false;
            }

            try {
                const runDate = new Date(run.start_time);
                return runDate <= now;
            } catch (error) {
                return false;
            }
        });

        if (pastRuns.length > 0) {
            const sortedRuns = [...pastRuns].sort((a, b) => {
                const dateA = new Date(a.start_time);
                const dateB = new Date(b.start_time);
                return dateB.getTime() - dateA.getTime();
            });

            const lastRun = sortedRuns[0];
            // console.log("[findLastSync] Found last run from history:", lastRun);

            try {
                const runDate = new Date(lastRun.start_time);
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                const day = dayNames[runDate.getDay()];

                const hours = runDate.getHours();
                const ampm = hours >= 12 ? 'PM' : 'AM';
                const formattedHours = hours % 12 || 12;
                const time = `${formattedHours}:00 ${ampm}`;

                return {
                    sync_day: day,
                    sync_time: time
                };
            } catch (error) {
                console.error("[findLastSync] Error parsing run date:", error);
            }
        }
    }

    // console.log("[findLastSync] No last sync found");
    return { sync_day: null, sync_time: null };
}

function checkAllScheduledSyncsCompleted(syncSessions) {
    if (!syncSessions || syncSessions.length === 0) {
        return false;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    const currentWeekSessions = syncSessions.filter(session => {
        if (!session.date) return false;

        const sessionDate = parseRunDateTime(session.date, "12:00 PM");
        return sessionDate >= startOfWeek && sessionDate <= endOfWeek;
    });

    const scheduledTimes = {
        'Monday': parseCronHours(MONDAY_CRON).map(formatCronHourToReadableTime),
        'Thursday': parseCronHours(THURSDAY_CRON).map(formatCronHourToReadableTime),
        'Friday': parseCronHours(FRIDAY_CRON).map(formatCronHourToReadableTime)
    };

    let expectedSessionsCount = 0;

    for (let i = 0; i < 7; i++) {
        const currentDay = new Date(startOfWeek);
        currentDay.setDate(startOfWeek.getDate() + i);

        if (currentDay > today) continue;

        const dayName = DAYS_OF_WEEK[currentDay.getDay()];

        if (scheduledTimes[dayName]) {
            expectedSessionsCount += scheduledTimes[dayName].length;
        }
    }

    const completedSessionsCount = currentWeekSessions.filter(
        session => session.status === 'Complete'
    ).length;

    // console.log(`Checking completed runs: ${completedSessionsCount}/${expectedSessionsCount} completed`);

    return completedSessionsCount === expectedSessionsCount && expectedSessionsCount > 0;
}

function getTargetSyncDates() {
    const now = getPacificDate();
    const thisTuesday = new Date(now);
    thisTuesday.setDate(now.getDate() - ((now.getDay() + 5) % 7));
    thisTuesday.setHours(0, 0, 0, 0);
    let baseTuesday;
    if (
        now.getFullYear() === thisTuesday.getFullYear() &&
        now.getMonth() === thisTuesday.getMonth() &&
        now.getDate() === thisTuesday.getDate()
    ) {
        baseTuesday = new Date(thisTuesday);
        baseTuesday.setDate(thisTuesday.getDate() - 7);
    } else if (now > thisTuesday) {
        baseTuesday = new Date(thisTuesday);
    } else {
        baseTuesday = new Date(thisTuesday);
        baseTuesday.setDate(thisTuesday.getDate() - 7);
    }
    const thursday = new Date(baseTuesday);
    thursday.setDate(baseTuesday.getDate() + 2);

    const friday = new Date(baseTuesday);
    friday.setDate(baseTuesday.getDate() + 3);

    const nextWeekMonday = new Date(baseTuesday);
    nextWeekMonday.setDate(baseTuesday.getDate() + 6);

    thursday.setHours(0, 0, 0, 0);
    friday.setHours(0, 0, 0, 0);
    nextWeekMonday.setHours(0, 0, 0, 0);
    return {
        thursday,
        friday,
        nextWeekMonday
    };
}

export function generateSyncSessionsForPeriod(runs: any[], fiscalPeriodStartDate: string, fiscalPeriodEndDate: string) {
    const { thursday, friday, nextWeekMonday } = getTargetSyncDates();

    const thursdayStr = formatDateString(thursday);
    const fridayStr = formatDateString(friday);
    const mondayStr = formatDateString(nextWeekMonday);

    // console.log(`Current week Thursday: ${thursdayStr}`);
    // console.log(`Current week Friday: ${fridayStr}`);
    // console.log(`Next week Monday: ${mondayStr}`);

    const cronSchedules = {
        'Monday': parseCronHours(MONDAY_CRON).map(formatCronHourToReadableTime),
        'Thursday': parseCronHours(THURSDAY_CRON).map(formatCronHourToReadableTime),
        'Friday': parseCronHours(FRIDAY_CRON).map(formatCronHourToReadableTime)
    };

    const syncSessions = [];

    processDaySchedule('Thursday', thursdayStr, cronSchedules['Thursday'], runs, syncSessions);
    processDaySchedule('Friday', fridayStr, cronSchedules['Friday'], runs, syncSessions);
    processDaySchedule('Monday', mondayStr, cronSchedules['Monday'], runs, syncSessions);


    // console.log(`Generated ${syncSessions.length} sync sessions for current week`);
    return syncSessions;
}

function logPeriodFormattedRuns(fiscalPeriodStartDate, fiscalPeriodEndDate) {
    if (!fiscalPeriodStartDate || !fiscalPeriodEndDate ||
        fiscalPeriodStartDate === '-' || fiscalPeriodEndDate === '-') {
        // console.log("Invalid fiscal period dates, skipping period run logging");
        return;
    }

    try {
        const [startMonth, startDay, startYear] = fiscalPeriodStartDate.split('/').map(Number);
        const [endMonth, endDay, endYear] = fiscalPeriodEndDate.split('/').map(Number);

        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, endMonth - 1, endDay);

        // console.log(`Logging runs for fiscal period: ${fiscalPeriodStartDate} to ${fiscalPeriodEndDate}`);
        // console.log(`Period spans ${Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))} days`);
    } catch (error) {
        console.error("Error logging period formatted runs:", error);
    }
}

export const GetJobRunsFromDatabricksResolver = async (
    _parent,
    args,
    context
) => {
    try {
        const jobIdentifier = args.input.jobName || DEFAULT_JOB_NAME;
        const limit = args.input.limit || DEFAULT_LIMIT;
        // console.log(`Getting job runs from Databricks job: ${jobIdentifier}, limit: ${limit}`);
        const fiscalWeekDates = await getFiscalWeekDates(context);
        const jobRunsResult = await getJobRunsFromDatabricks(
            jobIdentifier,
            limit
        );
        logPeriodFormattedRuns(
            fiscalWeekDates.fiscalPeriodStartDate,
            fiscalWeekDates.fiscalPeriodEndDate
        );

        const syncSessions = generateSyncSessionsForPeriod(
            jobRunsResult.runs,
            fiscalWeekDates.fiscalPeriodStartDate,
            fiscalWeekDates.fiscalPeriodEndDate
        );

        const fiscalWeeks = getWeeksForFiscalPeriod(
            fiscalWeekDates.fiscalPeriodStartDate,
            fiscalWeekDates.fiscalPeriodEndDate,
            formatDateString // Pass your formatter
        );

        const nextSync = findNextSync(
            syncSessions,
            fiscalWeekDates.fiscalPeriodStartDate,
            fiscalWeekDates.fiscalPeriodEndDate,
            fiscalWeeks
        );
        const lastSync = findLastSync(jobRunsResult, syncSessions);

        const previousWeekMondaySync = getPreviousWeekLastSync(jobRunsResult.runs);

        const syncHistory = {
            weeks: getWeeksForFiscalPeriod(
                fiscalWeekDates.fiscalPeriodStartDate,
                fiscalWeekDates.fiscalPeriodEndDate
            ),
            mondaySyncs: getSyncsForAllMondays(
                jobRunsResult.runs,
                fiscalWeekDates.fiscalPeriodStartDate
            )
        };

        const updatedSyncHistory = updateSyncHistoryWithPreviousMonday(
            syncHistory,
            jobRunsResult.runs,
            fiscalWeekDates.fiscalPeriodStartDate
        );

        const enhancedResult = {
            ...jobRunsResult,
            syncSessions,
            nextSync,
            lastSync,
            syncHistory: {
                ...updatedSyncHistory,
                weeks: updatedSyncHistory.weeks.slice(0, 3)
            }
        };
        return enhancedResult;
    } catch (error) {
        console.error("Error in GetJobRunsFromDatabricksResolver:", error);
        throw error;
    }
};

export const EPBCSSyncMonitorResolver = {
    Query: {
        getJobRunsFromDatabricks: async (_: any, args: { input: FetchJobRunsInput }, context: any) => {
            try {
                // console.log("getJobRunsFromDatabricks called with args:", JSON.stringify(args));

                const jobIdentifier = args.input.jobId || args.input.jobName;
                if (!jobIdentifier) {
                    throw new Error("Either jobId or jobName must be provided");
                }

                const limit = args.input.limit || DEFAULT_LIMIT;

                const fiscalWeekDates = await getFiscalWeekDates(context);

                const jobRunsResult = await getJobRunsFromDatabricks(
                    jobIdentifier,
                    limit
                );

                return jobRunsResult;
            } catch (error) {
                console.error("Error in getJobRunsFromDatabricks resolver:", error);
                throw error;
            }
        }
    }
};

function checkAllScheduledRunsCompleted(syncSessions) {
    if (!syncSessions || syncSessions.length === 0) {
        return false;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    const currentWeekSessions = syncSessions.filter(session => {
        if (!session.date) return false;

        const sessionDate = parseRunDateTime(session.date, "12:00 PM");
        return sessionDate >= startOfWeek && sessionDate <= endOfWeek;
    });

    const scheduledTimes = {
        'Monday': parseCronHours(MONDAY_CRON).map(formatCronHourToReadableTime),
        'Thursday': parseCronHours(THURSDAY_CRON).map(formatCronHourToReadableTime),
        'Friday': parseCronHours(FRIDAY_CRON).map(formatCronHourToReadableTime)
    };

    let expectedSessionsCount = 0;

    for (let i = 0; i < 7; i++) {
        const currentDay = new Date(startOfWeek);
        currentDay.setDate(startOfWeek.getDate() + i);

        if (currentDay > today) continue;

        const dayName = DAYS_OF_WEEK[currentDay.getDay()];

        if (scheduledTimes[dayName]) {
            expectedSessionsCount += scheduledTimes[dayName].length;
        }
    }

    const completedSessionsCount = currentWeekSessions.filter(
        session => session.status === 'Complete'
    ).length;

    // console.log(`Checking completed runs: ${completedSessionsCount}/${expectedSessionsCount} completed`);

    return completedSessionsCount === expectedSessionsCount && expectedSessionsCount > 0;
}

function getPreviousWeekMondayDate() {
    const today = new Date();
    const thisMonday = new Date(today);
    thisMonday.setDate(today.getDate() - ((today.getDay() + 6) % 7));
    const prevMonday = new Date(thisMonday);
    prevMonday.setDate(thisMonday.getDate() - 7);
    return prevMonday;
}

function getPreviousWeekLastSync(runs) {
    const prevMonday = getPreviousWeekMondayDate();
    prevMonday.setHours(0, 0, 0, 0);

    let foundRun = null;
    if (runs && runs.length) {
        for (const run of runs) {
            if (!run.start_time || run.start_time === 'N/A' || run.start_time === 'Invalid Date') continue;
            try {
                let runDateObj;
                if (run.start_time.includes(',')) {
                    const [runDatePart, runTimePart] = run.start_time.split(', ');
                    const [mm, dd, yyyy] = runDatePart.split('/').map(Number);
                    runDateObj = new Date(yyyy, mm - 1, dd);
                } else {
                    runDateObj = new Date(run.start_time);
                }
                runDateObj.setHours(0, 0, 0, 0);

                if (runDateObj.getTime() === prevMonday.getTime()) {
                    let runTimePart = '';
                    if (run.start_time.includes(',')) {
                        runTimePart = run.start_time.split(', ')[1];
                    } else {
                        const hours = runDateObj.getHours();
                        const minutes = runDateObj.getMinutes();
                        const ampm = hours >= 12 ? 'PM' : 'AM';
                        const formattedHours = hours % 12 || 12;
                        runTimePart = `${formattedHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
                    }
                    foundRun = {
                        date: formatDateString(prevMonday),
                        time: runTimePart,
                        weekNumber: getWeekNumberOfDate(prevMonday),
                        raw: run
                    };
                    break;
                }
            } catch (e) {
                continue;
            }
        }
    }

    // console.log("[SyncHistory] Previous Week Monday:", {
    //     prevMonday: formatDateString(prevMonday),
    //     found: !!foundRun,
    //     details: foundRun ? foundRun : "No run found for previous Monday"
    // });

    if (foundRun && foundRun.raw) delete foundRun.raw;

    return foundRun;
}

function updateSyncHistoryWithPreviousMonday(syncHistory, runs, fiscalPeriodStartDate) {
    const prevMondaySync = getPreviousWeekLastSync(runs);
    if (!prevMondaySync) return syncHistory;

    const updatedWeeks = syncHistory.weeks.map(week => {
        const [startMonth, startDay, startYear] = week.weekStartDate.split('/').map(Number);
        const [endMonth, endDay, endYear] = week.weekEndDate.split('/').map(Number);
        const weekStart = new Date(startYear, startMonth - 1, startDay);
        const weekEnd = new Date(endYear, endMonth - 1, endDay);
        const [pmMonth, pmDay, pmYear] = prevMondaySync.date.split('/').map(Number);
        const prevMondayDate = new Date(pmYear, pmMonth - 1, pmDay);

        if (prevMondayDate >= weekStart && prevMondayDate <= weekEnd) {
            return {
                ...week,
                lastRun: {
                    date: prevMondaySync.date,
                    time: prevMondaySync.time,
                    weekNumber: getFiscalWeekNumber(prevMondaySync.date, fiscalPeriodStartDate)
                }
            };
        }
        return week;
    });

    return {
        ...syncHistory,
        weeks: updatedWeeks
    };
}

function getAllMondaysBetween(startDateStr: string, endDateStr: string) {
    const [startMonth, startDay, startYear] = startDateStr.split('/').map(Number);
    const [endMonth, endDay, endYear] = endDateStr.split('/').map(Number);
    let current = new Date(endYear, endMonth - 1, endDay);
    current.setHours(0, 0, 0, 0);
    current.setDate(current.getDate() - ((current.getDay() + 6) % 7));
    const startDate = new Date(startYear, startMonth - 1, startDay);
    startDate.setHours(0, 0, 0, 0);

    const mondays: string[] = [];
    while (current >= startDate) {
        mondays.push(formatDateString(current));
        current.setDate(current.getDate() - 7);
    }
    //   console.log("[SyncHistory] Checking Mondays for sync history:", mondays);
    return mondays;
}

function getSyncsForAllMondays(runs, fiscalPeriodStartDate) {
    const mondays = getAllMondaysBetween(fiscalPeriodStartDate, formatDateString(new Date()));
    return mondays.map(monday => {
        const run = runs.find(r => {
            if (!r.start_time || r.start_time === 'N/A' || r.start_time === 'Invalid Date') return false;
            let runDateObj;
            if (r.start_time.includes(',')) {
                const [runDatePart] = r.start_time.split(', ');
                const [mm, dd, yyyy] = runDatePart.split('/').map(Number);
                runDateObj = new Date(yyyy, mm - 1, dd);
            } else {
                runDateObj = new Date(r.start_time);
            }
            runDateObj.setHours(0, 0, 0, 0);
            return formatDateString(runDateObj) === monday;
        });
        return {
            monday,
            run: run || null
        };
    });
}

function getFiscalWeekNumber(dateStr: string, fiscalPeriodStartDate: string): number {
    const [startMonth, startDay, startYear] = fiscalPeriodStartDate.split('/').map(Number);
    const [dateMonth, dateDay, dateYear] = dateStr.split('/').map(Number);
    const startDate = new Date(startYear, startMonth - 1, startDay);
    const date = new Date(dateYear, dateMonth - 1, dateDay);
    const diffDays = Math.floor((date.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    return Math.floor(diffDays / 7) + 1;
}
