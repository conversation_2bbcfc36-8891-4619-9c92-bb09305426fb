import "reflect-metadata";
import { FileUploadPharmaResolver } from "../fileUploadPharma.resolver";
import { FileUploadPharmaProvider } from "../../providers/fileUploadPharma.provider";
import { ContextValue } from "../../../../context";

// Mock the provider
jest.mock("../../providers/fileUploadPharma.provider");

// Mock the Connection module to prevent module-level execution
jest.mock("../../../pharmaUpload/Connection", () => ({
    pharmacyContainerClient: {
        getBlobClient: jest.fn(),
        exists: jest.fn().mockResolvedValue(true),
        listBlobsFlat: jest.fn().mockReturnValue([])
    },
    checkConnection: jest.fn().mockResolvedValue(true),
    listPharmacyBlobs: jest.fn().mockResolvedValue([])
}));

describe("FileUploadPharmaResolver", () => {
    let mockContext: ContextValue;
    let mockService: any;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Create mock service
        mockService = {
            fileUploadToBlob: jest.fn()
        };

        // Create mock context
        mockContext = {
            injector: {
                get: jest.fn().mockReturnValue(mockService)
            }
        } as any;
    });

    describe("fileUploadToBlob", () => {
        const mockArgs = {
            fileUploadToBlob: {
                fileName: "test-file.xlsx",
                uploadDataPersistenceEnabled: true,
                user: "test-user",
                fileContent: [1, 2, 3, 4, 5],
                fiscalWeekNbr: 52
            }
        };

        it("should successfully call service and return response", async () => {
            const mockResponse = {
                success: true,
                statusCode: 200,
                data: { message: "File uploaded successfully" }
            };

            mockService.fileUploadToBlob.mockResolvedValue(mockResponse);

            const result = await FileUploadPharmaResolver.fileUploadToBlob(
                null,
                mockArgs,
                mockContext
            );

            expect(result).toEqual(mockResponse);
            expect(mockContext.injector?.get).toHaveBeenCalledWith(FileUploadPharmaProvider.provide);
            expect(mockService.fileUploadToBlob).toHaveBeenCalledWith(mockArgs.fileUploadToBlob);
        });

        it("should handle service not available in context", async () => {
            // Mock context with no service
            const contextWithoutService: ContextValue = {
                injector: {
                    get: jest.fn().mockReturnValue(null)
                }
            } as any;

            try {
                await FileUploadPharmaResolver.fileUploadToBlob(
                    null,
                    mockArgs,
                    contextWithoutService
                );
                fail("Expected error to be thrown");
            } catch (error) {
                expect(error).toBeInstanceOf(Error);
                expect((error as Error).message).toBe(
                    "FileUploadPharmaService is not available in the context injector."
                );
            }
        });

        it("should handle context without injector", async () => {
            const contextWithoutInjector: ContextValue = {} as any;

            try {
                await FileUploadPharmaResolver.fileUploadToBlob(
                    null,
                    mockArgs,
                    contextWithoutInjector
                );
                fail("Expected error to be thrown");
            } catch (error) {
                expect(error).toBeInstanceOf(Error);
                expect((error as Error).message).toBe(
                    "FileUploadPharmaService is not available in the context injector."
                );
            }
        });

        it("should handle empty args object", async () => {
            const mockResponse = {
                success: true,
                statusCode: 200,
                data: { message: "Success" }
            };

            mockService.fileUploadToBlob.mockResolvedValue(mockResponse);

            const emptyArgs = { fileUploadToBlob: {} };
            const result = await FileUploadPharmaResolver.fileUploadToBlob(
                null,
                emptyArgs,
                mockContext
            );

            expect(result).toEqual(mockResponse);
            expect(mockService.fileUploadToBlob).toHaveBeenCalledWith({});
        });

        it("should propagate service errors", async () => {
            const serviceError = new Error("Service error");
            mockService.fileUploadToBlob.mockRejectedValue(serviceError);

            try {
                await FileUploadPharmaResolver.fileUploadToBlob(
                    null,
                    mockArgs,
                    mockContext
                );
                fail("Expected error to be thrown");
            } catch (error) {
                expect(error).toBe(serviceError);
            }
        });

        it("should handle service returning error response", async () => {
            const errorResponse = {
                success: false,
                statusCode: 400,
                message: "Invalid file format",
                error: true
            };

            mockService.fileUploadToBlob.mockResolvedValue(errorResponse);

            const result = await FileUploadPharmaResolver.fileUploadToBlob(
                null,
                mockArgs,
                mockContext
            );

            expect(result).toEqual(errorResponse);
        });

        it("should handle different file types", async () => {
            const csvArgs = {
                fileUploadToBlob: {
                    fileName: "data.csv",
                    uploadDataPersistenceEnabled: false,
                    user: "another-user",
                    fileContent: [65, 66, 67], // ABC in ASCII
                    fiscalWeekNbr: 1
                }
            };

            const mockResponse = {
                success: true,
                statusCode: 200,
                data: { message: "CSV uploaded successfully" }
            };

            mockService.fileUploadToBlob.mockResolvedValue(mockResponse);

            const result = await FileUploadPharmaResolver.fileUploadToBlob(
                null,
                csvArgs,
                mockContext
            );

            expect(result).toEqual(mockResponse);
            expect(mockService.fileUploadToBlob).toHaveBeenCalledWith(csvArgs.fileUploadToBlob);
        });

        it("should handle file buffer instead of file content", async () => {
            const bufferArgs = {
                fileUploadToBlob: {
                    fileName: "test.pdf",
                    uploadDataPersistenceEnabled: true,
                    user: "test-user",
                    fileBuffer: Buffer.from([1, 2, 3]),
                    fiscalWeekNbr: 25
                }
            };

            const mockResponse = {
                success: true,
                statusCode: 200,
                data: { message: "PDF uploaded successfully" }
            };

            mockService.fileUploadToBlob.mockResolvedValue(mockResponse);

            const result = await FileUploadPharmaResolver.fileUploadToBlob(
                null,
                bufferArgs,
                mockContext
            );

            expect(result).toEqual(mockResponse);
            expect(mockService.fileUploadToBlob).toHaveBeenCalledWith(bufferArgs.fileUploadToBlob);
        });

        it("should handle null parent parameter", async () => {
            const mockResponse = {
                success: true,
                statusCode: 200,
                data: { message: "Success" }
            };

            mockService.fileUploadToBlob.mockResolvedValue(mockResponse);

            const result = await FileUploadPharmaResolver.fileUploadToBlob(
                null,
                mockArgs,
                mockContext
            );

            expect(result).toEqual(mockResponse);
        });

        it("should handle undefined parent parameter", async () => {
            const mockResponse = {
                success: true,
                statusCode: 200,
                data: { message: "Success" }
            };

            mockService.fileUploadToBlob.mockResolvedValue(mockResponse);

            const result = await FileUploadPharmaResolver.fileUploadToBlob(
                undefined,
                mockArgs,
                mockContext
            );

            expect(result).toEqual(mockResponse);
        });
    });
});
