import MenuItemService from "./MenuItemService";
import menuItems from "../resource/menuItem.json";
import axios from "axios";

jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;

const originalPharmacyGroup = process.env.PHARMACY_AD_GROUP;
const originalMeceaEndpoint = process.env.MECEA_ENDPOINT;

afterAll(() => {
    if (originalPharmacyGroup !== undefined) {
        process.env.PHARMACY_AD_GROUP = originalPharmacyGroup;
    } else {
        delete process.env.PHARMACY_AD_GROUP;
    }

    if (originalMeceaEndpoint !== undefined) {
        process.env.MECEA_ENDPOINT = originalMeceaEndpoint;
    } else {
        delete process.env.MECEA_ENDPOINT;
    }

    jest.restoreAllMocks();
});

describe("MenuItemService", () => {
    describe("getMenuItems", () => {
        it("should return menu items from the resource when no token provided", async () => {
            const result = await MenuItemService.getMenuItems();
            // Should return filtered menu items (non-pharmacy users see Dashboard and Adjustment Worksheet)
            expect(result.menuItems).toBeDefined();
            expect(result.menuItems[0].contextId).toBe("category_excellence");

            // Non-pharmacy users should only see Allocatr Insights at top level
            const subAppIds = result.menuItems[0].subApp.map(
                (sa: any) => sa.id
            );
            expect(subAppIds).toContain("national_forecast_projection_tool");
            expect(subAppIds).not.toContain("nfpt_rx_forecast");
            const allocatrInsights = result.menuItems[0].subApp.find(
                (sa: any) => sa.id === "national_forecast_projection_tool"
            );
            const subMenuIds = allocatrInsights.subMenu.map(
                (item: any) => item.id
            );
            expect(subMenuIds).toContain("nfpt_dashboard");
            expect(subMenuIds).toContain("nfpt_adjustment_worksheet");
            expect(subMenuIds).not.toContain("nfpt_rx_forecast");
        });

        it("should return pharmacy-only menu items when token contains configured pharmacy group", async () => {
            process.env.PHARMACY_AD_GROUP = "az-menfpt-prod-rxfinance";
            // Mock JWT token for pharmacy user
            const pharmacyToken =
                "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJncm91cHMiOlsiYXotbWVuZnB0LXByb2QtcnhmaW5hbmNlIl19.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8";

            const result = await MenuItemService.getMenuItems(pharmacyToken);
            expect(result.menuItems).toBeDefined();
            expect(result.menuItems[0].contextId).toBe("category_excellence");

            // Pharmacy users should only see Rx Forecast as a top-level subApp
            const subAppIds = result.menuItems[0].subApp.map(
                (sa: any) => sa.id
            );
            expect(subAppIds).toContain("nfpt_rx_forecast");
            expect(subAppIds).not.toContain(
                "national_forecast_projection_tool"
            );
        });

        it("should return non-pharmacy menu items when token does not contain configured pharmacy group", async () => {
            process.env.PHARMACY_AD_GROUP = "az-menfpt-prod-rxfinance";
            // Mock JWT token for non-pharmacy user
            const nonPharmacyToken =
                "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJncm91cHMiOlsiYXotbWVtc3AtcHJvZC1vdGhlciJdfQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8";

            const result = await MenuItemService.getMenuItems(nonPharmacyToken);
            expect(result.menuItems).toBeDefined();
            expect(result.menuItems[0].contextId).toBe("category_excellence");

            // Non-pharmacy users should only see Allocatr Insights at top level
            const subAppIds = result.menuItems[0].subApp.map(
                (sa: any) => sa.id
            );
            expect(subAppIds).toContain("national_forecast_projection_tool");
            expect(subAppIds).not.toContain("nfpt_rx_forecast");
            const allocatrInsights = result.menuItems[0].subApp.find(
                (sa: any) => sa.id === "national_forecast_projection_tool"
            );
            const subMenuIds = allocatrInsights.subMenu.map(
                (item: any) => item.id
            );
            expect(subMenuIds).toContain("nfpt_dashboard");
            expect(subMenuIds).toContain("nfpt_adjustment_worksheet");
            expect(subMenuIds).not.toContain("nfpt_rx_forecast");
        });

        it("should handle invalid JWT token gracefully", async () => {
            const invalidToken = "Bearer invalid.token.here";

            const result = await MenuItemService.getMenuItems(invalidToken);
            expect(result.menuItems).toBeDefined();
            expect(result.menuItems[0].contextId).toBe("category_excellence");

            // Should default to non-pharmacy behavior (show Dashboard and Adjustment Worksheet)
            const subAppIds = result.menuItems[0].subApp.map(
                (sa: any) => sa.id
            );
            expect(subAppIds).toContain("national_forecast_projection_tool");
            expect(subAppIds).not.toContain("nfpt_rx_forecast");
        });

        it("should fetch and merge menu items from MECEA_ENDPOINT when available", async () => {
            process.env.MECEA_ENDPOINT = "https://test-mecea-endpoint.com";

            const mockResponseData = {
                subApp: [
                    {
                        id: "external_app_1",
                        name: "External App 1",
                        url: "https://external-app-1.com"
                    }
                ]
            };

            mockedAxios.get.mockResolvedValueOnce({
                data: mockResponseData
            });

            const result = await MenuItemService.getMenuItems();

            expect(mockedAxios.get).toHaveBeenCalledWith(
                "https://test-mecea-endpoint.com/api/menu"
            );
            expect(result.menuItems[0].subApp).toContainEqual(
                expect.objectContaining({
                    id: "external_app_1"
                })
            );
        });

        it("should handle MECEA_ENDPOINT API call failure and fallback to local menu", async () => {
            process.env.MECEA_ENDPOINT = "https://test-mecea-endpoint.com";

            mockedAxios.get.mockRejectedValueOnce(new Error("Network error"));

            const result = await MenuItemService.getMenuItems();

            expect(mockedAxios.get).toHaveBeenCalledWith(
                "https://test-mecea-endpoint.com/api/menu"
            );
            expect(result.menuItems).toBeDefined();
            expect(result.menuItems[0].contextId).toBe("category_excellence");
        });

        it("should handle MECEA_ENDPOINT response without data property", async () => {
            process.env.MECEA_ENDPOINT = "https://test-mecea-endpoint.com";

            mockedAxios.get.mockResolvedValueOnce({
                data: null
            });

            const result = await MenuItemService.getMenuItems();

            expect(mockedAxios.get).toHaveBeenCalledWith(
                "https://test-mecea-endpoint.com/api/menu"
            );
            expect(result.menuItems).toBeDefined();
            expect(result.menuItems[0].contextId).toBe("category_excellence");
        });

        it("should handle MECEA_ENDPOINT response with empty subApp array", async () => {
            process.env.MECEA_ENDPOINT = "https://test-mecea-endpoint.com";

            const mockResponseData = {
                subApp: []
            };

            mockedAxios.get.mockResolvedValueOnce({
                data: mockResponseData
            });

            const result = await MenuItemService.getMenuItems();

            expect(mockedAxios.get).toHaveBeenCalledWith(
                "https://test-mecea-endpoint.com/api/menu"
            );
            expect(result.menuItems).toBeDefined();
            expect(result.menuItems[0].contextId).toBe("category_excellence");
        });
    });
});
