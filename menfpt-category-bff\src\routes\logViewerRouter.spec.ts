import express from "express";
import request from "supertest";
import logViewerRouter from "./logViewerRouter";
import * as fs from "fs";

jest.mock("fs");

describe("logViewerRouter", () => {
    const app = express();
    app.use(express.json());
    app.use("/logs", logViewerRouter);

    afterEach(() => {
        jest.resetAllMocks();
    });

    it("should return the log file if it exists (GET /logs/api/logs)", async () => {
        (fs.existsSync as jest.Mock).mockReturnValue(true);
        const sendFileMock = jest
            .spyOn(app.response, "sendFile")
            .mockImplementation(function (this: any, ...args: any[]) {
                this.statusCode = 200;
                this.end();
                return this;
            });
        const res = await request(app).get("/logs/api/logs");
        expect(fs.existsSync).toHaveBeenCalled();
        expect(sendFileMock).toHaveBeenCalled();
        expect(res.status).toBe(200);
    });

    it("should return 404 if the log file does not exist (GET /logs/api/logs)", async () => {
        (fs.existsSync as jest.Mock).mockReturnValue(false);
        const res = await request(app).get("/logs/api/logs");
        expect(res.status).toBe(404);
        expect(res.text).toContain("Log file not found");
    });

    it("should return the HTML file for GET /logs/", async () => {
        const sendFileMock = jest
            .spyOn(app.response, "sendFile")
            .mockImplementation(function (this: any, ...args: any[]) {
                this.statusCode = 200;
                this.end();
                return this;
            });
        const res = await request(app).get("/logs/");
        expect(sendFileMock).toHaveBeenCalled();
        expect(res.status).toBe(200);
    });

    it("should handle error if the HTML file does not exist (GET /logs/)", async () => {
        const sendFileMock = jest
            .spyOn(app.response, "sendFile")
            .mockImplementation(function (this: any, ...args: any[]) {
                this.statusCode = 404;
                this.end();
                return this;
            });
        const res = await request(app).get("/logs/");
        expect(sendFileMock).toHaveBeenCalled();
        // 404 is simulated by the mock
        expect(res.status).toBe(404);
    });
});
