import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";

import { createModule } from "graphql-modules";
import { SaveAdjustmentProvider } from "./providers/saveAdjustment.provider";
const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));
const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const SavedAdjustmentModule = createModule({
    id: "SaveAdjustment",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [SaveAdjustmentProvider]
});
export default SavedAdjustmentModule;
