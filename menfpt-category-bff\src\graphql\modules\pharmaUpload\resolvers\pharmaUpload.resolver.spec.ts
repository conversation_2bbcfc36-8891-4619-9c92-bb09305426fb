import { PharmaUploadResolver } from "./pharmaUpload.resolver";
import { PharmaUploadService } from "../services/pharmaUpload.service";

describe("PharmaUploadResolver", () => {
    let mockGetUploadedDocuments: jest.Mock;

    beforeEach(() => {
        mockGetUploadedDocuments = jest.fn();
        jest.spyOn(PharmaUploadService.prototype, "getUploadedDocuments").mockImplementation(mockGetUploadedDocuments);
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("getUploadedDocuments", () => {
        it("should call service with correct arguments", async () => {
            const mockResult = [{ id: 1, name: "doc1" }];
            mockGetUploadedDocuments.mockResolvedValue(mockResult);

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: 42,
                    fetchAll: true,
                },
            };

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(mockGetUploadedDocuments).toHaveBeenCalledWith("testUser", 42, true);
            expect(result).toBe(mockResult);
        });

        it("should handle missing args gracefully", async () => {
            mockGetUploadedDocuments.mockResolvedValue([]);

            const args = {};

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(mockGetUploadedDocuments).toHaveBeenCalledWith(undefined, undefined, undefined);
            expect(result).toEqual([]);
        });

        it("should propagate errors from the service", async () => {
            mockGetUploadedDocuments.mockRejectedValue(new Error("Service error"));

            await expect(
                PharmaUploadResolver.getUploadedDocuments({}, { getUploadedDocuments: {} })
            ).rejects.toThrow("Service error");
        });

        it("should handle null getUploadedDocuments argument", async () => {
            mockGetUploadedDocuments.mockResolvedValue([]);

            const args = {
                getUploadedDocuments: null
            };

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(mockGetUploadedDocuments).toHaveBeenCalledWith(undefined, undefined, undefined);
            expect(result).toEqual([]);
        });

        it("should handle undefined getUploadedDocuments argument", async () => {
            mockGetUploadedDocuments.mockResolvedValue([]);

            const args = {
                getUploadedDocuments: undefined
            };

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(mockGetUploadedDocuments).toHaveBeenCalledWith(undefined, undefined, undefined);
            expect(result).toEqual([]);
        });

        it("should handle partial arguments", async () => {
            const mockResult = { uploadedDocuments: [] };
            mockGetUploadedDocuments.mockResolvedValue(mockResult);

            const args = {
                getUploadedDocuments: {
                    user: "testUser"
                    // Missing fiscalWeekNbr and fetchAll
                }
            };

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(mockGetUploadedDocuments).toHaveBeenCalledWith("testUser", undefined, undefined);
            expect(result).toBe(mockResult);
        });

        it("should handle string fiscalWeekNbr", async () => {
            const mockResult = { uploadedDocuments: [] };
            mockGetUploadedDocuments.mockResolvedValue(mockResult);

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: "202401",
                    fetchAll: false
                }
            };

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(mockGetUploadedDocuments).toHaveBeenCalledWith("testUser", "202401", false);
            expect(result).toBe(mockResult);
        });

        it("should handle different parameter types", async () => {
            const mockResult = { uploadedDocuments: [] };
            mockGetUploadedDocuments.mockResolvedValue(mockResult);

            const testCases = [
                {
                    args: {
                        getUploadedDocuments: {
                            user: "",
                            fiscalWeekNbr: 0,
                            fetchAll: false
                        }
                    },
                    expected: ["", 0, false]
                },
                {
                    args: {
                        getUploadedDocuments: {
                            user: "<EMAIL>",
                            fiscalWeekNbr: "2024Q1",
                            fetchAll: true
                        }
                    },
                    expected: ["<EMAIL>", "2024Q1", true]
                },
                {
                    args: {
                        getUploadedDocuments: {
                            user: "спец_символы",
                            fiscalWeekNbr: "52",
                            fetchAll: false
                        }
                    },
                    expected: ["спец_символы", "52", false]
                }
            ];

            for (const testCase of testCases) {
                jest.clearAllMocks();

                await PharmaUploadResolver.getUploadedDocuments({}, testCase.args);

                expect(mockGetUploadedDocuments).toHaveBeenCalledWith(...testCase.expected);
            }
        });

        it("should handle service returning null", async () => {
            mockGetUploadedDocuments.mockResolvedValue(null);

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: 42,
                    fetchAll: true
                }
            };

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(result).toBeNull();
        });

        it("should handle service returning undefined", async () => {
            mockGetUploadedDocuments.mockResolvedValue(undefined);

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: 42,
                    fetchAll: true
                }
            };

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(result).toBeUndefined();
        });

        it("should handle large response data", async () => {
            const largeResponse = {
                uploadedDocuments: Array.from({ length: 1000 }, (_, i) => ({
                    fileName: `file${i}.xlsx`,
                    fileContent: `content${i}`
                }))
            };

            mockGetUploadedDocuments.mockResolvedValue(largeResponse);

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: 42,
                    fetchAll: true
                }
            };

            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);

            expect(result).toBe(largeResponse);
            expect(result.uploadedDocuments).toHaveLength(1000);
        });

        it("should handle different parent parameter values", async () => {
            const mockResult = { uploadedDocuments: [] };
            mockGetUploadedDocuments.mockResolvedValue(mockResult);

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: 42,
                    fetchAll: true
                }
            };

            // Test with different parent values
            const parentValues = [null, undefined, {}, { someField: "value" }];

            for (const parent of parentValues) {
                jest.clearAllMocks();

                const result = await PharmaUploadResolver.getUploadedDocuments(parent, args);

                expect(result).toBe(mockResult);
                expect(mockGetUploadedDocuments).toHaveBeenCalledWith("testUser", 42, true);
            }
        });

        it("should handle destructuring with additional fields", async () => {
            const mockResult = { uploadedDocuments: [] };
            mockGetUploadedDocuments.mockResolvedValue(mockResult);

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: 42,
                    fetchAll: true,
                    extraField: "should-be-ignored",
                    anotherField: 123
                }
            };

            await PharmaUploadResolver.getUploadedDocuments({}, args);

            // Should only pass the three expected parameters
            expect(mockGetUploadedDocuments).toHaveBeenCalledWith("testUser", 42, true);
        });

        it("should handle async service behavior", async () => {
            const mockResult = { uploadedDocuments: [] };

            // Simulate async delay
            mockGetUploadedDocuments.mockImplementation(
                () => new Promise(resolve => setTimeout(() => resolve(mockResult), 10))
            );

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: 42,
                    fetchAll: true
                }
            };

            const startTime = Date.now();
            const result = await PharmaUploadResolver.getUploadedDocuments({}, args);
            const endTime = Date.now();

            expect(result).toBe(mockResult);
            expect(endTime - startTime).toBeGreaterThanOrEqual(10);
        });

        it("should handle different error types from service", async () => {
            const errorTypes = [
                new Error("Network error"),
                new TypeError("Type error"),
                "String error",
                null,
                undefined,
                { message: "Object error" }
            ];

            for (const error of errorTypes) {
                jest.clearAllMocks();
                mockGetUploadedDocuments.mockRejectedValue(error);

                await expect(
                    PharmaUploadResolver.getUploadedDocuments({}, { getUploadedDocuments: {} })
                ).rejects.toBe(error);
            }
        });

        it("should maintain service instantiation on each call", async () => {
            const mockResult = { uploadedDocuments: [] };
            mockGetUploadedDocuments.mockResolvedValue(mockResult);

            const args = {
                getUploadedDocuments: {
                    user: "testUser",
                    fiscalWeekNbr: 42,
                    fetchAll: true
                }
            };

            // Call multiple times
            await PharmaUploadResolver.getUploadedDocuments({}, args);
            await PharmaUploadResolver.getUploadedDocuments({}, args);

            // Service should be called each time
            expect(mockGetUploadedDocuments).toHaveBeenCalledTimes(2);
        });
    });
});