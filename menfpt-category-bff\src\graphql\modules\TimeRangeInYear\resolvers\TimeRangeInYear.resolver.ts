import { TimeRangeInYearReq } from "src/graphql/__generated__/gql-ts-types";
import { ContextValue } from "../../../context";
import { TimeRangeInYearProvider } from "../providers/TimeRangeInYear.provider";

export enum AggregationRanges {
    WEEK = "Week",
    PERIOD = "Period",
    QUARTER = "Quarter"
};

export const aggregationLevelFields = {
    [AggregationRanges.PERIOD]: "fiscalPeriodNbr",
    [AggregationRanges.WEEK]: "fiscalWeekNbr",
    [AggregationRanges.QUARTER]: "fiscalQuarterNbr"
};

export const TimeRangeInYearResolver = async (
    parent: any,
    args: { timeRangeInYearReq: TimeRangeInYearReq },
    context: ContextValue
): Promise<any> => {
    const TimeRangeInYear = context?.injector?.get(
        TimeRangeInYearProvider.provide
    );
    const fiscalYearNumbers = args.timeRangeInYearReq?.fiscalYearNumber;
    if (!fiscalYearNumbers || fiscalYearNumbers.length === 0) {
        return [];
    }
    const promises: any = [];
    fiscalYearNumbers.forEach((fiscalYearNumber) => {
        if (!fiscalYearNumber || fiscalYearNumber < 1 || fiscalYearNumber > 9999) {
            throw new Error("Fiscal year number must be between 1 and 9999");
        }
        promises.push(TimeRangeInYear?.getCalendarData({ fiscalYearNumber }));
    });
    const response = await Promise.all(promises);
    const finalResponse = new Map<number, any>();
    const level = args.timeRangeInYearReq.level as AggregationRanges;
    response.forEach((calendarData: any) => {
        if (Array.isArray(calendarData)) {
            calendarData.forEach((item: any) => {
                switch (level) {
                    case AggregationRanges.QUARTER:
                        finalResponse.set(item.fiscalQuarterNumber, {
                            fiscalYearNumber: item.fiscalYearNumber,
                            fiscalQuarterNumber: item.fiscalQuarterNumber,
                            fiscalQuarterStartDate: item.fiscalQuarterStartDate,
                            fiscalQuarterEndDate: item.fiscalQuarterEndDate,
                            createTimestamp: item.createTimestamp,
                            updateTimestamp: item.updateTimestamp
                        });
                        break;
                    case AggregationRanges.PERIOD:
                        finalResponse.set(item.fiscalPeriodNumber, {
                            fiscalYearNumber: item.fiscalYearNumber,
                            fiscalQuarterNumber: item.fiscalQuarterNumber,
                            fiscalQuarterStartDate: item.fiscalQuarterStartDate,
                            fiscalQuarterEndDate: item.fiscalQuarterEndDate,
                            fiscalPeriodNumber: item.fiscalPeriodNumber,
                            fiscalPeriodStartDate: item.fiscalPeriodStartDate,
                            fiscalPeriodEndDate: item.fiscalPeriodEndDate,
                            createTimestamp: item.createTimestamp,
                            updateTimestamp: item.updateTimestamp
                        });
                        break;
                    case AggregationRanges.WEEK:
                        finalResponse.set(item.fiscalWeekNumber, {
                            fiscalYearNumber: item.fiscalYearNumber,
                            fiscalQuarterNumber: item.fiscalQuarterNumber,
                            fiscalQuarterStartDate: item.fiscalQuarterStartDate,
                            fiscalQuarterEndDate: item.fiscalQuarterEndDate,
                            fiscalPeriodNumber: item.fiscalPeriodNumber,
                            fiscalPeriodStartDate: item.fiscalPeriodStartDate,
                            fiscalPeriodEndDate: item.fiscalPeriodEndDate,
                            fiscalWeekNumber: item.fiscalWeekNumber,
                            fiscalWeekStartDate: item.fiscalWeekStartDate,
                            fiscalWeekEndDate: item.fiscalWeekEndDate,
                            createTimestamp: item.createTimestamp,
                            updateTimestamp: item.updateTimestamp
                        });
                        break;
                    default:
                        throw new Error("Invalid aggregation level");
                }
            });
        }
    });
    return Array.from(finalResponse.values()) || [];
};
