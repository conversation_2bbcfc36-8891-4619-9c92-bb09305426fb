{"name": "menfpt-category-bff", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "npm run clean && npm run codegen && tsc -p ./tsconfig.json && npm run copy-files", "clean": "rimraf dist/ && rimraf coverage/", "codegen": "graphql-codegen --config codegen.yml", "copy-files": "copyfiles -u 1 src/graphql/modules/**/gglTypes/*.graphql src/graphql/modules/**/gqlTypes/*.graphql src/assets/**/* dist/", "lint": "eslint . --fix", "prettier-format": "prettier --config .prettierrc 'src/**/*.ts' --write", "sonar": "node sonar-project.js", "start": "cross-env PORT=8081 node ./dist/index.js", "start:dev": "cross-env PORT=8081 NODE_ENV=development node ./dist/index.js", "start:local": "cross-env PORT=4001 NODE_ENV=development ts-node-dev --respawn ./src/index.ts", "start:prod": "cross-env PORT=8081 NODE_ENV=production node ./dist/index.js", "postinstall": "mkdir -p node_modules/@types/json-stable-stringify && echo \"declare module 'json-stable-stringify' { const stringify: (...args: any[]) => string; export = stringify; }\" > node_modules/@types/json-stable-stringify/index.d.ts", "test": "jest --coverage --coverageReporters=lcov", "test:coverage": "jest --coverage --coverageReporters=html", "test:coverage:terminal": "jest --coverage --coverageReporters=text --colors --silent", "test:singleFile": "npx jest \"src/graphql/modules/EPBCSSyncMonitorGraphQL/EPBCSSyncMonitor/connection.spec.ts\" --verbose --no-cache --detectOpenHandles"}, "jestSonar": {"reportPath": "coverage/"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">=24.0.0", "npm": ">=10.0.0"}, "devDependencies": {"@graphql-codegen/cli": "2.16.1", "@graphql-codegen/typescript": "2.8.5", "@graphql-codegen/typescript-resolvers": "2.7.10", "@graphql-tools/mock": "8.7.14", "@graphql-tools/schema": "9.0.12", "@types/cors": "^2.8.13", "@types/jest": "^29.2.4", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.14.191", "@types/node": "^18.11.11", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.8", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^5.45.1", "@typescript-eslint/parser": "^5.45.1", "casual": "^1.6.2", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.7.0", "jest-sonar-reporter": "^2.0.0", "prettier": "2.8.0", "rimraf": "^3.0.2", "sonarqube-scanner": "^2.9.1", "supertest": "^7.1.1", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.4.0"}, "dependencies": {"@apollo/datasource-rest": "^4.3.2", "@apollo/server": "^4.2.2", "@apollo/utils.keyvaluecache": "^2.1.0", "@azure/identity": "^4.10.2", "@azure/msal-node": "^3.7.3", "@azure/storage-blob": "^12.27.0", "@graphql-tools/load-files": "^6.6.1", "@microsoft/microsoft-graph-client": "^3.0.7", "apollo-datasource": "^3.3.2", "apollo-server-plugin-base": "^3.7.2", "apollo-server-types": "^3.8.0", "axios": "^1.2.1", "body-parser": "^1.20.2", "chokidar": "^3.5.3", "cors": "^2.8.5", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.7", "dotenv": "^16.3.1", "express": "^4.18.2", "graphql": "^16.6.0", "graphql-import-node": "0.0.5", "graphql-modules": "2.1.0", "http": "^0.0.1-security", "isomorphic-fetch": "^3.0.0", "isomorphic-unfetch": "^3.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "morgan": "^1.10.0", "pdf-lib": "^1.17.1", "reflect-metadata": "^0.1.13", "uuid": "^9.0.1", "winston": "^3.14.2"}}