import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";

import { createModule } from "graphql-modules";
import { DisplayDateProvider } from "./providers/DisplayDate.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));
const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const DisplayDateModule = createModule({
    id: "DisplayDate",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [DisplayDateProvider]
});
export default DisplayDateModule;
