import { getContentTypeFromFileName, streamToBuffer, isValidFileName } from './fileUtils';
import { Readable } from 'stream';

describe('fileUtils', () => {
    describe('getContentTypeFromFileName', () => {
        it('should return correct content types for known extensions', () => {
            expect(getContentTypeFromFileName('file.xlsx')).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        });

        it('should return default content type for unknown extensions', () => {
            expect(getContentTypeFromFileName('file.unknown')).toBe('application/octet-stream');
            expect(getContentTypeFromFileName('file')).toBe('application/octet-stream');
            expect(getContentTypeFromFileName('file.')).toBe('application/octet-stream');
        });

        it('should handle case insensitive extensions', () => {
            expect(getContentTypeFromFileName('file.XLSX')).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        });
    });

    describe('streamToBuffer', () => {
        it('should convert readable stream to buffer', async () => {
            const testData = 'Hello, World!';
            const stream = Readable.from([Buffer.from(testData)]);

            const result = await streamToBuffer(stream as NodeJS.ReadableStream);

            expect(result).toBeInstanceOf(Buffer);
            expect(result.toString()).toBe(testData);
        });

        it('should handle multiple chunks', async () => {
            const chunk1 = Buffer.from('Hello, ');
            const chunk2 = Buffer.from('World!');
            const stream = Readable.from([chunk1, chunk2]);

            const result = await streamToBuffer(stream as NodeJS.ReadableStream);

            expect(result).toBeInstanceOf(Buffer);
            expect(result.toString()).toBe('Hello, World!');
        });

        it('should handle string chunks by converting to buffer', async () => {
            const stream = Readable.from(['Hello, ', 'World!']);

            const result = await streamToBuffer(stream as NodeJS.ReadableStream);

            expect(result).toBeInstanceOf(Buffer);
            expect(result.toString()).toBe('Hello, World!');
        });

        it('should handle empty stream', async () => {
            const stream = Readable.from([]);

            const result = await streamToBuffer(stream as NodeJS.ReadableStream);

            expect(result).toBeInstanceOf(Buffer);
            expect(result.length).toBe(0);
        });
    });

    describe('isValidFileName', () => {
        it('should return true for valid filenames', () => {
            expect(isValidFileName('file.xlsx')).toBe(true);
            expect(isValidFileName('document.pdf')).toBe(true);
            expect(isValidFileName('data_file.csv')).toBe(true);
            expect(isValidFileName('file-name.txt')).toBe(true);
            expect(isValidFileName('file123.json')).toBe(true);
        });

        it('should return false for invalid filenames', () => {
            expect(isValidFileName('')).toBe(false);
            expect(isValidFileName('   ')).toBe(false);
            expect(isValidFileName('file../other.xlsx')).toBe(false);
            expect(isValidFileName('../file.xlsx')).toBe(false);
            expect(isValidFileName('file/path.xlsx')).toBe(false);
            expect(isValidFileName('file\\path.xlsx')).toBe(false);
        });

        it('should handle null and undefined', () => {
            expect(isValidFileName(null as any)).toBe(false);
            expect(isValidFileName(undefined as any)).toBe(false);
        });
    });
});
