# Environment Variables GraphQL Module

This module provides a GraphQL query to retrieve whitelisted environment variables from the application.

## Query

### GetEnvVariables

Returns only whitelisted environment variables or variables with `_EXP` suffix as key-value pairs.

**Query:**

```graphql
query GetEnvVariables {
    GetEnvVariables {
        variables
    }
}
```

**Response:**

```json
{
    "data": {
        "GetEnvVariables": {
            "variables": {
                "NODE_ENV": "development",
                "PORT": 4001,
                "CONFIG_EXP": "config_value",
                "API_ENDPOINT_EXP": "https://api.example.com"
            }
        }
    }
}
```

## Features

-   Returns only whitelisted environment variables from `process.env`
-   Returns environment variables with `_EXP` suffix (for experimental/exposed variables)
-   Filters out system environment variables (Windows, Unix, npm, Git, VS Code, etc.)
-   Automatically parses numeric values (strings like "123" become numbers)
-   Filters out undefined environment variables
-   Returns empty object if no whitelisted or \_EXP environment variables are set
-   Handles errors gracefully by returning empty object
-   Fully tested with comprehensive test coverage

## Security Features

-   **Whitelist-based access**: Only explicitly whitelisted environment variables are returned
-   **Suffix-based exposure**: Variables with `_EXP` suffix are automatically exposed for experimental/debugging purposes
-   **System variable filtering**: All system and sensitive environment variables are automatically filtered out
-   **No sensitive data exposure**: Secure properties like API keys, passwords, and tokens are not returned unless explicitly whitelisted

## Usage

The query can be used in any GraphQL client to retrieve whitelisted environment variable information for debugging, configuration validation, or administrative purposes.

## Adding Variables to Whitelist

To add environment variables to the whitelist, edit the `whitelistedEnvVars` Set in `src/graphql/modules/envVariables/providers/envVariables.provider.ts`:

```typescript
private readonly whitelistedEnvVars = new Set<string>([
    'NODE_ENV',
    'PORT',
    'DATABASE_URL',
    // Add your whitelisted variables here
]);
```

## Using \_EXP Suffix

For experimental or debugging purposes, you can suffix any environment variable with `_EXP` to automatically expose it:

```bash
# These will be returned by the API
export CONFIG_EXP="debug_config"
export API_ENDPOINT_EXP="https://api.example.com"
export DEBUG_MODE_EXP="true"

# These will NOT be returned (unless whitelisted)
export SECRET_API_KEY="secret123"
export DATABASE_PASSWORD="password123"
```

## Security Note

This implementation provides enhanced security by:

-   Only exposing whitelisted environment variables
-   Allowing controlled exposure of experimental variables via `_EXP` suffix
-   Automatically filtering out all system and potentially sensitive variables

Always review the whitelist before deploying to production and ensure no sensitive information is exposed.
