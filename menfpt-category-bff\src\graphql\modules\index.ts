import { createApplication } from "graphql-modules";
import DisplayDateModule from "./DisplayDate";
import WorkSheetFilterModule from "./workSheetTableFilter";
import UserInfoModule from "./userInfo";
import ForecastAdjustmentTableModule from "./forecastAdjustmentTable";
import ForecastChangeLogModule from "./getForecastChangeLog";
import SavedAdjustmentModule from "./saveAdjustment";
import QuartersInYearModule from "./TimeRangeInYear";
import EPBCSSyncMonitorModule from "./EPBCSSyncMonitorGraphQL";
import AllocatrDashboardTableModule from "./allocatrDashboardTable";
import PharmaUploadModule from "./pharmaUpload";
import PharmaDownloadModule from "./pharmaDownload";
import FileUploadPharmaModule from "./fileUploadPharma";
import EnvVariablesModule from "./envVariables";
import { adminModule } from "./Admin";

export const gqlApp = createApplication({
        modules: [
                DisplayDateModule,
                UserInfoModule,
                WorkSheetFilterModule,
                ForecastAdjustmentTableModule,
                ForecastChangeLogModule,
                SavedAdjustmentModule,
                QuartersInYearModule,
                EPBCSSyncMonitorModule,
                AllocatrDashboardTableModule,
                PharmaUploadModule,
                PharmaDownloadModule,
                FileUploadPharmaModule,
                EnvVariablesModule,
                adminModule
        ]
});
