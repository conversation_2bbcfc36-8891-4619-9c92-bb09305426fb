import { fetchJobData, DEFAULT_JOB_NAME } from './connection';

export async function fetchDatabricksJobData(
  jobIdOrName: number | string = DEFAULT_JOB_NAME ?? ''
): Promise<any> {
  try {
    const result = await fetchJobData(jobIdOrName, false);
    return result.output;
  } catch (error) {
    console.error('Error fetching EPBCSSyncMonitor job data:', error);
    throw error;
  }
}

export async function fetchDatabricksJobDataWithStatus(
  jobIdOrName: number | string = DEFAULT_JOB_NAME ?? ''
): Promise<any> {
  try {
    return await fetchJobData(jobIdOrName, true);
  } catch (error) {
    console.error('Error fetching EPBCSSyncMonitor job data with status:', error);
    throw error;
  }
}
