import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";

import { createModule } from "graphql-modules";
import { EnvVariablesProvider } from "./providers/envVariables.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));

const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const EnvVariablesModule = createModule({
    id: "EnvVariables",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [EnvVariablesProvider]
});

export default EnvVariablesModule; 