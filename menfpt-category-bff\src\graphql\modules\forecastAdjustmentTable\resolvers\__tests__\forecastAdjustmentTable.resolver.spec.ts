import 'reflect-metadata';
import { ForecastAdjustmentTableResolver } from '../forecastAdjustmentTable.resolver';
import { currentQuarterCalendarData, expectedResponse, getServiceMocks, previousQuarterCalendarData, previousQuarterCalendarInfo, worksheetTableRequest } from '../__mocks__/forecastAdjustmentTable-mock';
import * as providers from '../../providers/forecastAdjustmentTable.provider';

jest.mock('../../providers/forecastAdjustmentTable.provider');

describe('ForecastAdjustmentTableResolver', () => {
    let context: any;
    let mockCalendarService: any;
    let mockForecastService: any;

    beforeEach(() => {
        mockCalendarService = {
            getCalendarData: jest.fn()
        };
        mockForecastService = {
            getWorksheetData: jest.fn()
        };
        context = {
            injector: {
                get: jest.fn((provider) => {
                    if (provider === providers.CalendarServiceProvider.provide) return mockCalendarService;
                    if (provider === providers.ForecastProvider.provide) return mockForecastService;
                })
            }
        };

        mockCalendarService.getCalendarData.mockResolvedValueOnce(currentQuarterCalendarData);
        mockCalendarService.getCalendarData.mockResolvedValueOnce(previousQuarterCalendarInfo);
        mockCalendarService.getCalendarData.mockResolvedValueOnce(previousQuarterCalendarData);
        mockForecastService.getWorksheetData.mockImplementation((req: any) => {
            return new Promise((resolve) => {
                resolve(getServiceMocks(req).data);
            });
        });
    });

    it('should return adjustmentWorksheetData and forecastData', async () => {

        const args = { ...worksheetTableRequest };
        const result = await ForecastAdjustmentTableResolver(null, args, context);

        expect(result).toHaveProperty('adjustmentWorksheetData');
        expect(result).toHaveProperty('forecastData');
        expect(mockForecastService.getWorksheetData).toHaveBeenCalledTimes(13);
        expect(mockCalendarService.getCalendarData).toHaveBeenCalledTimes(3);
        expect(result.adjustmentWorksheetData).toEqual(expectedResponse);
    });
});