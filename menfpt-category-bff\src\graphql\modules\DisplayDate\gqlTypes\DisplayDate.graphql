input DisplayDateReq {
    date: String
    qtrNumbersArr: [Int]
}

type DisplayDateRes {
    fiscalYearNumber: Int
    fiscalWeekNumber: Int
    fiscalQuarterNumber: Int
    fiscalPeriodNumber: Int
    fiscalQuarterStartDate: String
    fiscalQuarterEndDate: String
    fiscalWeekStartDate: String
    fiscalWeekEndDate: String
    fiscalPeriodStartDate: String
    fiscalPeriodEndDate: String
    createTimestamp: String
    updateTimestamp: String
    fiscalPeriodLockoutDate: String
    fiscalPeriodCertificationDate: String
    calendarDetails: [DisplayDateRes]
}
type Query {
    getDisplayDate(displayDateReq: DisplayDateReq): DisplayDateRes
}
