import "reflect-metadata";
import { TimeRangeInYearResolver } from "../TimeRangeInYear.resolver";
import { TimeRangeInYearProvider } from "../../providers/TimeRangeInYear.provider";

interface ContextValue {
    injector: {
        get: <T>(provider: any) => T;
    };
}

const mockCalendarService = {
    getCalendarData: jest.fn() as jest.Mock<Promise<any>, [any]>
};

const mockContext: ContextValue = {
    injector: {
        get: jest.fn(() => mockCalendarService) as <T>(provider: any) => T
    }
};

describe("TimeRangeInYearResolver", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should call getCalendarData with the correct parameters", async () => {
        const args = {
            timeRangeInYearReq: {
                fiscalYearNumber: [2024]
            }
        };

        await TimeRangeInYearResolver({}, args, mockContext as any);

        expect(mockContext.injector.get).toHaveBeenCalledWith(
            TimeRangeInYearProvider.provide
        );
        expect(mockCalendarService.getCalendarData).toHaveBeenCalledWith(
            { fiscalYearNumber: args.timeRangeInYearReq.fiscalYearNumber[0] }
        );
    });

    it("should return the response from getCalendarData", async () => {
        const mockResponse = [
            {
                fiscalYearNumber: 2024,
                fiscalQuarterNumber: 202401,
                fiscalQuarterStartDate: "02/25/2024",
                fiscalQuarterEndDate: "06/15/2024",
                createTimestamp: "2025-02-18T08:43:04.543Z",
                updateTimestamp: "2025-02-18T08:43:04.543Z"
            }
        ];

        mockCalendarService.getCalendarData.mockResolvedValueOnce(mockResponse);

        const result = await TimeRangeInYearResolver(
            {},
            { timeRangeInYearReq: { fiscalYearNumber: [2024], level: 'Quarter' } },
            mockContext as any
        );

        expect(result).toEqual(mockResponse);
    });

    it("should return an empty array if getCalendarData returns null or undefined", async () => {
        mockCalendarService.getCalendarData.mockResolvedValueOnce(null);

        const result = await TimeRangeInYearResolver(
            {},
            { timeRangeInYearReq: { fiscalYearNumber: [2024] } },
            mockContext as any
        );

        expect(result).toEqual([]);
    });

    it("should handle when service is not available", async () => {
        const contextWithoutService: ContextValue = {
            injector: {
                get: jest.fn(() => undefined) as <T>(provider: any) => T
            }
        };

        const result = await TimeRangeInYearResolver(
            {},
            { timeRangeInYearReq: { fiscalYearNumber: [2024] } },
            contextWithoutService as any
        );

        expect(result).toEqual([]);
    });
});
