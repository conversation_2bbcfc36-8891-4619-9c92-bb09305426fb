export const worksheetRawData = [
    {
        line1PublicToSalesNbr: 1,
        line5BookGrossProfitNbr: 2,
        line5BookGrossProfitPct: 0.1,
        line5MarkDownsNbr: 3,
        line5MarkDownsPct: 0.2,
        line5ShrinkNbr: 4,
        line5ShrinkPct: 0.3,
        line5RealGrossProfitNbr: 5,
        line5RealGrossProfitPct: 0.4,
        line6SuppliesPackagingNbr: 6,
        line7RetailsAllowancesNbr: 7,
        line7RetailsAllowancesPct: 0.5,
        line7RetailsSellingAllowancesNbr: 8,
        line7RetailsSellingAllowancesPct: 0.6,
        line7RetailsNonSellingAllowancesNbr: 9,
        line7RetailsNonSellingAllowancesPct: 0.7,
        line8RealGrossProfitNbr: 10,
        line8RealGrossProfitPct: 0.8,
        comment: 'test',
        forecastType: 'type',
        lastUpdatedUserRole: 'role',
        reason: 'reason',
        state: 'state',
        versionNbr: 1,
        createdTs: '2024-01-01',
        updatedTs: '2024-01-02',
        createdBy: 'user1',
        updatedBy: 'user2',
        fiscalYearNbr: 2024,
        fiscalQuarterNbr: 2,
        fiscalPeriodNbr: 3,
        fiscalWeekNbr: 4,
    },
];

export const expectedResponse = {
    aggregatedLevel: 'Weeks',
    subRow: 'Actuals',
    line1PublicToSalesNbr: 1,
    line5BookGrossProfitNbr: 2,
    line5BookGrossProfitPct: 0.1,
    line5MarkDownsNbr: 3,
    line5MarkDownsPct: 0.2,
    line5ShrinkNbr: 4,
    line5ShrinkPct: 0.3,
    line5RealGrossProfitNbr: 5,
    line5RealGrossProfitPct: 0.4,
    line6SuppliesPackagingNbr: 6,
    line7RetailsAllowancesNbr: 7,
    line7RetailsAllowancesPct: 0.5,
    line7RetailsSellingAllowancesNbr: 8,
    line7RetailsSellingAllowancesPct: 0.6,
    line7RetailsNonSellingAllowancesNbr: 9,
    line7RetailsNonSellingAllowancesPct: 0.7,
    line8RealGrossProfitNbr: 10,
    line8RealGrossProfitPct: 0.8,
    comment: 'test',
    forecastType: 'type',
    lastUpdatedUserRole: 'role',
    reason: 'reason',
    state: 'state',
    versionNbr: 1,
    createdTs: '2024-01-01',
    updatedTs: '2024-01-02',
    createdBy: 'user1',
    updatedBy: 'user2',
    fiscalQuarterNbr: 2,
    fiscalPeriodNbr: 3,
    fiscalWeekNbr: 0, // because fiscalPeriodNbr is present
}

export const firstData = {
    line1PublicToSalesNbr: 10,
    line5BookGrossProfitNbr: 20,
    line5BookGrossProfitPct: 0.5,
    line5MarkDownsNbr: 30,
    line5MarkDownsPct: 0.4,
    line5ShrinkNbr: 40,
    line5ShrinkPct: 0.3,
    line5RealGrossProfitNbr: 50,
    line5RealGrossProfitPct: 0.2,
    line6SuppliesPackagingNbr: 60,
    line7RetailsAllowancesNbr: 70,
    line7RetailsAllowancesPct: 0.1,
    line7RetailsSellingAllowancesNbr: 80,
    line7RetailsSellingAllowancesPct: 0.2,
    line7RetailsNonSellingAllowancesNbr: 90,
    line7RetailsNonSellingAllowancesPct: 0.3,
    line8RealGrossProfitNbr: 100,
    line8RealGrossProfitPct: 0.4,
    fiscalYearNbr: 2024,
    fiscalPeriodNbr: 3,
    fiscalWeekNbr: 4,
    fiscalQuarterNbr: 2,
    fiscalWeekEnding: '2024-01-01',
    comment: 'test',
    forecastType: 'type',
    reason: 'reason',
    state: 'state',
};

export const secondData = {
    line1PublicToSalesNbr: 1,
    line5BookGrossProfitNbr: 2,
    line5BookGrossProfitPct: 0.1,
    line5MarkDownsNbr: 3,
    line5MarkDownsPct: 0.2,
    line5ShrinkNbr: 4,
    line5ShrinkPct: 0.1,
    line5RealGrossProfitNbr: 5,
    line5RealGrossProfitPct: 0.05,
    line6SuppliesPackagingNbr: 6,
    line7RetailsAllowancesNbr: 7,
    line7RetailsAllowancesPct: 0.01,
    line7RetailsSellingAllowancesNbr: 8,
    line7RetailsSellingAllowancesPct: 0.02,
    line7RetailsNonSellingAllowancesNbr: 9,
    line7RetailsNonSellingAllowancesPct: 0.03,
    line8RealGrossProfitNbr: 10,
    line8RealGrossProfitPct: 0.04,
    fiscalYearNbr: 2023,
    fiscalPeriodNbr: 2,
    fiscalWeekNbr: 3,
    fiscalQuarterNbr: 1,
    fiscalWeekEnding: '2023-01-01',
    comment: 'test2',
    forecastType: 'type2',
    reason: 'reason2',
    state: 'state2',
};