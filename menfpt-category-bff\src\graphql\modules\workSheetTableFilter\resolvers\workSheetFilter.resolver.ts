import { WorkSheetFilterReq } from "src/graphql/__generated__/gql-ts-types";

import { ContextValue } from "src/graphql/context";

import { WorkSheetFilterProvider } from "../providers/workSheetFilter.provider";

//import * as dotenv from "dotenv";
//dotenv.config({ path: ".env.development" });
export const WorkSheetFilterResolver = async (
    parent: any,

    args: { workSheetFilterRequest: WorkSheetFilterReq },

    context: ContextValue
): Promise<any> => {
    const { workSheetFilterRequest } = args;

    const roleMappingService = context?.injector?.get(
        WorkSheetFilterProvider.provide
    );
    let roleMappingData: any = [];
    let departmentCategory: any = [];
    // Fetch data from desk_user/search

    roleMappingData = await roleMappingService?.getWorkSheetFilter(
        workSheetFilterRequest
    );

    // const userRole = rawData?.user_role;

    // console.log("Extracted User Role:", userRole);

    // const roles = JSON.parse(process.env.NFPT_USER_ROLES || "[]");

    // console.log("Roles from ENV:", roles);
    // if (roles.includes(userRole)) {
    //     console.log("Roles are the same");

    //     departmentCategory =
    //         await WorkSheetFilterService?.fetchdepartmentCategory(
    //             workSheetFilterRequest
    //         );
    // } else {
    //     console.log("Roles are different");
    // }

    return formattedData(roleMappingData) || [];
};

const formattedData = (roleMappingData: any) => {
    let formattedData: any = [];

    if (
        roleMappingData &&
        roleMappingData?.smicData &&
        roleMappingData?.smicData?.length > 0
    ) {
        roleMappingData?.smicData?.forEach((element: any) => {
            formattedData.push({
                divisionId: element?.division_id ?? null,
                divisionName: element?.division_nm ?? null,
                deptId: element?.dept_id ?? null,
                deptName: element?.dept_nm ?? null,
                retailSectionCd: element?.retail_section_cd ?? null,
                retailSectionName: element?.retail_section_nm ?? null,
                smicGroupCd: element?.smic_group_cd ?? null,
                smicGroupDesc: element?.smic_group_desc ?? null,
                smicCategoryCd: element?.smic_category_cd ?? null,
                smicCategoryDesc: element?.smic_category_desc ?? null,
                smicCategoryId: element?.smic_category_id ?? null,
                deskId: element?.desk_id ?? null,
                deskName: element?.desk_nm ?? null
            });
        });
        // if (departmentCategory && departmentCategory?.length>0){ let divisions: any = [];
        //         formattedData.map((item: any) => {
        //             divisions.push({
        //                 divisionId: item?.divisionId,

        //                 divisionName: item?.divisionName
        //             });
        //         });
        //         const uniqueDivisions = divisions.reduce((acc: any, current: any) => {
        //             const x = acc.find(
        //                 (item: any) =>
        //                     item.divisionId === current.divisionId &&
        //                     item.divisionName === current.divisionName
        //             );
        //             if (!x) {
        //                 acc.push(current);
        //             }
        //             return acc;
        //         }, []);
        //         departmentCategory?.forEach((element: any) => {
        //             uniqueDivisions.forEach((division: any) => {
        //                 formattedData.push({
        //                     divisionId: division?.divisionId ?? null,
        //                     divisionName: division?.divisionName ?? null,
        //                     deptId: element?.dept_id ?? null,
        //                     deptName: element?.dept_nm ?? null,
        //                     retailSectionCd: element?.retail_section_cd ?? null,
        //                     retailSectionName: element?.retail_section_nm ?? null,
        //                     smicGroupCd: element?.smic_group_cd ?? null,
        //                     smicGroupDesc: element?.smic_group_desc ?? null,
        //                     smicCategoryCd: element?.smic_category_cd ?? null,
        //                     smicCategoryDesc: element?.smic_category_desc ?? null,
        //                     smicCategoryId: element?.smic_category_id ?? null,
        //                     deskId: null,
        //                     deskName: null
        //                 });
        //             });
        //         });
        //     }

        return {
            smicData: formattedData,
            userId: roleMappingData?.user_id,
            userName: roleMappingData?.user_nm,
            userEmail: roleMappingData?.user_email,
            userRole: roleMappingData?.user_role,
            createdTimeStamp: roleMappingData?.created_ts,
            updatedTimeStamp: roleMappingData?.update_ts,
            createdBy: roleMappingData?.created_by,
            updatedBy: roleMappingData?.updated_by
        };
    }
};
