import "reflect-metadata";
import {
    ApolloServer,
    GraphQLRequestContext,
    ApolloServerPlugin
} from "@apollo/server";
import { expressMiddleware } from "@apollo/server/express4";
import { ApolloServerPluginDrainHttpServer } from "@apollo/server/plugin/drainHttpServer";
import express from "express";
import http from "http";
import cors from "cors";
import bodyParser from "body-parser";
import "graphql-import-node";
import dotenv from "dotenv";
import routes from "./routes/MenuItemRouter";
import helpIconRouter from "./routes/helpIconRouter";
import pharmaDownloadRouter from "./routes/pharmaDownloadRouter";
import path from "path";

if ("local" === process.env.NODE_ENV) {
    dotenv.config({ path: `.env.${process.env.NODE_ENV}` });
}

import { gqlApp } from "./graphql/modules/index";
import { ContextValue } from "./graphql/context";
import noop from "lodash/noop";
import { ApiCallTracker } from "./graphql/shared/utils/apiCallTracker";
import logViewerRouter from "./routes/logViewerRouter";

const apiCallTrackerPlugin: ApolloServerPlugin<ContextValue> = {
    async requestDidStart(requestContext: GraphQLRequestContext<ContextValue>) {
        return {
            async willSendResponse(
                requestContext: GraphQLRequestContext<ContextValue>
            ) {
                if (requestContext.contextValue.apiCallTracker) {
                    requestContext.contextValue.apiCallTracker.logSummary();
                }
            }
        };
    }
};

export const bootstrap = async () => {
    const app = express();
    const morgan = require("morgan");
    const { parse } = require("graphql");

    const httpServer = http.createServer(app);

    const server = new ApolloServer<ContextValue>({
        gateway: {
            async load() {
                return { executor: gqlApp.createApolloExecutor() };
            },
            onSchemaLoadOrUpdate(callback) {
                callback({ apiSchema: gqlApp.schema } as never);
                return () => {
                    noop();
                };
            },
            async stop() {
                noop();
            }
        },
        plugins: [
            ApolloServerPluginDrainHttpServer({ httpServer }),
            apiCallTrackerPlugin
        ],
        includeStacktraceInErrorResponses: "production" !== process.env.NODE_ENV
    });

    process.on("unhandledRejection", (reason, promise) => {
        console.error("Unhandled Rejection at:", promise, "reason:", reason);
        // Optionally log the error or send it to an error tracking service
    });
    process.on("uncaughtException", (error) => {
        console.error("Uncaught Exception:", error);
        // Optionally log the error or send it to an error tracking service
    });

    morgan.token("graphql-query", function getGraphqlQuery(req: any) {
        if (req.body && req.body.query) {
            try {
                const methodName = parse(req.body.query);
                const definition = methodName.definitions[0];
                return definition && definition.name ? definition.name.value : 'anonymous';
            } catch (error) {
                return 'parse-error';
            }
        }
        return 'no-query';
    });

    morgan.token("ui-trace-id", function (req: any, res: any) {
        return req.headers["ui-trace-id"];
    });

    app.use(
        morgan(
            "API call info(bff) - :method :url :status :res[content-length] - :response-time ms - :graphql-query - :ui-trace-id"
        )
    );

    await server.start();

    // Set up our Express middleware to handle CORS, body parsing,
    // and our expressMiddleware function.
    app.use(cors());
    app.get(
        "/menfpt-category-bff/health",
        (req: express.Request, res: express.Response) => {
            res.json({
                status: "UP",
                components: {
                    livenessState: { status: "UP" },
                    ping: { status: "UP" },
                    readinessState: { status: "UP" }
                }
            });
        }
    );
    app.use("/menfpt-category-bff/api", (req, res, next) =>
        routes(req, res, next)
    );

    app.use(
        ["/menfpt-category-bff/helpIcon", "/menfpt-category-bff/api/helpIcon"],
        helpIconRouter
    );

    // Add pharmacy download API routes
    app.use("/menfpt-category-bff/api/pharmacy", pharmaDownloadRouter);

    if (process.env.NODE_ENV !== "production") {
        app.use("/menfpt-category-bff/logs", logViewerRouter);
    }

    app.use(
        "/menfpt-category-bff",
        cors<cors.CorsRequest>(),
        bodyParser.json({
            limit: '50mb'
        }),
        bodyParser.urlencoded({
            limit: '50mb',
            extended: true
        }),
        expressMiddleware(server, {
            context: async ({ req }) => {
                const authToken =
                    req.headers.authorization || req.headers.Authorization;
                const uiTraceId = req.headers["ui-trace-id"];

                return {
                    authToken: Array.isArray(authToken)
                        ? authToken[0]
                        : authToken,
                    uiTraceId: Array.isArray(uiTraceId)
                        ? uiTraceId[0]
                        : uiTraceId,
                    apiCallTracker: new ApiCallTracker()
                };
            }
        })
    );

    const assetsPath = path.join(__dirname, "assets");

    app.get("*", (req: express.Request, res: express.Response) => {
        res.json({ message: "The requested route does not exist" });
    });

    const port = process.env.PORT || 8081;

    await new Promise<void>((resolve) =>
        httpServer.listen({ port: port }, resolve)
    );
    //console.log(`🚀 Server ready at http://localhost:${port}/`);
};
