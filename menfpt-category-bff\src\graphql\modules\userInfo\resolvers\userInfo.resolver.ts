import { ContextValue } from "../../../context";
import { UserInfoProvider } from "../providers/userInfo.provider";
import { } from "../../../__generated__/gql-ts-types";
import get from "lodash/get";

export const UserInfoResolver = async (
    parent: any,
    args: any,
    context: ContextValue
): Promise<any> => {
    const entitlementService = context?.injector?.get(
        UserInfoProvider.provide
    );

    const data = await entitlementService?.fetchUserInfoDetails(
        get(context, "authToken", "")
    );

    return data || {};
};

