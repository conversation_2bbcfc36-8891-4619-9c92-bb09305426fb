import * as winston from "winston";
import * as path from "path";
import * as fs from "fs";
import * as chokidar from "chokidar";

const isDevEnvironment = process.env.NODE_ENV !== "production";

const sharedFormat = winston.format.combine(
    winston.format.errors({ stack: true }),
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss.SSS" })
);

const printf = winston.format.printf((info) => {
    const message = `${info.timestamp} ${info.level}: ${info.message}`;
    return info.stack ? `${message}\n${info.stack}` : message;
});

const consoleTransport = new winston.transports.Console({
    format: winston.format.combine(
        winston.format.colorize(),
        sharedFormat,
        printf
    )
});

const logger = winston.createLogger({
    level: "info",
    format: winston.format.combine(sharedFormat, printf),
    transports: [consoleTransport]
});

if (isDevEnvironment) {
    const logFilePath = path.resolve(__dirname, "../../../../bff-logs.log");

    const logDir = path.dirname(logFilePath);
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }

    let fileTransport = new winston.transports.File({
        filename: logFilePath,
        options: { flags: "w" }
    });

    logger.add(fileTransport);

    const watcher = chokidar.watch(logFilePath, {
        persistent: true,
        ignoreInitial: true
    });

    watcher.on("unlink", () => {
        logger.info("Log file was deleted. Re-creating file transport...");
        logger.remove(fileTransport);
        fileTransport = new winston.transports.File({
            filename: logFilePath,
            options: { flags: "a" }
        });
        logger.add(fileTransport);
        logger.info("New file transport created for logs.");
    });

    watcher.on("error", (error) => logger.error(`Watcher error: ${error}`));
}

export const colorizer = winston.format.colorize();

export default logger;
