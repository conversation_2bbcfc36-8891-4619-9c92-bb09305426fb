import * as fs from "fs";
import * as path from "path";
import axios from "axios";
import menuItems from "../resource/menuItem.json";
import { isPharmacyUser, filterMenuItemsByUserRole } from "../util/menuFilterUtils";

class MenuItemService {
    public async getMenuItems(authToken?: string) {        
        const isPharmacy = isPharmacyUser(authToken || "");
        
        try {
            if(!process.env.MECEA_ENDPOINT){
                return filterMenuItemsByUserRole(menuItems, isPharmacy);
            }
            
            const menuItemsObject = JSON.parse(JSON.stringify(menuItems));
            const { data } = await axios.get(
                `${process.env.MECEA_ENDPOINT}/api/menu`
            );
            if (data) {
                menuItemsObject.menuItems[0].subApp.push(
                    ...(data.subApp || [])
                );
            }
            
            return filterMenuItemsByUserRole(menuItemsObject, isPharmacy);
        } catch (error) {
            return filterMenuItemsByUserRole(menuItems, isPharmacy);
        }
    }
}

export default new MenuItemService();
