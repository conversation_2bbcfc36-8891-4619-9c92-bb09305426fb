<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Download Help Document</title>
  <script>
    // Trigger the download of the PDF
    window.onload = () => {
      const pdfData = "{{PDF_DATA}}"; // Placeholder for PDF data
      const link = document.createElement('a');
      link.href = `data:application/pdf;base64,${pdfData}`;
      link.download = "helpDocument.pdf";
      link.target = "_self"; // Ensure the download does not navigate the main page
      link.click();
    };
  </script>
</head>
<body>
  <p>Preparing your download...</p>
</body>
</html>
