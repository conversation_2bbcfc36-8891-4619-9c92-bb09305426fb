# Ubuntu Base image with nodejs24.x
FROM node:24

USER root
# Adding application specific group and user
RUN groupadd -g 1999 menfpt-grp && useradd -r -u 1999 -g root menfpt

# Creating app directory and switching
WORKDIR /app

# Adding dependency and npm registry related files
COPY package*.json ./
# COPY package-lock.json .
COPY .npmrc .

# Installing the dependency node modules
# RUN npm ci --only=production

# Copying dist directory to app directory
COPY . /app/

# Copy the feature flags YML file
COPY feature-flags.yml /app/feature-flags.yml

# Changing the user and/or group ownership of an app directory
RUN chown menfpt:menfpt-grp /app -R

# Exposing container's port to the outside world
#EXPOSE 4001
EXPOSE 8081

# Switching user to application user from root
USER menfpt

# Running the application
CMD [ "/bin/sh", "npmscript.sh" ]
