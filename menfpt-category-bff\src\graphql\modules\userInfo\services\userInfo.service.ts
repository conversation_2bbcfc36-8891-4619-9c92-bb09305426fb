import { Injectable } from "graphql-modules";
import { BaseAPIService } from "../../../shared/classes/BaseAPI.service";
import {} from "../../../__generated__/gql-ts-types";
import get from "lodash/get";
import jwt from "jsonwebtoken";
import { MSP_DATA_GET_FAILURE } from "../../../shared/constants/errorCodes";
import { GraphQLError } from "graphql";
import { format } from "path";

@Injectable()
export class UserInfoService extends BaseAPIService {
    async fetchUserInfoDetails(token: string): Promise<any> {
        let response;
        try {
            if (!token) {
                return {};
            }
            response = await formatData(token);
        } catch (error) {
            throw new GraphQLError("Error while fetching user info data", {
                extensions: {
                    errorType: MSP_DATA_GET_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
        return response;
    }
}

const getADGroupFormToken = async (data: any) => {
    const tokenParts = data.split(" ");
    const decoded = jwt.decode(tokenParts[1]) || jwt.decode(tokenParts[0]);
    const groups = (get(decoded, "groups") || []) as string[];
    let relevantGroups = groups.filter(
        (group) => group.includes("menfpt") || group.includes("az-memsp")
    );
    let role = "";
    for (let i = 0; i < relevantGroups.length; i++) {
        const group = relevantGroups[i];
        console.log({ group, adGroup: process.env.PHARMACY_AD_GROUP });
        let adGroup = group.split("-");
        if (adGroup.length > 0) {
            switch (true) {
                case adGroup.includes("divisionmanager"):
                    return "DivisionalManager";
                case adGroup.includes("ncd"):
                    return "NationalManager";
                case adGroup.includes("fpa"):
                    return "FPA";
                case group === process.env.PHARMACY_AD_GROUP:
                    return "pharmacyUser";
                default:
                    role = "viewer";
            }
        }
    }

    return role;
};

const getUserId = (decoded: any) => {
    return get(decoded, "upn").split("@")[0];
};

const formatData = async (data: any) => {
    const tokenParts = data.split(" ");
    const decoded = jwt.decode(tokenParts[1]) || jwt.decode(tokenParts[0]);
    if (decoded) {
        const getUserName: any = get(decoded, "name");
        const userId: any = getUserId(decoded);
        const getUserEmail: any = get(decoded, "preferred_username");
        const userRole = await getADGroupFormToken(data);
        const userPermissions = {
            canView: true,
            canEdit: false
        };
        if (
            userRole === "NationalManager" ||
            userRole === "DivisionalManager"
        ) {
            userPermissions.canEdit = true;
        }
        const response = {
            userName: getUserName,
            userId: userId,
            userEmail: getUserEmail,
            userRole: userRole,
            userPermissions: userPermissions
        };
        return response;
    }
};
