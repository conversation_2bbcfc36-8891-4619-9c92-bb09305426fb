import axios from "axios";
import get from "lodash/get";
import { ExecutionContext } from "graphql-modules";
import { v4 as uuidv4 } from "uuid";
import * as path from "path";
import { API_END_POINTS } from "./APIEndPoints";
import dotenv from "dotenv";
import { set } from "lodash";
const filePath = path.resolve(__dirname, "../../../../.env.development");
dotenv.config({ path: filePath });
import * as winston from "winston";
import { logApiCall, createErrorPayload } from "../utils/logApiCall";

interface ErrorPayload {
    httpStatus: number | null;
    errorCode: string;
    name: string;
    message: string;
    url: string;
}

const maskSensitiveInfo = winston.format((info) => {
    if (info.payload) {
        const maskedPayload = { ...info.payload, sensitiveField: "***" };
        info.message += ` - payload: ${JSON.stringify(maskedPayload)}`;
    }
    return info;
});

const logger = winston.createLogger({
    level: "info",
    format: winston.format.combine(
        winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss.SSS" }),
        maskSensitiveInfo(),
        winston.format.simple()
    ),
    transports: [new winston.transports.Console()]
});

// Add colorizer for error logs
const colorizer = winston.format.colorize();

function getServiceEndPoints(): any {
    const getServiceEndPoints = Object.entries(process.env).filter(([key]) =>
        key.endsWith("ENDPOINT")
    );
    const serviceEndPoints = getServiceEndPoints.map(([key, value]) => ({
        [key]: value
    }));

    return serviceEndPoints;
}

export class BaseAPIService {
    createRequestURL(url?: any) {
        let setURL = `${process.env.CALENDAR_ENDPOINT}${url}`;
        //let setURL = "";
        const getServiceEndPointsFromEvnVar = getServiceEndPoints().map(
            (endpoint: any) => {
                const value =
                    endpoint[Object.keys(endpoint)[0]].split("menfpt/")[1];
                if (!value) return null;
                const domainEndpoint = value?.includes("/")
                    ? value.replace("/", "")
                    : value;
                return [
                    {
                        domainEndpoint: domainEndpoint,
                        baseURL: endpoint[Object.keys(endpoint)[0]]
                    }
                ];
            }
        ).filter((i: any) => i !== null);
        const setUrlMapping: {
            [key: string]: { endpoints: string[]; baseURL: string };
        } = {};

        for (let key in API_END_POINTS) {
            if (API_END_POINTS.hasOwnProperty(key)) {
                if (
                    getServiceEndPointsFromEvnVar.some(
                        (endpoint: any) => endpoint[0].domainEndpoint === key
                    )
                ) {
                    setUrlMapping[key] = {
                        endpoints: API_END_POINTS[key],
                        baseURL: getServiceEndPointsFromEvnVar.find(
                            (endpoint: any) =>
                                endpoint[0].domainEndpoint === key
                        )[0].baseURL
                    };
                }
            }
        }

        for (let domain in setUrlMapping) {
            if (
                setUrlMapping[domain].endpoints?.some((endpoint) =>
                    url?.includes(endpoint)
                )
            ) {
                let endpointPath =
                    setUrlMapping[domain].baseURL.split("menfpt/")[1];
                // if (endpointPath.includes("/")) {
                //     endpointPath = endpointPath.replace("/", "");
                // }

                if (url?.startsWith(endpointPath)) {
                    url = url.replace(endpointPath, "");
                }
                setURL = `${setUrlMapping[domain].baseURL}${url}`;
            }
        }

        if (!setURL) {
            throw new Error(`No domain found for the provided URL: ${url}`);
        }
        return setURL;
    }
    @ExecutionContext()
    private context: ExecutionContext | undefined;

    getHeader(url?: string) {
        const uiTraceId = get(this.context, "context.uiTraceId", "");
        return {
            "Content-Type": "application/json",
            "ui-trace-id": uiTraceId,
            "bff-trace-id": !uiTraceId ? "" : uuidv4()
        };
    }

    createErrorPayload(error: unknown): ErrorPayload {
        return {
            httpStatus: get(error, "response.status", null),
            errorCode: get(error, "code", "ERROR_CODE_UNKNOWN"),
            name: get(error, "name", "ERROR_NAME_UNKNOWN"),
            message: get(error, "response.data.message", ""),
            url: get(error, "config.url", "REQUEST_URL_UNKNOWN")
        };
    }

    async get(url: string, payload?: { body: object }, options?: object) {
        const getHeader = this.getHeader(url);
        const requestURL = this.createRequestURL(url);
        const bffMethod = get(this.context, "info.fieldName");
        if ((this.context as any)?.context?.apiCallTracker) {
            (this.context as any).context.apiCallTracker.track(
                bffMethod,
                "GET",
                requestURL
            );
        }
        logApiCall({
            phase: "START",
            method: "GET",
            url: requestURL,
            payload: payload?.body
        });
        try {
            const response = await axios.request({
                method: "get",
                url: requestURL,
                data: payload?.body,
                headers: getHeader,
                ...options
            });
            logApiCall({
                phase: "END",
                method: "GET",
                url: requestURL,
                payload: payload?.body,
                response
            });
            return response;
        } catch (error) {
            logApiCall({
                phase: "ERROR",
                method: "GET",
                url: requestURL,
                payload: payload?.body,
                error
            });
            throw error;
        }
    }

    async post(url: string, payload?: { body: object }, options?: object) {
        const getHeader = this.getHeader(url);
        const requestURL = this.createRequestURL(url);
        const bffMethod = get(this.context, "info.fieldName");
        if ((this.context as any)?.context?.apiCallTracker) {
            (this.context as any).context.apiCallTracker.track(
                bffMethod,
                "POST",
                requestURL
            );
        }
        logApiCall({
            phase: "START",
            method: "POST",
            url: requestURL,
            payload: payload?.body
        });
        try {
            const response = await axios.request({
                method: "post",
                url: requestURL,
                headers: getHeader,
                data: payload?.body,
                ...options
            });
            logApiCall({
                phase: "END",
                method: "POST",
                url: requestURL,
                payload: payload?.body,
                response
            });
            return response;
        } catch (error) {
            logApiCall({
                phase: "ERROR",
                method: "POST",
                url: requestURL,
                payload: payload?.body,
                error
            });
            throw error;
        }
    }

    async patch(url: string, payload?: { body: object }, options?: object) {
        const getHeader = this.getHeader(url);
        const requestURL = this.createRequestURL(url);
        const bffMethod = get(this.context, "info.fieldName");
        if ((this.context as any)?.context?.apiCallTracker) {
            (this.context as any).context.apiCallTracker.track(
                bffMethod,
                "PATCH",
                requestURL
            );
        }
        logApiCall({
            phase: "START",
            method: "PATCH",
            url: requestURL,
            payload: payload?.body
        });
        try {
            const response = await axios.request({
                method: "patch",
                url: requestURL,
                headers: getHeader,
                data: payload?.body,
                ...options
            });
            logApiCall({
                phase: "END",
                method: "PATCH",
                url: requestURL,
                payload: payload?.body,
                response
            });
            return response;
        } catch (error) {
            logApiCall({
                phase: "ERROR",
                method: "PATCH",
                url: requestURL,
                payload: payload?.body,
                error
            });
            throw error;
        }
    }

    async put(url: string, payload?: { body: object }, options?: object) {
        const getHeader = this.getHeader(url);
        const requestURL = this.createRequestURL(url);
        const bffMethod = get(this.context, "info.fieldName");
        if ((this.context as any)?.context?.apiCallTracker) {
            (this.context as any).context.apiCallTracker.track(
                bffMethod,
                "PUT",
                requestURL
            );
        }
        logApiCall({
            phase: "START",
            method: "PUT",
            url: requestURL,
            payload: payload?.body
        });
        try {
            const response = await axios.request({
                method: "put",
                headers: getHeader,
                url: requestURL,
                data: payload?.body,
                ...options
            });
            logApiCall({
                phase: "END",
                method: "PUT",
                url: requestURL,
                payload: payload?.body,
                response
            });
            return response;
        } catch (error) {
            logApiCall({
                phase: "ERROR",
                method: "PUT",
                url: requestURL,
                payload: payload?.body,
                error
            });
            throw error;
        }
    }

    async delete(url: string, payload?: { body: object }, options?: object) {
        const getHeader = this.getHeader(url);
        const requestURL = this.createRequestURL(url);
        const bffMethod = get(this.context, "info.fieldName");
        if ((this.context as any)?.context?.apiCallTracker) {
            (this.context as any).context.apiCallTracker.track(
                bffMethod,
                "DELETE",
                requestURL
            );
        }
        logApiCall({
            phase: "START",
            method: "DELETE",
            url: requestURL,
            payload: payload?.body
        });
        try {
            const response = await axios.request({
                method: "delete",
                url: requestURL,
                headers: getHeader,
                data: payload?.body,
                ...options
            });
            logApiCall({
                phase: "END",
                method: "DELETE",
                url: requestURL,
                payload: payload?.body,
                response
            });
            return response;
        } catch (error) {
            logApiCall({
                phase: "ERROR",
                method: "DELETE",
                url: requestURL,
                payload: payload?.body,
                error
            });
            throw error;
        }
    }
}
