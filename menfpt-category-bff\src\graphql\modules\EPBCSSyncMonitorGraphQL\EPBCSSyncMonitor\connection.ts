import axios, { AxiosError } from "axios";
import qs from "qs";

const formatTimestamp = (
    timestamp: string | number | null,
    includeMinutesSeconds: boolean = true
): string => {
    if (!timestamp) {
        return "N/A";
    }
    const timestampNum =
        typeof timestamp === "string" ? parseInt(timestamp, 10) : timestamp;
    if (isNaN(timestampNum)) {
        return "Invalid Date";
    }
    const date = new Date(timestampNum);
    if (isNaN(date.getTime())) {
        return "Invalid Date";
    }
    const options: Intl.DateTimeFormatOptions = {
        timeZone: "America/Los_Angeles",
        month: "2-digit",
        day: "2-digit",
        year: "numeric",
        hour: "2-digit",
        hour12: true
    };

    if (includeMinutesSeconds) {
        options.minute = "2-digit";
        options.second = "2-digit";
    } else {
        options.minute = "2-digit";
    }
    const formattedDate = date.toLocaleString("en-US", options);
    return formattedDate;
};

const TENANT_ID = process.env.DATABRICKS_TENANT_ID;
const CLIENT_ID = process.env.DATABRICKS_CLIENT_ID;
const CLIENT_SECRET =process.env.DATABRICKS_CLIENT_SECRET;
const DATABRICKS_INSTANCE =process.env.DATABRICKS_INSTANCE;

const DEFAULT_JOB_NAME =process.env.DATABRICKS_JOB;
// console.log(`EPBCSSyncMonitor connection configured:`);

export const MONDAY_CRON = process.env.MONDAY_CRON;
export const THURSDAY_CRON = process.env.THURSDAY_CRON;
export const FRIDAY_CRON = process.env.FRIDAY_CRON;

const getAccessToken = async (): Promise<string> => {
    const tokenUrl = `https://login.microsoftonline.com/${TENANT_ID}/oauth2/v2.0/token`;
    const data = qs.stringify({
        grant_type: "client_credentials",
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        scope: "2ff814a6-3304-4ab8-85cb-cd0e6f879c1d/.default"
    });

    try {
        const response = await axios.post(tokenUrl, data, {
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            }
        });
        return response.data.access_token;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            console.error(
                "Error fetching access token:",
                (error as AxiosError).response?.data || error.message
            );
        } else {
            console.error("Error fetching access token:", error);
        }
        throw new Error("Failed to fetch access token");
    }
};

const listAllJobs = async (accessToken: string): Promise<any> => {
    try {
        const response = await axios.get(
            `${DATABRICKS_INSTANCE}/api/2.1/jobs/list`,
            {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            }
        );
        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            console.error(
                "Error listing all jobs:",
                (error as AxiosError).response?.data || error.message
            );
        } else {
            console.error("Error listing all jobs:", error);
        }
        throw new Error("Failed to list all jobs");
    }
};

const findJobIdByName = async (
    accessToken: string,
    jobName: string
): Promise<number> => {
    try {
        const jobsResponse = await listAllJobs(accessToken);

        if (!jobsResponse.jobs || jobsResponse.jobs.length === 0) {
            throw new Error("No jobs found in the workspace");
        }

        const job = jobsResponse.jobs.find(
            (job: any) => job.settings.name === jobName
        );

        if (!job) {
            throw new Error(`Job with name '${jobName}' not found`);
        }

        return job.job_id;
    } catch (error) {
        console.error("Error finding job by name:", error);
        throw error;
    }
};

const listJobRuns = async (
    accessToken: string,
    jobId: number,
    limit: number = 1
): Promise<any> => {
    try {
        const response = await axios.get(
            `${DATABRICKS_INSTANCE}/api/2.1/jobs/runs/list`,
            {
                params: {
                    job_id: jobId,
                    limit: limit,
                    completed_only: true
                },
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            }
        );

        if (response.data.runs) {
            // console.log(
            //     "Job runs:",
            //     JSON.stringify({ runs: response.data.runs }, null, 2)
            // );
        }

        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            console.error(
                "Error listing job runs:",
                (error as AxiosError).response?.data || error.message
            );
        } else {
            console.error("Error listing job runs:", error);
        }
        throw new Error("Failed to list job runs");
    }
};

const fetchJobRunsWithFormatting = async (
    accessToken: string,
    jobId: number,
    limit: number = 10
): Promise<any> => {
    try {
        const runsResponse = await listJobRuns(accessToken, jobId, limit);

        if (!runsResponse.runs || runsResponse.runs.length === 0) {
            return { runs: [] };
        }

        const formattedRuns = runsResponse.runs.map((run: any) => {
            const resultState =
                run.state?.result_state || run.result_state || null;
            const fullStartTime = formatTimestamp(run.start_time, true);
            const displayStartTime = formatTimestamp(run.start_time, false);
            const displayEndTime = formatTimestamp(run.end_time, false);

            return {
                start_time: displayStartTime,
                end_time: displayEndTime,
                _full_start_time: fullStartTime,
                result_state: resultState
            };
        });

        // console.log("Formatted runs:", JSON.stringify(formattedRuns, null, 2));
        return { runs: formattedRuns };
    } catch (error) {
        console.error("Error in fetchJobRunsWithFormatting:", error);
        throw error;
    }
};

const fetchRunStatus = async (
    accessToken: string,
    runId: string
): Promise<any> => {
    try {
        const response = await axios.get(
            `${DATABRICKS_INSTANCE}/api/2.1/jobs/runs/get`,
            {
                params: { run_id: runId },
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            }
        );
        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            console.error(
                "Error fetching run status:",
                (error as AxiosError).response?.data || error.message
            );
        } else {
            console.error("Error fetching run status:", error);
        }
        throw new Error("Failed to fetch run status");
    }
};

const fetchRunOutput = async (
    accessToken: string,
    runId: string
): Promise<{ notebook_output: any; result_data: any }> => {
    try {
        const response = await axios.get(
            `${DATABRICKS_INSTANCE}/api/2.1/jobs/runs/get-output`,
            {
                params: { run_id: runId },
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            }
        );

        let notebookOutput = response.data.notebook_output || null;
        if (notebookOutput !== null && typeof notebookOutput === "object") {
            notebookOutput = JSON.stringify(notebookOutput);
        }

        let resultData = null;
        try {
            if (notebookOutput) {
                const parsedOutput = JSON.parse(notebookOutput);

                if (parsedOutput.data) {
                    resultData = parsedOutput.data;
                } else if (parsedOutput.result) {
                    resultData = parsedOutput.result;
                } else {
                    resultData = parsedOutput;
                }
            }
        } catch (e) {
            console.log("Could not parse notebook output as JSON:", e);
            resultData = notebookOutput;
        }

        return {
            notebook_output: notebookOutput,
            result_data: resultData
        };
    } catch (error) {
        if (axios.isAxiosError(error)) {
            console.error(
                "Error fetching run output:",
                (error as AxiosError).response?.data || error.message
            );
        } else {
            console.error("Error fetching run output:", error);
        }
        throw new Error("Failed to fetch run output");
    }
};

const fetchJobData = async (
    jobIdOrName: number | string,
    includeStatus: boolean = false
): Promise<any> => {
    try {
        const accessToken = await getAccessToken();
        let jobId: number;

        if (typeof jobIdOrName === "string") {
            jobId = await findJobIdByName(accessToken, jobIdOrName);
        } else {
            jobId = jobIdOrName;
        }

        const runsResponse = await listJobRuns(accessToken, jobId, 1);

        if (!runsResponse.runs || runsResponse.runs.length === 0) {
            throw new Error(`No completed runs found for job ID ${jobId}`);
        }

        const latestRun = runsResponse.runs[0];
        const runId = latestRun.run_id;

        const jobOutput = await fetchRunOutput(accessToken, runId);

        const formattedStatus = includeStatus
            ? {
                  ...latestRun,
                  start_time: formatTimestamp(latestRun.start_time, false),
                  end_time: formatTimestamp(latestRun.end_time, false),
                  state: latestRun.state || null,
                  life_cycle_state: latestRun.life_cycle_state || null,
                  result_state: latestRun.result_state || null,
                  state_message: latestRun.state_message || null,
                  user_cancelled_or_timedout:
                      latestRun.user_cancelled_or_timedout || false
              }
            : null;

        return includeStatus
            ? { status: formattedStatus, output: jobOutput }
            : { output: jobOutput };
    } catch (error) {
        console.error("Error in fetchJobData:", error);
        throw error;
    }
};

export function formatCronHourToReadableTime(hour: number): string {
  const isPM = hour >= 12;
  const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;
  return `${displayHour}:00 ${isPM ? 'PM' : 'AM'}`;
}

export function parseCronHours(cronExpression: string): number[] {
  try {
    const parts = cronExpression.split(' ');
    if (parts.length !== 5) {
      console.error('Invalid cron expression format:', cronExpression);
      return [];
    }
    const hourPart = parts[1];
    if (hourPart.includes(',')) {
      return hourPart.split(',').map(h => parseInt(h, 10));
    }
    return [parseInt(hourPart, 10)];
  } catch (error) {
    console.error('Error parsing cron expression:', error);
    return [];
  }
}

export {
    getAccessToken,
    listAllJobs,
    findJobIdByName,
    listJobRuns,
    fetchJobRunsWithFormatting,
    fetchRunStatus,
    fetchRunOutput,
    fetchJobData,
    formatTimestamp,
    DEFAULT_JOB_NAME
};
