import { UrlGenerationService } from './urlGeneration.service';

// Mock the dependency modules
jest.mock('../../pharmaUpload/Connection', () => ({
    isConnectionAvailable: jest.fn()
}));

jest.mock('../utils/blobUtils', () => ({
    checkBlobExists: jest.fn(),
    getBlobClient: jest.fn()
}));

jest.mock('../utils/urlUtils', () => ({
    generateBffDownloadUrl: jest.fn(),
    createExpiryDate: jest.fn()
}));

describe('UrlGenerationService', () => {
    let service: UrlGenerationService;
    let mockIsConnectionAvailable: jest.Mock;
    let mockCheckBlobExists: jest.Mock;
    let mockGetBlobClient: jest.Mock;
    let mockGenerateBffDownloadUrl: jest.Mock;
    let mockCreateExpiryDate: jest.Mock;

    beforeEach(() => {
        // Get mocked functions
        const { isConnectionAvailable } = require('../../pharmaUpload/Connection');
        const { checkBlobExists, getBlobClient } = require('../utils/blobUtils');
        const { generateBffDownloadUrl, createExpiryDate } = require('../utils/urlUtils');

        mockIsConnectionAvailable = isConnectionAvailable as jest.Mock;
        mockCheckBlobExists = checkBlobExists as jest.Mock;
        mockGetBlobClient = getBlobClient as jest.Mock;
        mockGenerateBffDownloadUrl = generateBffDownloadUrl as jest.Mock;
        mockCreateExpiryDate = createExpiryDate as jest.Mock;

        service = new UrlGenerationService();

        // Reset mocks
        jest.clearAllMocks();
    });

    describe('generateDownloadUrl', () => {
        it('should throw error when connection is not available', async () => {
            mockIsConnectionAvailable.mockReturnValue(false);

            await expect(service.generateDownloadUrl('test.xlsx')).rejects.toThrow('Azure Blob Storage connection not available');
        });

        it('should throw error when file does not exist', async () => {
            mockIsConnectionAvailable.mockReturnValue(true);
            mockCheckBlobExists.mockResolvedValue(false);

            await expect(service.generateDownloadUrl('nonexistent.xlsx')).rejects.toThrow("File 'nonexistent.xlsx' not found in pharmacy folder");
        });

        it('should return SAS URL when generation succeeds', async () => {
            mockIsConnectionAvailable.mockReturnValue(true);
            mockCheckBlobExists.mockResolvedValue(true);

            const mockExpiryDate = new Date('2025-08-06T12:00:00Z');
            const mockSasUrl = 'https://storage.blob.core.windows.net/container/pharmacy/test.xlsx?sv=2023-01-03&se=2025-08-06T12%3A00%3A00Z&sr=b&sp=r&sig=abc123';

            mockCreateExpiryDate.mockReturnValue(mockExpiryDate);

            const mockBlobClient = {
                generateSasUrl: jest.fn().mockResolvedValue(mockSasUrl)
            };

            mockGetBlobClient.mockReturnValue(mockBlobClient);

            const result = await service.generateDownloadUrl('test.xlsx', 60);

            expect(result).toBe(mockSasUrl);
            expect(mockCreateExpiryDate).toHaveBeenCalledWith(60);
            expect(mockBlobClient.generateSasUrl).toHaveBeenCalledWith({
                permissions: {
                    read: true
                },
                expiresOn: mockExpiryDate
            });
        });

        it('should fallback to BFF URL when SAS generation fails', async () => {
            mockIsConnectionAvailable.mockReturnValue(true);
            mockCheckBlobExists.mockResolvedValue(true);

            const mockExpiryDate = new Date('2025-08-06T12:00:00Z');
            const mockBffUrl = 'https://api.example.com/menfpt-category-bff/api/pharmacy/download/test.xlsx';

            mockCreateExpiryDate.mockReturnValue(mockExpiryDate);
            mockGenerateBffDownloadUrl.mockReturnValue(mockBffUrl);

            const mockBlobClient = {
                generateSasUrl: jest.fn().mockRejectedValue(new Error('SAS generation failed'))
            };

            mockGetBlobClient.mockReturnValue(mockBlobClient);

            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

            const result = await service.generateDownloadUrl('test.xlsx', 60);

            expect(result).toBe(mockBffUrl);
            expect(mockGenerateBffDownloadUrl).toHaveBeenCalledWith('test.xlsx');
            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining("SAS URL generation failed for 'test.xlsx', returning BFF download URL instead:"),
                expect.any(Error)
            );

            consoleSpy.mockRestore();
        });

        it('should use default expiry minutes when not specified', async () => {
            mockIsConnectionAvailable.mockReturnValue(true);
            mockCheckBlobExists.mockResolvedValue(true);

            const mockExpiryDate = new Date('2025-08-06T12:00:00Z');
            const mockSasUrl = 'https://storage.blob.core.windows.net/container/pharmacy/test.xlsx?sv=2023-01-03&se=2025-08-06T12%3A00%3A00Z&sr=b&sp=r&sig=abc123';

            mockCreateExpiryDate.mockReturnValue(mockExpiryDate);

            const mockBlobClient = {
                generateSasUrl: jest.fn().mockResolvedValue(mockSasUrl)
            };

            mockGetBlobClient.mockReturnValue(mockBlobClient);

            const result = await service.generateDownloadUrl('test.xlsx');

            expect(result).toBe(mockSasUrl);
            expect(mockCreateExpiryDate).toHaveBeenCalledWith(60); // Default value
        });

        it('should handle general errors', async () => {
            mockIsConnectionAvailable.mockReturnValue(true);
            mockCheckBlobExists.mockRejectedValue(new Error('Connection error'));

            await expect(service.generateDownloadUrl('test.xlsx')).rejects.toThrow('Connection error');
        });
    });
});
