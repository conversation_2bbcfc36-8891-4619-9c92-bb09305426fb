import { ForecastChangeLogReq } from "src/graphql/__generated__/gql-ts-types";

import { ContextValue } from "src/graphql/context";

import { ForecastChangeLogProvider } from "../providers/forecastChangeLog.provider";
import { update } from "lodash";

export const ForecastChangeLogResolver = async (
    parent: any,
    args: { forecastChangeLogReqest: ForecastChangeLogReq },
    context: ContextValue
): Promise<any> => {
    const { forecastChangeLogReqest } = args;
    const forecastChangeLogService = context?.injector?.get(
        ForecastChangeLogProvider.provide
    );

    if (!forecastChangeLogReqest || Object.keys(forecastChangeLogReqest).length === 0) {
        throw new Error("Invalid request: Payload cannot be empty");
    }

    let forecastChangeLogData: any = [];

    try {
        forecastChangeLogData = await forecastChangeLogService?.getForecastChangeLog(
            forecastChangeLogReqest
        );
        if (!forecastChangeLogData?.auditHistory || !Array.isArray(forecastChangeLogData.auditHistory)) {
            console.error("Invalid or missing auditHistory data:", forecastChangeLogData);
            throw new Error("Invalid or missing auditHistory data");
        }
        const filteredData = selectLatestVersion(forecastChangeLogData.auditHistory);
        return formattedData(filteredData) || [];

    } catch (error) {
        console.error("Error in ForecastChangeLogResolver:", error);
        throw new Error("Invalid or missing auditHistory data");
    }
};
const selectLatestVersion = (auditHistory: any[]) => {
    if (!auditHistory || auditHistory.length === 0) {
        return [];
    }
    const weeksByCompositeKey: { [key: string]: any } = {};

    auditHistory.forEach((item) => {
        item.weeks.forEach((week: any) => {
            const fiscalWeekNbr = week.fiscalWeekNbr || null;
            const divisionId = week.divisionId || null;
            const keyAttributeName = week.keyAttributeName || null;
            const keyAttributeValue = week.keyAttributeValue || null;
            const username = week.after.updated_by ||null;
            const dateObj = new Date(item.date);
            dateObj.setMilliseconds(0);
            const normalizedDate = dateObj.toISOString();
            const compositeKey = `${fiscalWeekNbr}_${divisionId}_${keyAttributeName}_${keyAttributeValue}_${username}_${normalizedDate}`;
            if (
                !weeksByCompositeKey[compositeKey] ||
                new Date(normalizedDate).getTime() > new Date(weeksByCompositeKey[compositeKey].date).getTime()
            ) {
                weeksByCompositeKey[compositeKey] = { ...week, date: normalizedDate };
            }
        });
    });
    const result = Object.values(weeksByCompositeKey).map((week) => {
        return {
            date: week.date,
            weeks: [week],
        };
    });

    return result;
};
const formattedData = (input: any[]) => {
    const output: any[] = [];
    if (input && input?.length > 0) {
        input.forEach((item) => {
            const timestamp = item.updatedTs || item.date || "";
            if (!timestamp) {
                console.warn("No valid timestamp found for item:", item);
                return;
            }
            const updatedBy = item.weeks?.[0]?.after?.updated_by || item.weeks?.[0]?.before?.updated_by || null;
            const editedColumns = item.weeks?.[0]?.after?.edited_columns || item.weeks?.[0]?.before?.edited_columns || null;
            const existingEntry = output.find(
                (entry) => entry.updatedTimestamp === timestamp
            );

            const weekMetrics = (item.weeks || []).map((week: any) => {
                const adjustedFields = Object.entries(week.after || {})
                    .filter(([field]) => field !== "reason" && field !== "comment" && field !== "state" && field !== "version_nbr" && field !== "updated_by" && field !== "edited_columns")
                    .map(([field, value]) => ({
                        fieldName: field,
                        oldValue: week.before?.[field],
                        newValue: value,
                    }));

                return {
                    fiscalWeekNbrs: week.fiscalWeekNbr || null,
                    keyAttributeName: week.keyAttributeName || null,
                    keyAttributeValue: week.keyAttributeValue || null,
                    reason: week.after?.reason || null,
                    comment: week.after?.comment || null,
                    adjustedFields,
                };
            });

            if (existingEntry) {
                existingEntry.updatedMetrics.push(...weekMetrics);
            } else {
                output.push({
                    updatedTimestamp: timestamp,
                    updatedBy,
                    editedColumns,
                    updatedMetrics: weekMetrics,
                });
            }
        });
    }

    return output;
};