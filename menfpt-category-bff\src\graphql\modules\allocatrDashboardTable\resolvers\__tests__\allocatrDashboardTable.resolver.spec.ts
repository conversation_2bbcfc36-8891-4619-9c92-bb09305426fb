import "reflect-metadata";
import { AllocatrDashboardTableResolver } from "../allocatrDashboardTable.resolver";
import {
    AllocatrDataMappingServiceProvider,
    CalendarServiceProvider
} from "../../providers/allocatrDashboardTable.provider";

jest.mock("../dashboardTableHelper", () => ({
    ...jest.requireActual("../dashboardTableHelper"),
    rollUpDataFromWeek: jest.fn(() => new Map()),
    sumDataByAggregationRange: jest.fn(() => new Map())
}));

describe("AllocatrDashboardTableResolver", () => {
    let mockContext: any;
    let mockAllocatrDataMappingService: any;
    let mockCalendarService: any;

    beforeEach(() => {
        mockAllocatrDataMappingService = {
            getForecastDataForDashboard: jest.fn(),
            getProjectionDataForDashboard: jest.fn(),
            getActualDataForDashboard: jest.fn()
        };

        mockCalendarService = {
            getCalendarData: jest.fn()
        };

        mockContext = {
            injector: {
                get: jest.fn((provider) => {
                    if (
                        provider === AllocatrDataMappingServiceProvider.provide
                    ) {
                        return mockAllocatrDataMappingService;
                    }
                    if (provider === CalendarServiceProvider.provide) {
                        return mockCalendarService;
                    }
                    return null;
                })
            }
        };
    });

    it("should call services with correct payloads and return aggregated data", async () => {
        const mockGqlReq = {
            quarterNbr: 202401,
            deptId: ["D1"],
            latestReleaseWeek: {
                value: "2024-01-01"
            },
            periodNumbers: [1, 2, 3],
            weekNumbers: [1, 2, 3, 4, 5],
            currentFiscalYearNbr: 2024
        };
        const mockCalendarData = [
            { fiscalWeekNumber: 1, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 2, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 3, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 4, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 5, fiscalPeriodNumber: 2 }
        ];

        mockCalendarService.getCalendarData.mockResolvedValue(mockCalendarData);
        mockAllocatrDataMappingService.getForecastDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getProjectionDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getActualDataForDashboard.mockResolvedValue(
            []
        );

        await AllocatrDashboardTableResolver(
            {},
            { allocatrDashboardReq: mockGqlReq },
            mockContext
        );

        expect(
            mockAllocatrDataMappingService.getForecastDataForDashboard
        ).toHaveBeenCalled();
        expect(
            mockAllocatrDataMappingService.getProjectionDataForDashboard
        ).toHaveBeenCalled();
        expect(
            mockAllocatrDataMappingService.getActualDataForDashboard
        ).toHaveBeenCalled();
    });
    it("should handle empty responses from services gracefully", async () => {
        const mockGqlReq = {
            quarterNbr: 202401,
            deptId: ["D1"],
            latestReleaseWeek: {
                value: "2024-01-01"
            },
            periodNumbers: [1, 2, 3],
            weekNumbers: [1, 2, 3, 4, 5],
            currentFiscalYearNbr: 2024
        };
        const mockCalendarData = [
            { fiscalWeekNumber: 1, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 2, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 3, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 4, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 5, fiscalPeriodNumber: 2 }
        ];

        mockCalendarService.getCalendarData.mockResolvedValue(mockCalendarData);
        mockAllocatrDataMappingService.getForecastDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getProjectionDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getActualDataForDashboard.mockResolvedValue(
            []
        );

        const result = await AllocatrDashboardTableResolver(
            {},
            { allocatrDashboardReq: mockGqlReq },
            mockContext
        );
        expect(result).toEqual({
            allocatrDashboardTableData: []
        });
    });

    it("should handle multiple departments and include 'Total' department", async () => {
        mockContext.injector.get = jest.fn((provider) => {
            if (provider === AllocatrDataMappingServiceProvider.provide) {
                return mockAllocatrDataMappingService;
            }
            if (provider === CalendarServiceProvider.provide) {
                return mockCalendarService;
            }
            return null;
        });
        const mockGqlReq = {
            quarterNbr: 202401,
            deptIds: ["D1", "D2"],
            latestReleaseWeek: { value: "2024-01-01" },
            periodNumbers: [1, 2],
            weekNumbers: [1, 2],
            currentFiscalYearNbr: 2024
        };
        const mockCalendarData = [
            { fiscalWeekNumber: 1, fiscalPeriodNumber: 1 },
            { fiscalWeekNumber: 2, fiscalPeriodNumber: 1 }
        ];
        mockCalendarService.getCalendarData.mockResolvedValue(mockCalendarData);
        mockAllocatrDataMappingService.getForecastDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getProjectionDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getActualDataForDashboard.mockResolvedValue(
            []
        );
        const result = await AllocatrDashboardTableResolver(
            {},
            { allocatrDashboardReq: mockGqlReq },
            mockContext
        );
        expect(result.allocatrDashboardTableData.length).toBe(3); // Total + 2 depts
    });

    it("should handle no departments gracefully", async () => {
        const mockGqlReq = {
            quarterNbr: 202401,
            deptIds: [],
            latestReleaseWeek: { value: "2024-01-01" },
            periodNumbers: [1],
            weekNumbers: [1],
            currentFiscalYearNbr: 2024
        };
        mockCalendarService.getCalendarData.mockResolvedValue([]);
        mockAllocatrDataMappingService.getForecastDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getProjectionDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getActualDataForDashboard.mockResolvedValue(
            []
        );
        const result = await AllocatrDashboardTableResolver(
            {},
            { allocatrDashboardReq: mockGqlReq },
            mockContext
        );
        expect(result.allocatrDashboardTableData.length).toBe(0);
    });

    it("should handle calendarService.getCalendarData returning undefined", async () => {
        const mockGqlReq = {
            quarterNbr: 202401,
            deptIds: ["D1"],
            latestReleaseWeek: { value: "2024-01-01" },
            periodNumbers: [1],
            weekNumbers: [1],
            currentFiscalYearNbr: 2024
        };
        mockCalendarService.getCalendarData.mockResolvedValue(undefined);
        mockAllocatrDataMappingService.getForecastDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getProjectionDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getActualDataForDashboard.mockResolvedValue(
            []
        );
        const result = await AllocatrDashboardTableResolver(
            {},
            { allocatrDashboardReq: mockGqlReq },
            mockContext
        );
        expect(result.allocatrDashboardTableData).toBeDefined();
    });

    it("should handle service errors gracefully", async () => {
        const mockGqlReq = {
            quarterNbr: 202401,
            deptIds: ["D1"],
            latestReleaseWeek: { value: "2024-01-01" },
            periodNumbers: [1],
            weekNumbers: [1],
            currentFiscalYearNbr: 2024
        };
        mockCalendarService.getCalendarData.mockRejectedValue(
            new Error("Service error")
        );
        mockAllocatrDataMappingService.getForecastDataForDashboard.mockRejectedValue(
            new Error("Service error")
        );
        mockAllocatrDataMappingService.getProjectionDataForDashboard.mockRejectedValue(
            new Error("Service error")
        );
        mockAllocatrDataMappingService.getActualDataForDashboard.mockRejectedValue(
            new Error("Service error")
        );
        await expect(
            AllocatrDashboardTableResolver(
                {},
                { allocatrDashboardReq: mockGqlReq },
                mockContext
            )
        ).rejects.toThrow("Service error");
    });

    it("should handle transformWeek with various formats", async () => {
        const mockGqlReq = {
            quarterNbr: 202401,
            deptIds: ["D1"],
            latestReleaseWeek: { value: "01/15/2024" },
            periodNumbers: [1],
            weekNumbers: [1],
            currentFiscalYearNbr: 2024
        };
        mockCalendarService.getCalendarData.mockResolvedValue([
            { fiscalWeekNumber: 1, fiscalPeriodNumber: 1 }
        ]);
        mockAllocatrDataMappingService.getForecastDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getProjectionDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getActualDataForDashboard.mockResolvedValue(
            []
        );
        await AllocatrDashboardTableResolver(
            {},
            { allocatrDashboardReq: mockGqlReq },
            mockContext
        );
        // No assertion needed, just ensure no error
    });

    it("should sort period numbers in ascending order", async () => {
        const mockGqlReq = {
            quarterNbr: 202401,
            deptIds: ["D1"],
            latestReleaseWeek: { value: "2024-01-01" },
            periodNumbers: [7, 5, 6], // Unsorted order
            weekNumbers: [1, 2, 3, 4, 5],
            currentFiscalYearNbr: 2024
        };
        const mockCalendarData = [
            { fiscalWeekNumber: 1, fiscalPeriodNumber: 5 },
            { fiscalWeekNumber: 2, fiscalPeriodNumber: 5 },
            { fiscalWeekNumber: 3, fiscalPeriodNumber: 6 },
            { fiscalWeekNumber: 4, fiscalPeriodNumber: 6 },
            { fiscalWeekNumber: 5, fiscalPeriodNumber: 7 }
        ];

        mockCalendarService.getCalendarData.mockResolvedValue(mockCalendarData);
        mockAllocatrDataMappingService.getForecastDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getProjectionDataForDashboard.mockResolvedValue(
            []
        );
        mockAllocatrDataMappingService.getActualDataForDashboard.mockResolvedValue(
            []
        );

        const result = await AllocatrDashboardTableResolver(
            {},
            { allocatrDashboardReq: mockGqlReq },
            mockContext
        );

        // Verify that the periods are sorted in ascending order
        const departmentData = result.allocatrDashboardTableData[0];
        if (departmentData && departmentData.periods) {
            const periodNumbers = departmentData.periods.map((period: any) => period.periodNumber);
            expect(periodNumbers).toEqual([5, 6, 7]); // Should be sorted in ascending order
        }
    });
});
