import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";
import { createModule } from "graphql-modules";
import { AllocatrDataMappingServiceProvider, CalendarServiceProvider } from "./providers/allocatrDashboardTable.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));
const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const AllocatrDashboardTableModule = createModule({
    id: "AllocatrDashboardTable",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [AllocatrDataMappingServiceProvider, CalendarServiceProvider]
});

export default AllocatrDashboardTableModule;
