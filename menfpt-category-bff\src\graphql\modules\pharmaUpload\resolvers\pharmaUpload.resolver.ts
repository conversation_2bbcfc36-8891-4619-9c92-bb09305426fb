import axios from "axios";
import { pharmacyContainerClient } from "../../pharmaUpload/Connection";
import { PharmaUploadService } from "../../pharmaUpload/services/pharmaUpload.service";
const pharmaUploadService = new PharmaUploadService();

export const PharmaUploadResolver = {
    getUploadedDocuments: async (_: any, args: any) => {
        const { user, fiscalWeekNbr, fetchAll } = args.getUploadedDocuments || {};
        return pharmaUploadService.getUploadedDocuments(user, fiscalWeekNbr, fetchAll);
    },
};
