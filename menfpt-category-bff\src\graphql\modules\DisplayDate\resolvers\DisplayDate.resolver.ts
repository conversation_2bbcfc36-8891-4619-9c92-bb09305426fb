import { DisplayDateReq } from "src/graphql/__generated__/gql-ts-types";
import { ContextValue } from "../../../context";
import { DisplayDateProvider } from "../providers/DisplayDate.provider";
import { LOCKOUT_CERTIFY_CUTTOFF } from "../../../Util";
import { utcToZonedTime, format } from 'date-fns-tz';



const transformWeek = (week: string | number) => {
    if (typeof week === "string" && /^\d{2}\/\d{2}\/\d{4}$/.test(week)) {
        const [month, day, year] = week.split("/");
        const PSTdate = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}` + LOCKOUT_CERTIFY_CUTTOFF;
        return PSTdate;
    }
    const pacificTime = utcToZonedTime(new Date(week), 'America/Los_Angeles');
    const formattedTime = format(pacificTime, 'yyyy-MM-dd HH:mm:ss zzz', { timeZone: 'America/Los_Angeles' });
    return formattedTime;
}

export const DisplayDateResolver = async (
    parent: any,
    args: { displayDateReq: any },
    context: ContextValue
): Promise<any> => {
    const DisplayDateService = context?.injector?.get(
        DisplayDateProvider.provide
    );
    if (!DisplayDateService) {
        throw new Error(
            "DisplayDateService is not available in the context injector."
        );
    }
    let response: any = [];
    const req = args.displayDateReq || {};
    if (Array.isArray(req.qtrNumbersArr) && req.qtrNumbersArr.length > 0) {
        // Make multiple API calls in parallel for each quarter number
        const results = await Promise.all(
            req.qtrNumbersArr.map((quarterNumber: number) => {
                // Pass the rest of the req, but override fiscalQuarterNumber
                return DisplayDateService.getCalendarData({
                    ...req,
                    fiscalQuarterNumber: quarterNumber
                });
            })
        );
        // Flatten the results (each result is an array)
        response = results.flat().map((data: any) => {
            console.log("data", data);
            return {
                ...data,
                fiscalPeriodLockoutDate: transformWeek(data.fiscalPeriodLockoutDate),
                fiscalPeriodCertificationDate: transformWeek(data.fiscalPeriodCertificationDate)
            }
        })
    } else {
        const data = await DisplayDateService?.getCalendarData(req);
        if (data && data?.length > 0) {
            response = [data[0]];
        }
    }
    return { calendarDetails: response || [] };
};
