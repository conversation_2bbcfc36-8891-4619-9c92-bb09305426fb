input TimeRangeInYearReq {
    fiscalYearNumber: [Int]
    level: String
}

type RangeData {
    fiscalYearNumber: Int
    fiscalQuarterNumber: Int
    fiscalQuarterStartDate: String
    fiscalQuarterEndDate: String
    fiscalPeriodNumber: Int
    fiscalPeriodStartDate: String
    fiscalPeriodEndDate: String
    fiscalWeekNumber: Int
    fiscalWeekStartDate: String
    fiscalWeekEndDate: String
    createTimestamp: String
    updateTimestamp: String
}

type Query {
    getAllTimeRangeInYear(
        timeRangeInYearReq: TimeRangeInYearReq
    ): [RangeData]
}
