import "reflect-metadata";
import { FileUploadPharmaService } from "../fileUploadPharma.service";
import { BaseAPIService } from "../../../../shared/classes/BaseAPI.service";
import { GraphQLError } from "graphql";
import { MSP_DATA_POST_FAILURE } from "../../../../shared/constants/errorCodes";
import { pharmacyContainerClient } from "../../../pharmaUpload/Connection";

// Mock the dependencies
jest.mock("../../../../shared/classes/BaseAPI.service");

// Comprehensive mock for pharmaUpload/Connection to prevent module-level execution
jest.mock("../../../pharmaUpload/Connection", () => {
    return {
        pharmacyContainerClient: {
            getBlobClient: jest.fn(),
            exists: jest.fn().mockResolvedValue(true),
            listBlobsFlat: jest.fn().mockReturnValue([])
        },
        checkConnection: jest.fn().mockResolvedValue(true),
        listPharmacyBlobs: jest.fn().mockResolvedValue([])
    };
});

describe("FileUploadPharmaService", () => {
    let service: FileUploadPharmaService;
    let mockPost: jest.Mock;
    let mockCreateErrorPayload: jest.Mock;

    beforeEach(() => {
        // Reset all mocks
        jest.clearAllMocks();

        // Setup service instance
        service = new FileUploadPharmaService();

        // Mock BaseAPIService methods
        mockPost = jest.fn();
        mockCreateErrorPayload = jest.fn().mockReturnValue({
            httpStatus: 500,
            errorCode: "ERROR_CODE",
            name: "Error",
            message: "Test error",
            url: "test-url"
        });

        (service as any).post = mockPost;
        (service as any).createErrorPayload = mockCreateErrorPayload;

        // Setup default successful blob client mock
        const mockBlobClient = {
            url: "https://mock-storage.blob.core.windows.net/menfpt/pharmacy/test-file.xlsx",
            getBlockBlobClient: jest.fn(() => ({
                upload: jest.fn().mockResolvedValue({})
            }))
        };
        (pharmacyContainerClient.getBlobClient as jest.Mock).mockReturnValue(mockBlobClient);
    });

    describe("fileUploadToBlob", () => {
        const mockRequest = {
            fileName: "test-file.xlsx",
            uploadDataPersistenceEnabled: true,
            user: "test-user",
            fileContent: new Array(2000).fill(80), // Large enough buffer with proper Excel header
            fiscalWeekNbr: 52
        };

        // Set proper Excel header for validation
        beforeEach(() => {
            if (Array.isArray(mockRequest.fileContent)) {
                mockRequest.fileContent[0] = 0x50; // P
                mockRequest.fileContent[1] = 0x4B; // K
            }
        });

        it("should successfully upload file and process forecast", async () => {
            // Mock successful API response
            mockPost.mockResolvedValue({
                status: 200,
                data: { message: "File processed successfully" }
            });

            const result = await service.fileUploadToBlob(mockRequest);

            expect(result).toEqual({
                success: true
            });

            // Verify blob upload was called
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith("pharmacy/test-file.xlsx");

            // Verify API call was made
            expect(mockPost).toHaveBeenCalledWith("forecast/file/process", {
                body: {
                    fileName: "test-file.xlsx",
                    uploadDataPersistenceEnabled: true,
                    user: "test-user",
                    fiscalWeekNbr: 52
                }
            });
        });

        it("should handle fileBuffer instead of fileContent", async () => {
            // Create a proper Excel file buffer with correct header
            const excelBuffer = Buffer.alloc(2000);
            excelBuffer[0] = 0x50; // P
            excelBuffer[1] = 0x4B; // K

            const requestWithBuffer = {
                ...mockRequest,
                fileContent: undefined,
                fileBuffer: excelBuffer
            };

            mockPost.mockResolvedValue({
                status: 200,
                data: { message: "File processed successfully" }
            });

            const result = await service.fileUploadToBlob(requestWithBuffer);

            expect(result.success).toBe(true);
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith("pharmacy/test-file.xlsx");
        });

        it("should return error response when blob upload fails", async () => {
            // Mock the blob client to throw an error during upload
            const mockBlobClient = {
                url: "https://mock-blob-url.com/pharmacy/test-file.xlsx",
                getBlockBlobClient: jest.fn(() => ({
                    upload: jest.fn().mockRejectedValue(new Error("Blob upload failed"))
                }))
            };
            (pharmacyContainerClient.getBlobClient as jest.Mock).mockReturnValue(mockBlobClient);

            const result = await service.fileUploadToBlob(mockRequest);

            expect(result).toEqual({
                success: false,
                error: "Failed to upload file to blob storage: Error: Blob upload failed",
                details: expect.any(Error)
            });
        });

        it("should return error response when API call fails with custom error", async () => {
            // Mock API failure
            mockPost.mockRejectedValue({
                message: "Validation failed"
            });

            const result = await service.fileUploadToBlob(mockRequest);

            expect(result).toEqual({
                success: false,
                error: "Error while processing file for forecast",
                details: expect.any(GraphQLError)
            });
        });
        it("should return error response when API call fails with GraphQL error", async () => {
            // Mock a GraphQL error
            const graphqlError = new GraphQLError("Service unavailable");
            mockPost.mockRejectedValue(graphqlError);

            const result = await service.fileUploadToBlob(mockRequest);

            expect(result).toEqual({
                success: false,
                error: "Error while processing file for forecast",
                details: expect.any(GraphQLError)
            });
        });


        it("should handle missing file buffer", async () => {
            const requestWithoutFile = {
                ...mockRequest,
                fileContent: undefined,
                fileBuffer: undefined
            };

            const result = await service.fileUploadToBlob(requestWithoutFile);

            expect(result.success).toBe(false);
            expect(result.error).toContain("No valid file buffer provided");
        });

        it("should handle invalid file content array", async () => {
            const requestWithInvalidContent = {
                ...mockRequest,
                fileContent: "invalid-content-type" as any
            };

            const result = await service.fileUploadToBlob(requestWithInvalidContent);

            expect(result.success).toBe(false);
        });

        it("should handle large file content", async () => {
            // Create a large file buffer (5MB)
            const largeBuffer = new Array(5 * 1024 * 1024).fill(80);
            largeBuffer[0] = 0x50; // P
            largeBuffer[1] = 0x4B; // K

            const requestWithLargeFile = {
                ...mockRequest,
                fileContent: largeBuffer
            };

            mockPost.mockResolvedValue({
                status: 200,
                data: { message: "Large file processed successfully" }
            });

            const result = await service.fileUploadToBlob(requestWithLargeFile);

            expect(result.success).toBe(true);
        });

        it("should handle special characters in filename", async () => {
            const specialCharsRequest = {
                ...mockRequest,
                fileName: "тест-файл-pharmacy_ñáme.xlsx"
            };

            mockPost.mockResolvedValue({
                status: 200,
                data: { message: "File with special characters processed" }
            });

            const result = await service.fileUploadToBlob(specialCharsRequest);

            expect(result.success).toBe(true);
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith("pharmacy/тест-файл-pharmacy_ñáme.xlsx");
        });

        it("should handle different fiscal week numbers", async () => {
            const testCases = [1, 26, 52, 53];

            for (const fiscalWeek of testCases) {
                jest.clearAllMocks();

                const requestWithFiscalWeek = {
                    ...mockRequest,
                    fiscalWeekNbr: fiscalWeek
                };

                mockPost.mockResolvedValue({
                    status: 200,
                    data: { message: `Week ${fiscalWeek} processed` }
                });

                const result = await service.fileUploadToBlob(requestWithFiscalWeek);

                expect(result.success).toBe(true);
                expect(mockPost).toHaveBeenCalledWith("forecast/file/process", {
                    body: expect.objectContaining({
                        fiscalWeekNbr: fiscalWeek
                    })
                });
            }
        });

        it("should handle uploadDataPersistenceEnabled false", async () => {
            const requestWithPersistenceDisabled = {
                ...mockRequest,
                uploadDataPersistenceEnabled: false
            };

            mockPost.mockResolvedValue({
                status: 200,
                data: { message: "File processed without persistence" }
            });

            const result = await service.fileUploadToBlob(requestWithPersistenceDisabled);

            expect(result.success).toBe(true);
            expect(mockPost).toHaveBeenCalledWith("forecast/file/process", {
                body: expect.objectContaining({
                    uploadDataPersistenceEnabled: false
                })
            });
        });

        it("should handle missing optional fields", async () => {
            const minimalRequest = {
                fileName: "minimal.xlsx",
                uploadDataPersistenceEnabled: true,
                user: "test-user",
                fileContent: new Array(2000).fill(80),
                fiscalWeekNbr: 1
            };
            minimalRequest.fileContent[0] = 0x50; // P
            minimalRequest.fileContent[1] = 0x4B; // K

            mockPost.mockResolvedValue({
                status: 200,
                data: { message: "Minimal file processed" }
            });

            const result = await service.fileUploadToBlob(minimalRequest);

            expect(result.success).toBe(true);
        });
    });

    describe("uploadFileToBlob (private method)", () => {
        it("should throw error when no file buffer provided", async () => {
            try {
                await (service as any).uploadFileToBlob("test.xlsx");
                fail("Expected error to be thrown");
            } catch (error) {
                expect(error).toBeInstanceOf(Error);
                expect((error as Error).message).toBe("Failed to upload file to blob storage: Error: No file buffer provided for upload");
            }
        });

        it("should successfully upload file to blob storage", async () => {
            const fileName = "test.xlsx";
            const fileBuffer = Buffer.alloc(2000);
            fileBuffer[0] = 0x50; // P
            fileBuffer[1] = 0x4B; // K

            // Setup successful mock for this specific test
            const mockBlobClient = {
                url: "https://mock-storage.blob.core.windows.net/menfpt/pharmacy/test-file.xlsx",
                getBlockBlobClient: jest.fn(() => ({
                    upload: jest.fn().mockResolvedValue({})
                }))
            };
            (pharmacyContainerClient.getBlobClient as jest.Mock).mockReturnValue(mockBlobClient);

            const result = await (service as any).uploadFileToBlob(fileName, fileBuffer);

            expect(result).toBe("https://mock-storage.blob.core.windows.net/menfpt/pharmacy/test-file.xlsx");
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith("pharmacy/test.xlsx");
        });

        it("should handle blob upload error", async () => {
            const fileName = "test.xlsx";
            const fileBuffer = Buffer.alloc(2000);
            const uploadError = new Error("Network error");

            // Mock the blob client to throw an error during upload
            const mockBlobClient = {
                url: "https://mock-blob-url.com/pharmacy/test-file.xlsx",
                getBlockBlobClient: jest.fn(() => ({
                    upload: jest.fn().mockRejectedValue(uploadError)
                }))
            };
            (pharmacyContainerClient.getBlobClient as jest.Mock).mockReturnValue(mockBlobClient);

            try {
                await (service as any).uploadFileToBlob(fileName, fileBuffer);
                fail("Expected error to be thrown");
            } catch (error) {
                expect(error).toBeInstanceOf(Error);
                expect((error as Error).message).toBe("Failed to upload file to blob storage: Error: Network error");
            }
        });
    });

    describe("processFileForForecast (private method)", () => {
        const mockPayload = {
            fileName: "test.xlsx",
            uploadDataPersistenceEnabled: true,
            user: "test-user",
            fiscalWeekNbr: 52
        };

        it("should successfully process file for forecast", async () => {
            const mockResponse = {
                status: 200,
                data: { message: "Success" }
            };
            mockPost.mockResolvedValue(mockResponse);

            const result = await (service as any).processFileForForecast(mockPayload);

            expect(result).toEqual({
                data: { message: "Success" },
                status: 200
            });

            expect(mockPost).toHaveBeenCalledWith("forecast/file/process", {
                body: {
                    fileName: "test.xlsx",
                    uploadDataPersistenceEnabled: true,
                    user: "test-user",
                    fiscalWeekNbr: 52
                }
            });
        });

        it("should throw GraphQLError on API failure", async () => {
            const apiError = {
                response: {
                    status: 400,
                    data: { message: "Bad request" }
                }
            };
            mockPost.mockRejectedValue(apiError);

            try {
                await (service as any).processFileForForecast(mockPayload);
                fail("Expected GraphQLError to be thrown");
            } catch (error) {
                expect(error).toBeInstanceOf(GraphQLError);
                expect((error as GraphQLError).message).toBe("Error while processing file for forecast");
                expect((error as GraphQLError).extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    httpStatus: 500,
                    errorCode: "ERROR_CODE",
                    name: "Error",
                    message: "Test error",
                    url: "test-url"
                });
            }
        });

        it("should handle API error without response data", async () => {
            const apiError = {
                message: "Network timeout"
            };
            mockPost.mockRejectedValue(apiError);

            try {
                await (service as any).processFileForForecast(mockPayload);
                fail("Expected GraphQLError to be thrown");
            } catch (error) {
                expect(error).toBeInstanceOf(GraphQLError);
                expect((error as GraphQLError).message).toBe("Error while processing file for forecast");
            }
        });
    });

    describe("getMimeTypeFromFileName (private method)", () => {
        it("should return default MIME type for PDF files", () => {
            const result = (service as any).getMimeTypeFromFileName("document.pdf");
            expect(result).toBe("application/octet-stream");
        });

        it("should return correct MIME type for Excel files", () => {
            const xlsxResult = (service as any).getMimeTypeFromFileName("spreadsheet.xlsx");
            const xlsResult = (service as any).getMimeTypeFromFileName("spreadsheet.xls");

            expect(xlsxResult).toBe("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            expect(xlsResult).toBe("application/vnd.ms-excel");
        });

        it("should return default MIME type for CSV files", () => {
            const result = (service as any).getMimeTypeFromFileName("data.csv");
            expect(result).toBe("application/octet-stream");
        });

        it("should return default MIME type for text files", () => {
            const result = (service as any).getMimeTypeFromFileName("readme.txt");
            expect(result).toBe("application/octet-stream");
        });

        it("should return default MIME type for unknown extensions", () => {
            const result = (service as any).getMimeTypeFromFileName("unknown.xyz");
            expect(result).toBe("application/octet-stream");
        });

        it("should handle files without extensions", () => {
            const result = (service as any).getMimeTypeFromFileName("filename");
            expect(result).toBe("application/octet-stream");
        });

        it("should handle case insensitive extensions", () => {
            const result = (service as any).getMimeTypeFromFileName("document.PDF");
            expect(result).toBe("application/octet-stream");
        });
    });
});
