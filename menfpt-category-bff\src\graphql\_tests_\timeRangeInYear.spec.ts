import "reflect-metadata";
import { testkit, gql, MockedApplication } from "graphql-modules";
import { gqlApp } from "../modules/index";
import { CalendarService } from "../modules/services/calendar.service";
import TimeRangeInYearModule from "../modules/TimeRangeInYear";
import { TimeRangeInYearProvider } from "../modules/TimeRangeInYear/providers/TimeRangeInYear.provider";

const getQuartersInYearResponse = [
    {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 202401,
        fiscalQuarterStartDate: "02/25/2024",
        fiscalQuarterEndDate: "06/15/2024"
    },
    {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 202402,
        fiscalQuarterStartDate: "06/16/2024",
        fiscalQuarterEndDate: "09/14/2024"
    },
    {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 202403,
        fiscalQuarterStartDate: "09/15/2024",
        fiscalQuarterEndDate: "12/14/2024"
    },
    {
        fiscalYearNumber: 2024,
        fiscalQuarterNumber: 202404,
        fiscalQuarterStartDate: "12/15/2024",
        fiscalQuarterEndDate: "02/22/2025"
    }
];

describe("getAllQuartersInYr", () => {
    let service: CalendarService;
    beforeEach(() => {
        service = new CalendarService();
    });

    describe("Testing positive scenarios for getAllQuartersInYr", () => {
        let app: MockedApplication;
        beforeAll(() => {
            app = testkit.mockApplication(gqlApp).replaceModule(
                testkit.mockModule(TimeRangeInYearModule, {
                    providers: [
                        {
                            provide: TimeRangeInYearProvider.provide,
                            useValue: {
                                getCalendarData() {
                                    return getQuartersInYearResponse;
                                }
                            }
                        }
                    ]
                })
            );
        });

        it("Should return the correct response for getAllQuartersInYr query", async () => {
            const result = await testkit.execute(app, {
                document: gql`
                    {
                        getAllTimeRangeInYear(timeRangeInYearReq: { fiscalYearNumber: [2024], level: "Quarter" }) {
                            fiscalYearNumber
                            fiscalQuarterNumber
                            fiscalQuarterStartDate
                            fiscalQuarterEndDate
                        }
                    }
                `,
                contextValue: {},
            });

            expect(result?.data?.getAllTimeRangeInYear).toEqual(getQuartersInYearResponse);
        });
    });

    describe("Testing error scenarios for getAllQuartersInYr", () => {
        let app: MockedApplication;
        beforeAll(() => {
            app = testkit.mockApplication(gqlApp).replaceModule(
                testkit.mockModule(TimeRangeInYearModule, {
                    providers: [
                        {
                            provide: TimeRangeInYearProvider.provide,
                            useValue: {
                                getCalendarData() {
                                    return null;
                                }
                            }
                        }
                    ]
                })
            );
        });

        it("Should return an empty array when no data is available", async () => {
            const result = await testkit.execute(app, {
                document: gql`
                    {
                        getAllTimeRangeInYear(timeRangeInYearReq: { fiscalYearNumber: [2024], level: "Quarter" }) {
                            fiscalYearNumber
                            fiscalQuarterNumber
                            fiscalQuarterStartDate
                            fiscalQuarterEndDate
                        }
                    }
                `,
                contextValue: {}
            });

            expect(result?.data?.getAllTimeRangeInYear).toEqual([]);
        });
    });
});
