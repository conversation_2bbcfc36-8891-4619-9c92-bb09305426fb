import "reflect-metadata";
import { RoleMappingService } from "../roleMapping.service";
import { GraphQLError } from "graphql";
import { MSP_DATA_POST_FAILURE } from "../../../shared/constants/errorCodes";
jest.mock("axios");
jest.mock("../../../shared/classes/BaseAPI.service");
const WorkSheetFilterAPIResponse = {
    message: "Records found",
    data: {
        smicData: [
            {
                division_id: "27",
                division_name: "Seattle",

                dept_id: "311",

                dept_nm: "GM/HBC",

                retail_section_cd: "312",
                retail_section_nm: "FAMILY CARE GMHBC",
                smic_group_cd: 52,

                smic_group_desc: "HAIR CARE",
                smic_category_cd: 55,

                smic_category_desc: "HAIR CARE MULTI-CULTURAL",
                smic_category_id: 5255,

                desk_id: "27-25",
                desk_nm: "27-<PERSON><PERSON><PERSON><PERSON>W<PERSON>"
            },
            {
                division_id: "28",
                division_name: "Portland",
                dept_id: "312",
                dept_nm: "GROCERY",
                smic_group_cd: 53,
                smic_group_desc: "SOFT DRINKS",
                smic_category_cd: 56,
                smic_category_desc: "CARBONATED DRINKS",
                smic_category_id: 5356,
                retail_section_cd: "313",
                retail_section_nm: "BEVERAGES",
                desk_id: "28-26",
                desk_nm: "28-JOHN DOE"
            },
            {
                division_id: "29",
                division_name: "San Francisco",
                dept_id: "313",
                dept_nm: "PRODUCE",
                smic_group_cd: 54,
                smic_group_desc: "CITRUS",
                smic_category_cd: 57,
                smic_category_desc: "ORANGES",
                smic_category_id: 5457,
                retail_section_cd: "314",
                retail_section_nm: "FRUITS",
                desk_id: "29-27",
                desk_nm: "29-JANE SMITH"
            }
        ],
        user_nm: "BONNIE KWOK",
        user_id: "bkwok01",

        user_email: "<EMAIL>",

        user_role: "ASM",
        created_ts: "2025-03-29T07:36:56.307Z",
        update_ts: "2025-03-29T07:36:56.307Z",
        created_by: "NFPT System",
        updated_by: "NFPT System"
    },
    timestamp: "2025-02-05T08:08:51.618Z"
};
describe("RoleMappingService", () => {
    let service: RoleMappingService;
    beforeEach(() => {
        service = new RoleMappingService();
    });
    describe("To get WorkSheet Filter", () => {
        it("should return WorkSheet Filter response", async () => {
            const response = {
                data: WorkSheetFilterAPIResponse
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getWorkSheetFilter({
                userId: "bkwok01"
            });
            expect(result).toEqual(WorkSheetFilterAPIResponse.data);
            expect(service.post).toHaveBeenCalledWith("desk-user/search", {
                body: { userId: "bkwok01", fetchAll: true }
            });
        });

        it("should handle response with missing data property", async () => {
            const response = {
                data: {
                    message: "No records found",
                    timestamp: "2025-02-23T12:57:03.303Z"
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getWorkSheetFilter({
                userId: "bkwok01"
            });
            expect(result).toBeUndefined();
        });

        it("should handle response with null data", async () => {
            const response = {
                data: {
                    message: "No records found",
                    data: null,
                    timestamp: "2025-02-23T12:57:03.303Z"
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.getWorkSheetFilter({
                userId: "bkwok01"
            });
            expect(result).toBeNull();
        });
        it("should throw an error with the correct extensions when get request fails", async () => {
            const error = new Error("Error while fetching data");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "SOME_ERROR_CODE",
                name: "SOME_NAME",
                message: "Could not fetch data",
                url: ""
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(
                mockErrorPayload
            );

            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);
            try {
                await service.getWorkSheetFilter({
                    userId: "bkwok01"
                });
            } catch (err) {
                const error = err as GraphQLError;
                expect(err).toBeInstanceOf(GraphQLError);
                expect(error.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });
});
