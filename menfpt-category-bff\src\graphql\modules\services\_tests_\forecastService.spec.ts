import "reflect-metadata";
import { ForecastService } from "../forecast.service";
import { GraphQLError } from "graphql";
import { MSP_DATA_POST_FAILURE } from "../../../shared/constants/errorCodes";

const forecastChangeLogAPIResponse: any = [
    {
        divisionId: "20",
        deptId: "301",
        smicCategoryId: 101,
        fiscalWeekNbr: 202501,
        adjustedFields: [Array],
        comment: "Reviewed Again2",
        reason: "Extreme Weather",
        updatedBy: "agupt57",
        updatedTs: "2025-03-04T07: 27: 15.677Z"
    },
    {
        divisionId: "20",
        deptId: "301",
        smicCategoryId: 101,
        fiscalWeekNbr: 202502,
        adjustedFields: [Array],
        comment: "Update",
        reason: "Extreme Weather",
        updatedBy: "agupt57",
        updatedTs: "2025-03-04T07: 31: 42.413Z"
    },
    {
        divisionId: "21",
        deptId: "302",
        smicCategoryId: 102,
        fiscalWeekNbr: 202501,
        adjustedFields: [Array],
        comment: "Reviewed Again2",
        reason: "Extreme Weather",
        updatedBy: "agupt57",
        updatedTs: "2025-03-04T07: 27: 15.677Z"
    },
    {
        divisionId: "21",
        deptId: "302",
        smicCategoryId: 102,
        fiscalWeekNbr: 202502,
        adjustedFields: [Array],
        comment: "Update",
        reason: "Extreme Weather",
        updatedBy: "agupt57",
        updatedTs: "2025-03-04T07: 31: 42.413Z"
    }
];

const SaveAdjustmentResponse = {
    message: "Successfully saved forecast adjustment snapshot!",
    timestamp: "2025-04-02T23:37:42.629Z",
};

const forecastAdjustmentTable = {
    message: "Records found",
    data: [
        {
            fiscalQuarterNbr: 202501,
            line1PublicToSalesNbr: 5131956.442680197,
            line5BookGrossProfitNbr: 2449049.020340421,
            line5BookGrossProfitPct: 0.477215,
            line5MarkDownsNbr: -773753.972759327,
            line5MarkDownsPct: -0.150771,
            line5ShrinkNbr: -105919.264945058,
            line5ShrinkPct: -0.020639,
            line4CostOfSalesNbr: -3556869.582620558,
            line5RealGrossProfitNbr: 1569375.782636036,
            line5RealGrossProfitPct: 0.305804,
            line6SuppliesPackagingNbr: 0,
            line7RetailsAllowancesNbr: 344234.820541694,
            line7RetailsAllowancesPct: 0.067076,
            line8RealGrossProfitNbr: 2793283.840882116,
            line8RealGrossProfitPct: 0.544292
        }
    ],
    timestamp: "2025-03-19T13:18:43.965Z"
};

jest.mock("axios");
jest.mock("../../../shared/classes/BaseAPI.service");

describe("ForecastChangeLogService", () => {
    let service: ForecastService;
    beforeEach(() => {
        service = new ForecastService();
    });

    describe("To get forecast change log details", () => {
        it("should return forecast change log details", async () => {
            const response = {
                data: forecastChangeLogAPIResponse
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const subscriptions: any = await service.getForecastChangeLog({
                keyAttributeName: "Desk",
                keyAttributeValue: ["301-19", "301-20"],
                divisionId: ["30", "31"]
            });

            expect(subscriptions).toEqual(response.data);
        });

        it("should throw an error while running forecast change log details", async () => {
            const error = new Error("Error while fetching data");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "SOME_ERROR_CODE",
                name: "SOME_NAME",
                message: "Could not fetch data",
                url: ""
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(
                mockErrorPayload
            );

            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            try {
                await service.getForecastChangeLog({});
            } catch (err) {
                const error = err as GraphQLError;
                expect(err).toBeInstanceOf(GraphQLError);
                expect(error.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });

    describe("To get adjustment worksheet table data", () => {
        it("should return adjustment worksheet table data", async () => {
            const response = {
                data: forecastAdjustmentTable
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const worksheetData: any = await service.getWorksheetData({
                divisionIds: ["20"],
                deptIds: ["301"],
                smicCategoryIds: ["101"],
                fiscalWeekNbrs: ["202501"],
                fetchOnlyPublished: true
            });

            expect(worksheetData).toEqual(forecastAdjustmentTable.data);
        });

        it("should throw an error while running forecast change log details", async () => {
            const error = new Error("Error while fetching data");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "SOME_ERROR_CODE",
                name: "SOME_NAME",
                message: "Could not fetch data",
                url: ""
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(
                mockErrorPayload
            );

            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            try {
                await service.getWorksheetData({});
            } catch (err) {
                const error = err as GraphQLError;
                expect(err).toBeInstanceOf(GraphQLError);
                expect(error.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });
    describe("To test saveAdjustment", () => {
        it("should return saveAdjustment response", async () => {
            const response = {
                data: SaveAdjustmentResponse
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.SaveAdjustment({
                "deptId": "301",
                "divisionIds": [
                    "30"
                ],
                "smicCategoryIds": [
                    3425
                ],
                "updatedBy": "mshai15",
                "weeks": [
                    {
                        "fiscalWeekNbr": 202501,
                        "editedColumns": "salesToPublic|shrink",
                        "reason": "Extreme Weather",
                        "comment": "Reviewed",
                        "newAggregatedData": {
                            "line1PublicToSalesNbr": 550764.923,
                            "line1PublicToSalesPct": 0.34657,
                            "line5BookGrossProfitNbr": 579151.7687,
                            "line5BookGrossProfitPct": 0.2657,
                            "line5MarkDownsNbr": -362170.8836,
                            "line5MarkDownsPct": 0.1662,
                            "line5ShrinkNbr": -82974.9683,
                            "line5ShrinkPct": 0.0381,
                            "line6SuppliesPackagingNbr": 0,
                            "line6SuppliesPackagingPct": 0,
                            "line7RetailsAllowancesNbr": 255097.6657,
                            "line7RetailsAllowancesPct": 0.0381
                        },
                        "previousAggregatedData": {
                            "line1PublicToSalesNbr": 550764.923,
                            "line1PublicToSalesPct": 0.34657,
                            "line5BookGrossProfitNbr": 579151.7687,
                            "line5BookGrossProfitPct": 0.2657,
                            "line5MarkDownsNbr": -362170.8836,
                            "line5MarkDownsPct": 0.1662,
                            "line5ShrinkNbr": -82974.9683,
                            "line5ShrinkPct": 0.0381,
                            "line6SuppliesPackagingNbr": 0,
                            "line6SuppliesPackagingPct": 0,
                            "line7RetailsAllowancesNbr": 255097.6657,
                            "line7RetailsAllowancesPct": 0.0381
                        }
                    }
                ],
                "deskId": null
            });

            expect(result.data).toEqual(SaveAdjustmentResponse);
        });

        it("should throw an error when SaveAdjustment fails", async () => {
            const error = new Error("Error while saving adjustment");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "SAVE_ERROR",
                name: "SaveError",
                message: "Could not save adjustment",
                url: "saveAdjustment"
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(mockErrorPayload);

            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            try {
                await service.SaveAdjustment({
                    deptId: "301",
                    divisionIds: ["30"],
                    smicCategoryIds: [3425],
                    updatedBy: "mshai15",
                    weeks: []
                });
            } catch (err) {
                const graphQLError = err as GraphQLError;
                expect(graphQLError).toBeInstanceOf(GraphQLError);
                expect(graphQLError.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });

    describe("CalculateAdjustment", () => {
        it("should return calculation response successfully", async () => {
            const response = {
                data: {
                    message: "Calculation completed successfully",
                    result: {
                        adjustedValue: 1500000,
                        adjustmentAmount: 50000
                    }
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                deptId: "301",
                divisionIds: ["30"],
                smicCategoryIds: [3425],
                adjustmentType: "percentage",
                adjustmentValue: 0.05
            };

            const result = await service.CalculateAdjustment(payload);

            expect(result).toEqual(response);
            expect(service.post).toHaveBeenCalledWith("calculate", {
                body: payload
            });
        });

        it("should throw an error when CalculateAdjustment fails", async () => {
            const error = new Error("Error while calculating adjustment");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "CALC_ERROR",
                name: "CalcError",
                message: "Could not calculate adjustment",
                url: "calculate"
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(mockErrorPayload);

            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            try {
                await service.CalculateAdjustment({
                    deptId: "301",
                    divisionIds: ["30"]
                });
            } catch (err) {
                const graphQLError = err as GraphQLError;
                expect(graphQLError).toBeInstanceOf(GraphQLError);
                expect(graphQLError.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });

    describe("SaveCategoryAdjustment", () => {
        it("should return category adjustment save response successfully", async () => {
            const response = {
                data: {
                    message: "Category adjustment saved successfully",
                    savedCount: 5
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                categoryId: "101",
                adjustments: [
                    {
                        fiscalWeekNbr: 202501,
                        adjustmentValue: 10000,
                        reason: "Market conditions"
                    }
                ]
            };

            const result = await service.SaveCategoryAdjustment(payload);

            expect(result).toEqual(response);
            expect(service.post).toHaveBeenCalledWith("categories/saveAdjustments", {
                body: payload
            });
        });

        it("should throw an error when SaveCategoryAdjustment fails", async () => {
            const error = new Error("Error while saving category adjustment");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "CATEGORY_SAVE_ERROR",
                name: "CategorySaveError",
                message: "Could not save category adjustment",
                url: "categories/saveAdjustments"
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(mockErrorPayload);

            (
                service.post as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            try {
                await service.SaveCategoryAdjustment({
                    categoryId: "101",
                    adjustments: []
                });
            } catch (err) {
                const graphQLError = err as GraphQLError;
                expect(graphQLError).toBeInstanceOf(GraphQLError);
                expect(graphQLError.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });

    describe("UpdateForecastSnapshot", () => {
        it("should return forecast snapshot update response successfully", async () => {
            const response = {
                data: {
                    message: "Forecast snapshot updated successfully",
                    snapshotId: "snapshot_123"
                }
            };
            (
                service.put as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const payload = {
                snapshotId: "snapshot_123",
                status: "published",
                updatedBy: "mshai15"
            };

            const result = await service.UpdateForecastSnapshot(payload);

            expect(result).toEqual(response);
            expect(service.put).toHaveBeenCalledWith("forecast/update/status", {
                body: payload
            });
        });

        it("should throw an error when UpdateForecastSnapshot fails", async () => {
            const error = new Error("Error while updating forecast snapshot");
            const mockErrorPayload = {
                httpStatus: 400,
                errorCode: "UPDATE_ERROR",
                name: "UpdateError",
                message: "Could not update forecast snapshot",
                url: "forecast/update/status"
            };
            jest.spyOn(service, "createErrorPayload").mockReturnValue(mockErrorPayload);

            (
                service.put as jest.MockedFunction<() => Promise<never>>
            ).mockRejectedValueOnce(error);

            try {
                await service.UpdateForecastSnapshot({
                    snapshotId: "snapshot_123",
                    status: "published"
                });
            } catch (err) {
                const graphQLError = err as GraphQLError;
                expect(graphQLError).toBeInstanceOf(GraphQLError);
                expect(graphQLError.extensions).toEqual({
                    errorType: MSP_DATA_POST_FAILURE,
                    ...mockErrorPayload
                });
                expect(service.createErrorPayload).toHaveBeenCalledWith(error);
            }
        });
    });

    describe("getWorksheetData - additional scenarios", () => {
        it("should handle forecast endpoint correctly", async () => {
            const response = {
                data: {
                    data: [
                        {
                            fiscalWeekNbr: 202501,
                            sales: 1000000,
                            profit: 250000
                        }
                    ]
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const actualsReq = {
                endPoint: "forecast",
                divisionIds: ["20"],
                deptIds: ["301"]
            };

            const result = await service.getWorksheetData(actualsReq);

            expect(result).toEqual(response.data.data);
            expect(service.post).toHaveBeenCalledWith("aggregate/search", {
                body: actualsReq
            });
        });

        it("should handle non-forecast endpoint correctly", async () => {
            const response = {
                data: {
                    data: [
                        {
                            fiscalWeekNbr: 202501,
                            actualSales: 950000,
                            actualProfit: 237500
                        }
                    ]
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const actualsReq = {
                endPoint: "actuals",
                divisionIds: ["20"],
                deptIds: ["301"]
            };

            const result = await service.getWorksheetData(actualsReq);

            expect(result).toEqual(response.data.data);
            expect(service.post).toHaveBeenCalledWith("actuals/search", {
                body: actualsReq
            });
        });

        it("should handle missing response.data.data", async () => {
            const response = {
                data: {
                    message: "No data found"
                }
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const actualsReq = {
                endPoint: "forecast",
                divisionIds: ["20"]
            };

            const result = await service.getWorksheetData(actualsReq);

            expect(result).toBeUndefined();
        });
    });
});
