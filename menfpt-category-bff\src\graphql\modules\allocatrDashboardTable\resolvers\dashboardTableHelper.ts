export interface AggregateApiData {
    aggregationLevel: string,
    fiscalYearNbr?: string,
    fiscalPeriodNbr?: number,
    fiscalWeekNbr?: number,
    fiscalQuarterNbr?: number,
    divisionId: string,
    deptId: string,
    totalLine1Sales: number,
    totalLine4CostOfSales: number,
    totalLine5BookGrossProfit: number,
    totalLine5MarkDowns: number,
    totalLine5Shrink: number,
    totalLine5RealGrossProfit: number,
    totalLine6SuppliesPackaging: number
    totalLine7RetailAllowances: number
    totalLine8RealGrossProfit: number,
    storeIndicator: string,
    lastyearTotalSales?: number
}

export enum AggregationRanges {
    WEEK = "Week",
    PERIOD = "Period",
    QUARTER = "Quarter"
}

export enum AggregationLevels {
    ACTUALS = "actuals",
    FORECAST = "forecast",
    PROJECTION = "projection"
}

export enum FieldNames {
    LINE_1 = "totalLine1Sales",
    LINE_5_BOOK_GROSS_PROFIT = "totalLine5BookGrossProfit",
    LINE_5_MARKDOWNS = "totalLine5MarkDowns",
    LINE_5_SHRINK = "totalLine5Shrink",
    LY_LINE_1 = "lastyearTotalSales",
    LINE_6 = "totalLine6SuppliesPackaging",
    LINE_7 = "totalLine7RetailAllowances"
}

export const aggregationLevelFields = {
    [AggregationRanges.PERIOD]: "fiscalPeriodNbr",
    [AggregationRanges.WEEK]: "fiscalWeekNbr",
    [AggregationRanges.QUARTER]: "fiscalQuarterNbr"
};

export const anyLineItemExists = (item: any) => {
    // Get all FieldNames except LY_LINE_1
    const fieldsToCheck = Object.values(FieldNames).filter(field => field !== FieldNames.LY_LINE_1);
    // Check if any of the fields exist and have non-zero values
    return fieldsToCheck.some(field => {
        const value = item?.[field];
        return value !== undefined && value !== null && value !== 0;
    });
}

const totalData = (identicalData: any, keeperData: any, field: string) => {
    const identical = identicalData?.[field] || 0;
    const keeper = keeperData?.[field] || 0;
    return identical + keeper;
};

const getIdPercentage = (lastYear: number, fcstLine1: number) => {
    let idPercentage = 0;
    if (lastYear !== 0) {
        idPercentage = (fcstLine1 - lastYear) / lastYear;
    }
    return idPercentage;
};

const getTotalLineItem = (identical: any, keeper: any, field: string) => {
    const id = identical?.[field] || 0;
    const keeperId = keeper?.[field] || 0;
    return id + keeperId;
};

const getPct = (value: number, total: number) => {
    if (total === 0) {
        return 0;
    }
    return (value / total);
}

const _constructKey = (_key1: string, _key2: string) => {
    return `${_key1}#${_key2}`;
};

const constructKeyWithItem = (item: any, value: any) => {
    const aggregationLevel = item.aggregationLevel;
    return `${aggregationLevel}#${item.deptId}#${value}`;
};

export const getBaseAggregatedData = (
    proj: any,
    actual: any,
    fcst: any,
    projKeeper: any = {},
    actualKeepr: any = {},
    fcstKeeper: any = {},
    type: string,
    id: number,
    weekToPeriodMap: Map<number, number>,
    hasAllActuals: Map<string, boolean> = new Map()
) => {
    // Use actual if available, otherwise forecast
    const actualLine1 = totalData(actual, actualKeepr, FieldNames.LINE_1);
    const fcstLine1 = totalData(fcst, fcstKeeper, FieldNames.LINE_1);
    const lastYearLine1 = totalData(actual, actualKeepr, FieldNames.LY_LINE_1);
    const projectionLine1 = totalData(proj, projKeeper, FieldNames.LINE_1);
    let isActualUsed = anyLineItemExists(actual);
    let totalFcstOrActualLine1 = isActualUsed ? actualLine1 : fcstLine1;
    if (type === 'Week' && actual.deptId === "Total") {
        totalFcstOrActualLine1 = actualLine1 + fcstLine1;
        if (actualLine1 && fcstLine1) {
            isActualUsed = false;
        }
    }

    const idPercentage = getIdPercentage(lastYearLine1, totalFcstOrActualLine1);

    // vsLY value formula
    const vsLYValue = totalFcstOrActualLine1 - lastYearLine1;
    const vsLYPercentage = lastYearLine1 ? (vsLYValue / lastYearLine1) : 0;

    // vsProjection formula
    const vsProjectionValue = totalFcstOrActualLine1 - projectionLine1;
    const vsProjectionPercentage = projectionLine1 ? (vsProjectionValue / projectionLine1) : 0;

    // get Book Gross Profit, Markdown, and Shrink Objects
    const _getSubLine = (_field: string) => {
        const _projectionValue = getTotalLineItem(proj, projKeeper, _field);
        const _projectionPct = getPct(
            _projectionValue,
            projectionLine1
        );
        const _actualValue = getTotalLineItem(actual, actualKeepr, _field)
        const _forecastValue = getTotalLineItem(fcst, fcstKeeper, _field);
        let _actualOrFcstValue = isActualUsed ? _actualValue : _forecastValue;
        if (type === 'Week' && actual.deptId === "Total") {
            _actualOrFcstValue = _actualValue + _forecastValue;
        }

        const _actualOrFcstPct = getPct(
            _actualOrFcstValue,
            totalFcstOrActualLine1
        );
        const _vsProjection = _actualOrFcstPct - _projectionPct;
        return {
            projectionValue: _projectionValue,
            projectionPct: _projectionPct,
            actualOrForecast: _actualOrFcstValue,
            percentActualOrForecast: _actualOrFcstPct,
            vsProjection: _vsProjection
        };
    };

    // get Line 5 object
    const _getLine5 = (_bookGrossProfit: any, _markdown: any, _shrink: any) => {
        const _projectionValueBookGrossProfit = getTotalLineItem(
            proj,
            projKeeper,
            FieldNames.LINE_5_BOOK_GROSS_PROFIT
        );
        const _projectionValueMarkdown = getTotalLineItem(
            proj,
            projKeeper,
            FieldNames.LINE_5_MARKDOWNS
        );
        const _projectionValueShrink = getTotalLineItem(
            proj,
            projKeeper,
            FieldNames.LINE_5_SHRINK
        );

        const _sumLine5Proj =
            _projectionValueBookGrossProfit +
            _projectionValueMarkdown +
            _projectionValueShrink;
        const _sumLine5PctProj =
            _bookGrossProfit.projectionPct +
            _markdown.projectionPct +
            _shrink.projectionPct;

        const _line5ActualOrForecast =
            totalFcstOrActualLine1 *
            (_bookGrossProfit.percentActualOrForecast + _markdown.percentActualOrForecast + _shrink.percentActualOrForecast);
        const _line5ActualOrForecastPct = (_line5ActualOrForecast && totalFcstOrActualLine1)
            ? (_line5ActualOrForecast / totalFcstOrActualLine1)
            : 0;

        const _vsProjection = (_line5ActualOrForecast - _sumLine5Proj).toFixed(4);
        const _vsProjectionPct = (_line5ActualOrForecastPct - _sumLine5PctProj).toFixed(4);

        return {
            actualOrForecast: _line5ActualOrForecast || 0,
            percentActualOrForecast: _line5ActualOrForecastPct || 0,
            projectionValue: _sumLine5Proj,
            projectionPct: _sumLine5PctProj,
            vsProjection: _vsProjection,
            percentVsProjection: _vsProjectionPct
        };
    };

    const _getLine6And7 = (_field: string) => {
        const _projectValue = getTotalLineItem(proj, projKeeper, _field);
        const _actualValue = getTotalLineItem(actual, actualKeepr, _field);
        const _forecastValue = getTotalLineItem(fcst, fcstKeeper, _field);
        let _actualOrFcstValue = isActualUsed ? _actualValue : _forecastValue;
        if (type === 'Week' && actual.deptId === "Total") {
            _actualOrFcstValue = _actualValue + _forecastValue;
        }
        const _vsProjection = _actualOrFcstValue - _projectValue;
        return {
            projection: _projectValue,
            actualOrForecast: _actualOrFcstValue || 0,
            vsProjection: _vsProjection
        };
    };

    const _getLine8 = (_line5: any, _line6: any, _line7: any) => {

        const _actualOrForecast = _line5.actualOrForecast + _line6.actualOrForecast + _line7.actualOrForecast;
        const _actualOrForecastPct = (_actualOrForecast && totalFcstOrActualLine1) ? (_actualOrForecast / totalFcstOrActualLine1) : 0;

        const _projection =
            _line5.projectionValue + _line6.projection + _line7.projection;
        const _projectionPct = (_projection && projectionLine1)
            ? (_projection / projectionLine1)
            : 0;

        const _vsProjection = (_actualOrForecast - _projection).toFixed(4);
        const _vsProjectionPct = (_actualOrForecastPct - _projectionPct).toFixed(4);

        return {
            actualOrForecast: _actualOrForecast || 0,
            percentActualOrForecast: _actualOrForecastPct || 0,
            projectionValue: _projection,
            projectionPct: _projectionPct,
            vsProjection: _vsProjection,
            percentVsProjection: _vsProjectionPct
        };
    };

    const bookGrossProfit = _getSubLine(FieldNames.LINE_5_BOOK_GROSS_PROFIT);
    const markdown = _getSubLine(FieldNames.LINE_5_MARKDOWNS);
    const shrink = _getSubLine(FieldNames.LINE_5_SHRINK);
    const line5 = _getLine5(bookGrossProfit, markdown, shrink);
    const line6 = _getLine6And7(FieldNames.LINE_6);
    const line7 = _getLine6And7(FieldNames.LINE_7);
    const line8 = _getLine8(line5, line6, line7);

    const aggregatedData: any = {
        id: `${type}-${id}`,
        line1Projection: projectionLine1,
        lastYear: lastYearLine1,
        actualOrForecast: totalFcstOrActualLine1,
        idPercentage,
        vsLY: {
            value: vsLYValue,
            percentage: vsLYPercentage
        },
        vsProjection: {
            value: vsProjectionValue,
            percentage: vsProjectionPercentage
        },
        bookGrossProfit,
        markdown,
        shrink,
        line5,
        line6,
        line7,
        line8,
        isActualUsed
    };

    switch (type) {
        case "week":
        case "Week":
            aggregatedData.weekNumber = id;
            aggregatedData.periodNumber = weekToPeriodMap.get(id);
            break;
        case "period":
        case "Period":
            aggregatedData.periodNumber = id;
            break;
        default:
            aggregatedData.quarterNumber = id;
    }

    return aggregatedData;
};

const createBaseObject = (aggregationLevel: string = "", deptId: string, divisionId: string, storeIndicator: string) => {
    const data = {
        aggregationLevel,
        deptId,
        divisionId,
        storeIndicator,
        totalLine1Sales: 0,
        totalLine4CostOfSales: 0,
        totalLine5BookGrossProfit: 0,
        totalLine5MarkDowns: 0,
        totalLine5Shrink: 0,
        totalLine5RealGrossProfit: 0,
        totalLine6SuppliesPackaging: 0,
        totalLine7RetailAllowances: 0,
        totalLine8RealGrossProfit: 0,
        lastyearTotalSales: 0,
    }
    return data;
}

export const sumDataByAggregationRange = (data: any[] = [], isTotal = true, _aggregationLevel: string = "", deptToActuals = new Map<string, boolean>()) => {
    const newData: any = {}
    const applicableFields = Object.values(FieldNames);
    data.forEach((item: any) => {
        const aggregationLevel = _aggregationLevel as AggregationRanges || item.aggregationLevel as AggregationRanges;
        const key = _constructKey(
            aggregationLevel,
            item[aggregationLevelFields[aggregationLevel] as keyof typeof aggregationLevelFields]
        );
        if (!newData[key] && !deptToActuals.get(constructKeyWithItem(item, item.fiscalWeekNbr))) {
            newData[key] = { ...item };
            newData[key].aggregationLevel = aggregationLevel;
            if (isTotal) {
                newData[key].deptId = "Total";
            }
        } else if (!deptToActuals.get(constructKeyWithItem(item, item.fiscalWeekNbr))) {
            Object.keys(item).forEach(subItem => {
                if (typeof newData[key][subItem] === "number" && applicableFields.includes(subItem as FieldNames)) {
                    newData[key][subItem] += item[subItem] || 0;
                }
            });
        }

    });
    return Object.values(newData);
}

export const rollUpDataFromWeek = (actualdata: any[],
    forecastData: any[],
    aggregationLevel: string,
    allDeptIds: any[],
    allWeekNumbers: any[],
    fiscalIds: Map<number, number> | number) => {

    return allDeptIds.map((deptId: string) => {
        const allWeeksData = allWeekNumbers.map((weekNumber: number) => {
            const actualItem = { ...actualdata?.find(item => item.deptId === deptId && item.fiscalWeekNbr === weekNumber) };
            const forecastItem = { ...forecastData?.find(item => item.deptId === deptId && item.fiscalWeekNbr === weekNumber) };
            let selectedItem: AggregateApiData = createBaseObject(aggregationLevel, deptId, actualItem?.divisionId || "", actualItem?.storeIndicator || "");
            if (anyLineItemExists(actualItem)) {
                selectedItem = actualItem;
            } else if (anyLineItemExists(forecastItem)) {
                selectedItem = forecastItem
            }
            selectedItem.lastyearTotalSales = actualItem?.lastyearTotalSales || 0;
            if (fiscalIds && fiscalIds instanceof Map) {
                selectedItem.fiscalPeriodNbr = fiscalIds.get(weekNumber) || 0;
            } else if (typeof fiscalIds === "number") {
                selectedItem.fiscalQuarterNbr = fiscalIds;
            }
            delete selectedItem.fiscalWeekNbr;
            return selectedItem;
        });
        return sumDataByAggregationRange(allWeeksData, false, aggregationLevel);
    }).flat();
}
