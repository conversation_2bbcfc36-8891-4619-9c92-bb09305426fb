import "reflect-metadata";
import { testkit, gql, MockedApplication } from "graphql-modules";
import { gqlApp } from "../modules/index";
import WorkSheetFilterModule from "../modules/workSheetTableFilter";
import { WorkSheetFilterProvider } from "../modules/workSheetTableFilter/providers/workSheetFilter.provider";
import { RoleMappingService } from "../modules/services/roleMapping.service";
const getWorkSheetFilterResponse = {
    smicData: [
        {
            divisionId: 27,
            divisionName: "Seattle",
            deptId: 311,
            deptName: "GM/HBC",
            smicGroupCd: 52,
            smicGroupDesc: "HAIR CARE",
            smicCategoryCd: 55,
            smicCategoryDesc: "HAIR CARE MULTI-CULTURAL",
            smicCategoryId: 5255,
            retailSectionCd: 312,
            retailSectionName: "FAMILY CARE GMHBC",
            deskId: "27-25",
            deskName: "27-B<PERSON><PERSON><PERSON> KWOK"
        },
        {
            divisionId: 28,
            divisionName: "Portland",
            deptId: 312,
            deptName: "GROCERY",
            smicGroupCd: 53,
            smicGroupDesc: "SOFT DRINKS",
            smicCategoryCd: 56,
            smicCategoryDesc: "CARBONATED DRINKS",
            smicCategoryId: 5356,
            retailSectionCd: 313,
            retailSectionName: "BEVERAGES",
            deskId: "28-26",
            deskName: "28-JOHN DOE"
        },
        {
            divisionId: 29,
            divisionName: "San Francisco",
            deptId: 313,
            deptName: "PRODUCE",
            smicGroupCd: 54,
            smicGroupDesc: "CITRUS",
            smicCategoryCd: 57,
            smicCategoryDesc: "ORANGES",
            smicCategoryId: 5457,
            retailSectionCd: 314,
            retailSectionName: "FRUITS",
            deskId: "29-27",
            deskName: "29-JANE SMITH"
        }
    ],
    userId: "bkwok01",
    userName: "BONNIE KWOK",
    userEmail: "<EMAIL>",
    userRole: "ASM",
    createdTimeStamp: "2025-03-29T07:36:56.307Z",
    updatedTimeStamp: "2025-03-29T07:36:56.307Z",
    createdBy: "NFPT System",
    updatedBy: "NFPT System"
};
describe("getWorkSheetFilter", () => {
    let service: RoleMappingService;
    beforeEach(() => {
        service = new RoleMappingService();
    });
    describe("Testing positive scenarios for getWorkSheetFilter details", () => {
        let app: MockedApplication;
        beforeAll(() => {
            app = testkit.mockApplication(gqlApp).replaceModule(
                testkit.mockModule(WorkSheetFilterModule, {
                    providers: [
                        {
                            provide: WorkSheetFilterProvider.provide,
                            useValue: {
                                getWorkSheetFilter() {
                                    return getWorkSheetFilterResponse;
                                }
                            }
                        }
                    ]
                })
            );
        });
        it("Should return the correct response for getWorkSheetFilter query", async () => {
            const result = await testkit.execute(app, {
                document: gql`
                    {
                        getWorkSheetFilter(
                            workSheetFilterRequest: $workSheetFilterRequest
                        ) {
                            smicData {
                                divisionId
                                divisionName
                                deptId
                                deptName
                                smicGroupCd
                                smicGroupDesc
                                smicCategoryCd
                                smicCategoryDesc
                                smicCategoryId
                                retailSectionCd
                                retailSectionName
                                deskId
                                deskName
                            }
                            userId
                            userName
                            userEmail
                            userRole
                            createdTimeStamp
                            updatedTimeStamp
                            createdBy
                            updatedBy
                        }
                    }
                `,
                contextValue: {}
            });
            expect(result?.data?.getWorkSheetFilter).toEqual({
                smicData: [
                    {
                        divisionId: null,
                        divisionName: null,
                        deptId: null,
                        deptName: null,
                        smicGroupCd: null,
                        smicGroupDesc: null,
                        smicCategoryCd: null,
                        smicCategoryDesc: null,
                        smicCategoryId: null,
                        retailSectionCd: null,
                        retailSectionName: null,
                        deskId: null,
                        deskName: null
                    },
                    {
                        divisionId: null,
                        divisionName: null,
                        deptId: null,
                        deptName: null,
                        smicGroupCd: null,
                        smicGroupDesc: null,
                        smicCategoryCd: null,
                        smicCategoryDesc: null,
                        smicCategoryId: null,
                        retailSectionCd: null,
                        retailSectionName: null,
                        deskId: null,
                        deskName: null
                    },
                    {
                        divisionId: null,
                        divisionName: null,
                        deptId: null,
                        deptName: null,
                        smicGroupCd: null,
                        smicGroupDesc: null,
                        smicCategoryCd: null,
                        smicCategoryDesc: null,
                        smicCategoryId: null,
                        retailSectionCd: null,
                        retailSectionName: null,
                        deskId: null,
                        deskName: null
                    }
                ],
                userId: null,
                userName: null,
                userEmail: null,
                userRole: null,
                createdTimeStamp: null,
                updatedTimeStamp: null,
                createdBy: null,
                updatedBy: null
            });
        });
    });
});
