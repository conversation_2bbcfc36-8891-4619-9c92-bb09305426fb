export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  BigInt: any;
  JSON: any;
  Upload: any;
};

export type AdjustedFields = {
  __typename?: 'AdjustedFields';
  fieldName?: Maybe<Scalars['String']>;
  newValue?: Maybe<Scalars['String']>;
  oldValue?: Maybe<Scalars['String']>;
};

export type Adjustment = {
  bannerId?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  deptId?: InputMaybe<Scalars['String']>;
  deskId?: InputMaybe<Scalars['String']>;
  divisionIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  isReset?: InputMaybe<Scalars['Boolean']>;
  smicCategoryIds?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  updatedBy?: InputMaybe<Scalars['String']>;
  weeks?: InputMaybe<Array<InputMaybe<Week>>>;
};

export type AdjustmentData = {
  line1PublicToSalesNbr?: InputMaybe<Scalars['Float']>;
  line1PublicToSalesPct?: InputMaybe<Scalars['Float']>;
  line5BookGrossProfitNbr?: InputMaybe<Scalars['Float']>;
  line5BookGrossProfitPct?: InputMaybe<Scalars['Float']>;
  line5MarkDownsNbr?: InputMaybe<Scalars['Float']>;
  line5MarkDownsPct?: InputMaybe<Scalars['Float']>;
  line5ShrinkNbr?: InputMaybe<Scalars['Float']>;
  line5ShrinkPct?: InputMaybe<Scalars['Float']>;
  line6SuppliesPackagingNbr?: InputMaybe<Scalars['Float']>;
  line6SuppliesPackagingPct?: InputMaybe<Scalars['Float']>;
  line7RetailsAllowancesNbr?: InputMaybe<Scalars['Float']>;
  line7RetailsNonSellingAllowancesNbr?: InputMaybe<Scalars['Float']>;
  line7RetailsSellingAllowancesNbr?: InputMaybe<Scalars['Float']>;
};

export type AdjustmentWorksheetAggregated = {
  __typename?: 'AdjustmentWorksheetAggregated';
  aggregatedLevel?: Maybe<Scalars['String']>;
  bannerId?: Maybe<Array<Maybe<Scalars['String']>>>;
  comment?: Maybe<Scalars['String']>;
  createdBy?: Maybe<Scalars['String']>;
  createdTs?: Maybe<Scalars['String']>;
  fiscalPeriodNbr?: Maybe<Scalars['Int']>;
  fiscalQuarterNbr?: Maybe<Scalars['Int']>;
  fiscalWeekEnding?: Maybe<Scalars['String']>;
  fiscalWeekNbr?: Maybe<Scalars['Int']>;
  fiscalYearNbr?: Maybe<Scalars['Int']>;
  forecastType?: Maybe<Scalars['String']>;
  lastUpdatedUserRole?: Maybe<Scalars['String']>;
  line1PublicToSalesNbr?: Maybe<Scalars['Float']>;
  line1PublicToSalesPct?: Maybe<Scalars['Float']>;
  line5BookGrossProfitNbr?: Maybe<Scalars['Float']>;
  line5BookGrossProfitPct?: Maybe<Scalars['Float']>;
  line5MarkDownsNbr?: Maybe<Scalars['Float']>;
  line5MarkDownsPct?: Maybe<Scalars['Float']>;
  line5RealGrossProfitNbr?: Maybe<Scalars['Float']>;
  line5RealGrossProfitPct?: Maybe<Scalars['Float']>;
  line5ShrinkNbr?: Maybe<Scalars['Float']>;
  line5ShrinkPct?: Maybe<Scalars['Float']>;
  line6SuppliesPackagingNbr?: Maybe<Scalars['Float']>;
  line7RetailsAllowancesNbr?: Maybe<Scalars['Float']>;
  line7RetailsAllowancesPct?: Maybe<Scalars['Float']>;
  line7RetailsNonSellingAllowancesNbr?: Maybe<Scalars['Float']>;
  line7RetailsNonSellingAllowancesPct?: Maybe<Scalars['Float']>;
  line7RetailsSellingAllowancesNbr?: Maybe<Scalars['Float']>;
  line7RetailsSellingAllowancesPct?: Maybe<Scalars['Float']>;
  line8RealGrossProfitNbr?: Maybe<Scalars['Float']>;
  line8RealGrossProfitPct?: Maybe<Scalars['Float']>;
  mainRow?: Maybe<Scalars['String']>;
  reason?: Maybe<Scalars['String']>;
  sourceTs?: Maybe<Scalars['String']>;
  state?: Maybe<Scalars['String']>;
  subRow?: Maybe<Scalars['String']>;
  updatedBy?: Maybe<Scalars['String']>;
  updatedTs?: Maybe<Scalars['String']>;
  versionNbr?: Maybe<Scalars['Int']>;
};

export type AdjustmentWorksheetReq = {
  bannerId?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  currentFiscalPeriodNbr?: InputMaybe<Scalars['Int']>;
  currentFiscalWeekNbr?: InputMaybe<Scalars['Int']>;
  currentFiscalYearNbr?: InputMaybe<Scalars['Int']>;
  deptIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  divisionIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  quarterEndingDate?: InputMaybe<Scalars['String']>;
  quarterNbr?: InputMaybe<Scalars['Int']>;
  smicCategoryIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type AdjustmentWorksheetRes = {
  __typename?: 'AdjustmentWorksheetRes';
  adjustmentWorksheetData?: Maybe<Array<Maybe<AdjustmentWorksheetAggregated>>>;
  forecastData?: Maybe<Array<Maybe<NonAggregatedForecastData>>>;
};

export type AllocatrDashboardReq = {
  currentFiscalYearNbr: Scalars['Int'];
  deptIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  divisionBannerPairs?: InputMaybe<Array<InputMaybe<DivisionBanner>>>;
  filteredWeekNumbers?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  latestReleaseWeek?: InputMaybe<LatestWeek>;
  periodNumbers?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  quarterNbr: Scalars['Int'];
  smicCategoryIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  type?: InputMaybe<Scalars['String']>;
  weekNumbers?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
};

export type AllocatrDashboardRes = {
  __typename?: 'AllocatrDashboardRes';
  allocatrDashboardTableData?: Maybe<Array<Maybe<AllocatrDashboardResponseData>>>;
};

export type AllocatrDashboardResponseData = {
  __typename?: 'AllocatrDashboardResponseData';
  id?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  periods?: Maybe<Array<Maybe<PeriodData>>>;
  quarter?: Maybe<QuarterData>;
  weeks?: Maybe<Array<Maybe<WeekData>>>;
};

export type BookGrossProfit = {
  __typename?: 'BookGrossProfit';
  actualOrForecast?: Maybe<Scalars['Float']>;
  percentActualOrForecast?: Maybe<Scalars['Float']>;
  projectionPct?: Maybe<Scalars['Float']>;
  projectionValue?: Maybe<Scalars['Float']>;
  vsProjection?: Maybe<Scalars['Float']>;
};

export type CronScheduleInfo = {
  __typename?: 'CronScheduleInfo';
  friday?: Maybe<Scalars['String']>;
  monday?: Maybe<Scalars['String']>;
  thursday?: Maybe<Scalars['String']>;
};

export type DatabricksJobRun = {
  __typename?: 'DatabricksJobRun';
  end_time?: Maybe<Scalars['String']>;
  result_state?: Maybe<Scalars['String']>;
  start_time?: Maybe<Scalars['String']>;
};

export type DatabricksJobRunsResult = {
  __typename?: 'DatabricksJobRunsResult';
  lastSync?: Maybe<LastSync>;
  nextSync?: Maybe<NextSync>;
  syncHistory?: Maybe<SyncHistory>;
  syncSessions?: Maybe<Array<Maybe<SyncSession>>>;
};

export type DeactivateUserData = {
  __typename?: 'DeactivateUserData';
  effectiveEndDate: Scalars['String'];
  isActive: Scalars['String'];
  updatedBy: Scalars['String'];
  updatedTs: Scalars['String'];
  userId: Scalars['String'];
};

export type DeactivateUserInput = {
  divisionBannerPairs: Array<DivisionBannerPairInput>;
  effectiveEndDate: Scalars['String'];
  userId: Scalars['String'];
};

export type DeactivateUserResponse = {
  __typename?: 'DeactivateUserResponse';
  data: DeactivateUserData;
  message: Scalars['String'];
  timestamp: Scalars['String'];
};

export type DisplayDateReq = {
  date?: InputMaybe<Scalars['String']>;
  qtrNumbersArr?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
};

export type DisplayDateRes = {
  __typename?: 'DisplayDateRes';
  calendarDetails?: Maybe<Array<Maybe<DisplayDateRes>>>;
  createTimestamp?: Maybe<Scalars['String']>;
  fiscalPeriodCertificationDate?: Maybe<Scalars['String']>;
  fiscalPeriodEndDate?: Maybe<Scalars['String']>;
  fiscalPeriodLockoutDate?: Maybe<Scalars['String']>;
  fiscalPeriodNumber?: Maybe<Scalars['Int']>;
  fiscalPeriodStartDate?: Maybe<Scalars['String']>;
  fiscalQuarterEndDate?: Maybe<Scalars['String']>;
  fiscalQuarterNumber?: Maybe<Scalars['Int']>;
  fiscalQuarterStartDate?: Maybe<Scalars['String']>;
  fiscalWeekEndDate?: Maybe<Scalars['String']>;
  fiscalWeekNumber?: Maybe<Scalars['Int']>;
  fiscalWeekStartDate?: Maybe<Scalars['String']>;
  fiscalYearNumber?: Maybe<Scalars['Int']>;
  updateTimestamp?: Maybe<Scalars['String']>;
};

export type DivisionBanner = {
  bannerId?: InputMaybe<Scalars['String']>;
  divisionId?: InputMaybe<Scalars['String']>;
};

export type DivisionBannerPairInput = {
  bannerId: Scalars['String'];
  divisionId: Scalars['String'];
};

export type DivisionBannerUsersUserMgmtDepartment = {
  __typename?: 'DivisionBannerUsersUserMgmtDepartment';
  deptId: Scalars['String'];
  deptName: Scalars['String'];
  deskId: Array<Scalars['String']>;
  deskName: Array<Scalars['String']>;
};

export type DivisionBannerUsersUserMgmtInput = {
  divisionBannerPairs: Array<DivisionBannerPairInput>;
};

export type DivisionBannerUsersUserMgmtResponse = {
  __typename?: 'DivisionBannerUsersUserMgmtResponse';
  data: Array<DivisionBannerUsersUserMgmtUser>;
  message: Scalars['String'];
  timestamp: Scalars['String'];
};

export type DivisionBannerUsersUserMgmtUser = {
  __typename?: 'DivisionBannerUsersUserMgmtUser';
  bannerId: Array<Scalars['String']>;
  bannerName: Array<Scalars['String']>;
  departments: Array<DivisionBannerUsersUserMgmtDepartment>;
  divisionId: Scalars['String'];
  effectiveEndDate?: Maybe<Scalars['String']>;
  effectiveStartDate: Scalars['String'];
  isActive: Scalars['Boolean'];
  isSuperUser: Scalars['Boolean'];
  managerEmail: Scalars['String'];
  managerName: Scalars['String'];
  updatedBy: Scalars['String'];
  updatedTs: Scalars['String'];
  userName: Scalars['String'];
  userRole: Scalars['String'];
};

export type EnvVariablesResponse = {
  __typename?: 'EnvVariablesResponse';
  variables: Scalars['JSON'];
};

export type FetchJobRunsInput = {
  jobId?: InputMaybe<Scalars['BigInt']>;
  jobName?: InputMaybe<Scalars['String']>;
  limit?: InputMaybe<Scalars['Int']>;
};

export type FiscalWeekDates = {
  __typename?: 'FiscalWeekDates';
  friday?: Maybe<Scalars['String']>;
  monday?: Maybe<Scalars['String']>;
  thursday?: Maybe<Scalars['String']>;
};

export type ForecastChangeLog = {
  __typename?: 'ForecastChangeLog';
  editedColumns?: Maybe<Scalars['String']>;
  updatedBy?: Maybe<Scalars['String']>;
  updatedMetrics?: Maybe<Array<Maybe<UpdatedMetrics>>>;
  updatedTimestamp?: Maybe<Scalars['String']>;
};

export type ForecastChangeLogReq = {
  bannerId?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  divisionId?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  keyAttributeName?: InputMaybe<Scalars['String']>;
  keyAttributeValue?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type GetUploadedDocumentsRes = {
  __typename?: 'GetUploadedDocumentsRes';
  uploadedDocuments?: Maybe<Array<Maybe<UploadedDocument>>>;
};

export type LastSync = {
  __typename?: 'LastSync';
  sync_day?: Maybe<Scalars['String']>;
  sync_time?: Maybe<Scalars['String']>;
};

export type Line5 = {
  __typename?: 'Line5';
  actualOrForecast?: Maybe<Scalars['Float']>;
  percentActualOrForecast?: Maybe<Scalars['Float']>;
  percentVsProjection?: Maybe<Scalars['Float']>;
  projectionPct?: Maybe<Scalars['Float']>;
  projectionValue?: Maybe<Scalars['Float']>;
  vsProjection?: Maybe<Scalars['Float']>;
};

export type Line6 = {
  __typename?: 'Line6';
  actualOrForecast?: Maybe<Scalars['Float']>;
  projection?: Maybe<Scalars['Float']>;
  vsProjection?: Maybe<Scalars['Float']>;
};

export type Line7 = {
  __typename?: 'Line7';
  actualOrForecast?: Maybe<Scalars['Float']>;
  projection?: Maybe<Scalars['Float']>;
  vsProjection?: Maybe<Scalars['Float']>;
};

export type Line8 = {
  __typename?: 'Line8';
  actualOrForecast?: Maybe<Scalars['Float']>;
  percentActualOrForecast?: Maybe<Scalars['Float']>;
  percentVsProjection?: Maybe<Scalars['Float']>;
  projectionPct?: Maybe<Scalars['Float']>;
  projectionValue?: Maybe<Scalars['Float']>;
  vsProjection?: Maybe<Scalars['Float']>;
};

export type Markdown = {
  __typename?: 'Markdown';
  actualOrForecast?: Maybe<Scalars['Float']>;
  percentActualOrForecast?: Maybe<Scalars['Float']>;
  projectionPct?: Maybe<Scalars['Float']>;
  projectionValue?: Maybe<Scalars['Float']>;
  vsProjection?: Maybe<Scalars['Float']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  userDeactivationUserMgmt: DeactivateUserResponse;
};


export type MutationUserDeactivationUserMgmtArgs = {
  input: DeactivateUserInput;
};

export type NextSync = {
  __typename?: 'NextSync';
  sync_day?: Maybe<Scalars['String']>;
  sync_time?: Maybe<Scalars['String']>;
};

export type PeriodData = {
  __typename?: 'PeriodData';
  actualOrForecast?: Maybe<Scalars['Float']>;
  bookGrossProfit?: Maybe<BookGrossProfit>;
  id?: Maybe<Scalars['String']>;
  idPercentage?: Maybe<Scalars['Float']>;
  lastYear?: Maybe<Scalars['Float']>;
  line1Projection?: Maybe<Scalars['Float']>;
  line5?: Maybe<Line5>;
  line6?: Maybe<Line6>;
  line7?: Maybe<Line7>;
  line8?: Maybe<Line8>;
  markdown?: Maybe<Markdown>;
  periodNumber?: Maybe<Scalars['Int']>;
  shrink?: Maybe<Shrink>;
  vsLY?: Maybe<VsLy>;
  vsProjection?: Maybe<VsProjection>;
};

export type PharmaDownloadUrlResponse = {
  __typename?: 'PharmaDownloadUrlResponse';
  downloadUrl?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  expiresAt?: Maybe<Scalars['String']>;
  isBFFProxied: Scalars['Boolean'];
  success: Scalars['Boolean'];
};

export type PharmaFileInfo = {
  __typename?: 'PharmaFileInfo';
  fileName: Scalars['String'];
  fullPath: Scalars['String'];
  lastModified: Scalars['String'];
  size: Scalars['Int'];
};

export type PharmaFileListResponse = {
  __typename?: 'PharmaFileListResponse';
  error?: Maybe<Scalars['String']>;
  files: Array<PharmaFileInfo>;
  success: Scalars['Boolean'];
};

export enum PharmaUploadDay {
  Friday = 'FRIDAY',
  Monday = 'MONDAY',
  Saturday = 'SATURDAY',
  Sunday = 'SUNDAY',
  Thursday = 'THURSDAY',
  Tuesday = 'TUESDAY',
  Wednesday = 'WEDNESDAY'
}

export type PharmaUploadDocumentsReq = {
  fetchAll?: InputMaybe<Scalars['Boolean']>;
  fiscalWeekNbr?: InputMaybe<Scalars['Int']>;
  user?: InputMaybe<Scalars['String']>;
};

export type QuarterData = {
  __typename?: 'QuarterData';
  actualOrForecast?: Maybe<Scalars['Float']>;
  bookGrossProfit?: Maybe<BookGrossProfit>;
  id?: Maybe<Scalars['String']>;
  idPercentage?: Maybe<Scalars['Float']>;
  lastYear?: Maybe<Scalars['Float']>;
  line1Projection?: Maybe<Scalars['Float']>;
  line5?: Maybe<Line5>;
  line6?: Maybe<Line6>;
  line7?: Maybe<Line7>;
  line8?: Maybe<Line8>;
  markdown?: Maybe<Markdown>;
  quarterNumber?: Maybe<Scalars['Int']>;
  shrink?: Maybe<Shrink>;
  vsLY?: Maybe<VsLy>;
  vsProjection?: Maybe<VsProjection>;
};

export type Query = {
  __typename?: 'Query';
  GetEnvVariables: EnvVariablesResponse;
  divisionBannerUsersUserMgmt: DivisionBannerUsersUserMgmtResponse;
  generatePharmacyFileDownloadUrl: PharmaDownloadUrlResponse;
  getAdjustmentWorksheetData?: Maybe<AdjustmentWorksheetRes>;
  getAllTimeRangeInYear?: Maybe<Array<Maybe<RangeData>>>;
  getAllocatrDashboardTableData?: Maybe<AllocatrDashboardRes>;
  getDisplayDate?: Maybe<DisplayDateRes>;
  getForecastChangeLog?: Maybe<Array<Maybe<ForecastChangeLog>>>;
  getJobRunsFromDatabricks?: Maybe<DatabricksJobRunsResult>;
  getUploadedDocuments?: Maybe<GetUploadedDocumentsRes>;
  getWorkSheetFilter?: Maybe<WorkSheetFilterRes>;
  listPharmacyFiles: PharmaFileListResponse;
  saveAdjustment?: Maybe<SaveAdjustmentResponse>;
  testADConnection?: Maybe<Scalars['JSON']>;
  userInfo?: Maybe<UserDetails>;
  userManagementUserInfo: UserManagementUserInfoResponse;
};


export type QueryDivisionBannerUsersUserMgmtArgs = {
  input: DivisionBannerUsersUserMgmtInput;
};


export type QueryGeneratePharmacyFileDownloadUrlArgs = {
  expiryMinutes?: InputMaybe<Scalars['Int']>;
  fileName: Scalars['String'];
};


export type QueryGetAdjustmentWorksheetDataArgs = {
  adjustmentWorksheetReq?: InputMaybe<AdjustmentWorksheetReq>;
};


export type QueryGetAllTimeRangeInYearArgs = {
  timeRangeInYearReq?: InputMaybe<TimeRangeInYearReq>;
};


export type QueryGetAllocatrDashboardTableDataArgs = {
  allocatrDashboardReq?: InputMaybe<AllocatrDashboardReq>;
};


export type QueryGetDisplayDateArgs = {
  displayDateReq?: InputMaybe<DisplayDateReq>;
};


export type QueryGetForecastChangeLogArgs = {
  forecastChangeLogReqest?: InputMaybe<ForecastChangeLogReq>;
};


export type QueryGetJobRunsFromDatabricksArgs = {
  input: FetchJobRunsInput;
};


export type QueryGetUploadedDocumentsArgs = {
  getUploadedDocuments?: InputMaybe<PharmaUploadDocumentsReq>;
};


export type QueryGetWorkSheetFilterArgs = {
  workSheetFilterRequest?: InputMaybe<WorkSheetFilterReq>;
};


export type QuerySaveAdjustmentArgs = {
  adjustment?: InputMaybe<Adjustment>;
};


export type QueryUserManagementUserInfoArgs = {
  input: UserManagementUserInfoInput;
};

export type RangeData = {
  __typename?: 'RangeData';
  createTimestamp?: Maybe<Scalars['String']>;
  fiscalPeriodEndDate?: Maybe<Scalars['String']>;
  fiscalPeriodNumber?: Maybe<Scalars['Int']>;
  fiscalPeriodStartDate?: Maybe<Scalars['String']>;
  fiscalQuarterEndDate?: Maybe<Scalars['String']>;
  fiscalQuarterNumber?: Maybe<Scalars['Int']>;
  fiscalQuarterStartDate?: Maybe<Scalars['String']>;
  fiscalWeekEndDate?: Maybe<Scalars['String']>;
  fiscalWeekNumber?: Maybe<Scalars['Int']>;
  fiscalWeekStartDate?: Maybe<Scalars['String']>;
  fiscalYearNumber?: Maybe<Scalars['Int']>;
  updateTimestamp?: Maybe<Scalars['String']>;
};

export type SaveAdjustmentResponse = {
  __typename?: 'SaveAdjustmentResponse';
  message?: Maybe<Scalars['String']>;
  success?: Maybe<Scalars['Boolean']>;
};

export type ScheduledTimeInfo = {
  __typename?: 'ScheduledTimeInfo';
  Friday?: Maybe<Scalars['String']>;
  Monday?: Maybe<Scalars['String']>;
  Thursday?: Maybe<Scalars['String']>;
};

export type Shrink = {
  __typename?: 'Shrink';
  actualOrForecast?: Maybe<Scalars['Float']>;
  percentActualOrForecast?: Maybe<Scalars['Float']>;
  projectionPct?: Maybe<Scalars['Float']>;
  projectionValue?: Maybe<Scalars['Float']>;
  vsProjection?: Maybe<Scalars['Float']>;
};

export type SimplifiedRunInfo = {
  __typename?: 'SimplifiedRunInfo';
  date?: Maybe<Scalars['String']>;
  time?: Maybe<Scalars['String']>;
  weekNumber?: Maybe<Scalars['Int']>;
};

export type SyncDayInfo = {
  __typename?: 'SyncDayInfo';
  sync_date?: Maybe<Scalars['String']>;
  sync_day?: Maybe<Scalars['String']>;
};

export type SyncDays = {
  __typename?: 'SyncDays';
  sync_days?: Maybe<Array<Maybe<SyncDayInfo>>>;
};

export type SyncHistory = {
  __typename?: 'SyncHistory';
  weeks?: Maybe<Array<Maybe<WeekInfo>>>;
};

export type SyncSession = {
  __typename?: 'SyncSession';
  date?: Maybe<Scalars['String']>;
  day?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['String']>;
  time?: Maybe<Scalars['String']>;
};

export type TimeRangeInYearReq = {
  fiscalYearNumber?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  level?: InputMaybe<Scalars['String']>;
};

export type UpdatedMetrics = {
  __typename?: 'UpdatedMetrics';
  adjustedFields?: Maybe<Array<Maybe<AdjustedFields>>>;
  bannerId?: Maybe<Array<Maybe<Scalars['String']>>>;
  comment?: Maybe<Scalars['String']>;
  fiscalWeekNbrs?: Maybe<Scalars['String']>;
  keyAttributeName?: Maybe<Scalars['String']>;
  keyAttributeValue?: Maybe<Scalars['String']>;
  reason?: Maybe<Scalars['String']>;
};

export type UploadedDocument = {
  __typename?: 'UploadedDocument';
  fileContent?: Maybe<Scalars['String']>;
  fileName?: Maybe<Scalars['String']>;
  files?: Maybe<Scalars['String']>;
  updatedTs?: Maybe<Scalars['String']>;
};

export type UserDetails = {
  __typename?: 'UserDetails';
  userEmail?: Maybe<Scalars['String']>;
  userId?: Maybe<Scalars['String']>;
  userName?: Maybe<Scalars['String']>;
  userPermissions?: Maybe<UserPermissions>;
  userRole?: Maybe<Scalars['String']>;
};

export type UserManagementUserInfoAssignment = {
  __typename?: 'UserManagementUserInfoAssignment';
  bannerIds: Array<Scalars['String']>;
  divisionId: Scalars['String'];
  divisionName: Scalars['String'];
  effectiveEndDate?: Maybe<Scalars['String']>;
  effectiveStartDate: Scalars['String'];
  updateTs: Scalars['String'];
  updatedBy: Scalars['String'];
};

export type UserManagementUserInfoInput = {
  userId: Scalars['String'];
};

export type UserManagementUserInfoProfile = {
  __typename?: 'UserManagementUserInfoProfile';
  active: Scalars['Boolean'];
  ldapId?: Maybe<Scalars['String']>;
  managerEmail?: Maybe<Scalars['String']>;
  managerName?: Maybe<Scalars['String']>;
  superUser: Scalars['Boolean'];
  userId: Scalars['String'];
  userName: Scalars['String'];
  userRole: Scalars['String'];
};

export type UserManagementUserInfoResponse = {
  __typename?: 'UserManagementUserInfoResponse';
  data: Array<UserManagementUserInfoAssignment>;
  message: Scalars['String'];
  timestamp: Scalars['String'];
  userInfo: UserManagementUserInfoProfile;
};

export type UserPermissions = {
  __typename?: 'UserPermissions';
  canEdit?: Maybe<Scalars['Boolean']>;
  canView?: Maybe<Scalars['Boolean']>;
};

export type VsLy = {
  __typename?: 'VsLY';
  percentage?: Maybe<Scalars['Float']>;
  value?: Maybe<Scalars['Float']>;
};

export type VsProjection = {
  __typename?: 'VsProjection';
  percentage?: Maybe<Scalars['Float']>;
  value?: Maybe<Scalars['Float']>;
};

export type Week = {
  comment?: InputMaybe<Scalars['String']>;
  editedColumns?: InputMaybe<Scalars['String']>;
  fiscalWeekNbr?: InputMaybe<Scalars['Int']>;
  newAggregatedData?: InputMaybe<AdjustmentData>;
  previousAggregatedData?: InputMaybe<AdjustmentData>;
  reason?: InputMaybe<Scalars['String']>;
};

export type WeekInfo = {
  __typename?: 'WeekInfo';
  lastRun?: Maybe<SimplifiedRunInfo>;
};

export type WeekRunInfo = {
  __typename?: 'WeekRunInfo';
  date?: Maybe<Scalars['String']>;
  run_id?: Maybe<Scalars['BigInt']>;
  status?: Maybe<Scalars['String']>;
  time?: Maybe<Scalars['String']>;
  weekNumber?: Maybe<Scalars['Int']>;
};

export type WorkSheetFilterReq = {
  userId?: InputMaybe<Scalars['String']>;
};

export type WorkSheetFilterRes = {
  __typename?: 'WorkSheetFilterRes';
  createdBy?: Maybe<Scalars['String']>;
  createdTimeStamp?: Maybe<Scalars['String']>;
  smicData?: Maybe<Array<Maybe<SmicDataDetails>>>;
  updatedBy?: Maybe<Scalars['String']>;
  updatedTimeStamp?: Maybe<Scalars['String']>;
  userEmail?: Maybe<Scalars['String']>;
  userId?: Maybe<Scalars['String']>;
  userName?: Maybe<Scalars['String']>;
  userRole?: Maybe<Scalars['String']>;
};

export type LatestWeek = {
  name?: InputMaybe<Scalars['String']>;
  num?: InputMaybe<Scalars['Int']>;
  value?: InputMaybe<Scalars['String']>;
  weekNumber?: InputMaybe<Scalars['Int']>;
};

export type NonAggregatedForecastData = {
  __typename?: 'nonAggregatedForecastData';
  bannerId?: Maybe<Array<Maybe<Scalars['String']>>>;
  comment?: Maybe<Scalars['String']>;
  createdBy?: Maybe<Scalars['String']>;
  createdTs?: Maybe<Scalars['String']>;
  deptId?: Maybe<Scalars['Int']>;
  divisionId?: Maybe<Scalars['Int']>;
  fiscalPeriodNbr?: Maybe<Scalars['Int']>;
  fiscalQuarterNbr?: Maybe<Scalars['Int']>;
  fiscalWeekNbr?: Maybe<Scalars['Int']>;
  fiscalYearNbr?: Maybe<Scalars['Int']>;
  forecastType?: Maybe<Scalars['String']>;
  lastUpdatedUserRole?: Maybe<Scalars['String']>;
  line1BillOutGrossPct?: Maybe<Scalars['Float']>;
  line1PublicToSalesNbr?: Maybe<Scalars['Float']>;
  line4CostOfSalesNbr?: Maybe<Scalars['Float']>;
  line5BookGrossProfitNbr?: Maybe<Scalars['Float']>;
  line5BookGrossProfitPct?: Maybe<Scalars['Float']>;
  line5MarkDownsNbr?: Maybe<Scalars['Float']>;
  line5MarkDownsPct?: Maybe<Scalars['Float']>;
  line5RealGrossProfitNbr?: Maybe<Scalars['Float']>;
  line5RealGrossProfitPct?: Maybe<Scalars['Float']>;
  line5ShrinkNbr?: Maybe<Scalars['Float']>;
  line5ShrinkPct?: Maybe<Scalars['Float']>;
  line6SuppliesPackagingNbr?: Maybe<Scalars['Float']>;
  line7RetailsAllowancesNbr?: Maybe<Scalars['Float']>;
  line7RetailsAllowancesPct?: Maybe<Scalars['Float']>;
  line7RetailsNonSellingAllowancesNbr?: Maybe<Scalars['Float']>;
  line7RetailsNonSellingAllowancesPct?: Maybe<Scalars['Float']>;
  line7RetailsSellingAllowancesNbr?: Maybe<Scalars['Float']>;
  line7RetailsSellingAllowancesPct?: Maybe<Scalars['Float']>;
  line8RealGrossProfitNbr?: Maybe<Scalars['Float']>;
  line8RealGrossProfitPct?: Maybe<Scalars['Float']>;
  reason?: Maybe<Scalars['String']>;
  smicCategoryId?: Maybe<Scalars['Int']>;
  sourceTs?: Maybe<Scalars['String']>;
  state?: Maybe<Scalars['String']>;
  updatedBy?: Maybe<Scalars['String']>;
  updatedTs?: Maybe<Scalars['String']>;
  versionNbr?: Maybe<Scalars['Int']>;
};

export type SmicDataDetails = {
  __typename?: 'smicDataDetails';
  bannerId?: Maybe<Scalars['String']>;
  bannerName?: Maybe<Scalars['String']>;
  deptId?: Maybe<Scalars['String']>;
  deptName?: Maybe<Scalars['String']>;
  deskId?: Maybe<Scalars['String']>;
  deskName?: Maybe<Scalars['String']>;
  divisionId?: Maybe<Scalars['String']>;
  divisionName?: Maybe<Scalars['String']>;
  retailSectionCd?: Maybe<Scalars['String']>;
  retailSectionName?: Maybe<Scalars['String']>;
  smicCategoryCd?: Maybe<Scalars['Int']>;
  smicCategoryDesc?: Maybe<Scalars['String']>;
  smicCategoryId?: Maybe<Scalars['Int']>;
  smicGroupCd?: Maybe<Scalars['Int']>;
  smicGroupDesc?: Maybe<Scalars['String']>;
};

export type WeekData = {
  __typename?: 'weekData';
  actualOrForecast?: Maybe<Scalars['Float']>;
  bookGrossProfit?: Maybe<BookGrossProfit>;
  id?: Maybe<Scalars['String']>;
  idPercentage?: Maybe<Scalars['Float']>;
  isActualUsed?: Maybe<Scalars['Boolean']>;
  lastYear?: Maybe<Scalars['Float']>;
  line1Projection?: Maybe<Scalars['Float']>;
  line5?: Maybe<Line5>;
  line6?: Maybe<Line6>;
  line7?: Maybe<Line7>;
  line8?: Maybe<Line8>;
  markdown?: Maybe<Markdown>;
  periodNumber?: Maybe<Scalars['Int']>;
  shrink?: Maybe<Shrink>;
  vsLY?: Maybe<VsLy>;
  vsProjection?: Maybe<VsProjection>;
  weekNumber?: Maybe<Scalars['Int']>;
};
