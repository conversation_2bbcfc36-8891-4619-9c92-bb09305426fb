import { PharmaUploadService } from "./pharmaUpload.service";
import axios from "axios";
import {
    pharmacyContainerClient,
    isConnectionAvailable
} from "../../pharmaUpload/Connection";

jest.mock("axios");
jest.mock("../../pharmaUpload/Connection", () => ({
    pharmacyContainerClient: {
        getBlobClient: jest.fn()
    },
    isConnectionAvailable: jest.fn().mockReturnValue(true)
}));

describe("PharmaUploadService", () => {
    let service: PharmaUploadService;

    beforeEach(() => {
        service = new PharmaUploadService();
        jest.clearAllMocks();
    });

    describe("getUploadedDocuments", () => {
        it("should fetch jobs and return uploaded documents with file content", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [
                        { fileName: "file1.txt" },
                        { fileName: "file2.txt" },
                        { fileName: "file1.txt" }
                    ]
                }
            });

            const mockDownloadToBuffer = jest
                .fn()
                .mockResolvedValue(Buffer.from("test-content"));
            const mockExists = jest.fn().mockResolvedValue(true);
            const mockGetBlobClient = jest.fn().mockImplementation(() => ({
                exists: mockExists,
                downloadToBuffer: mockDownloadToBuffer
            }));

            (pharmacyContainerClient.getBlobClient as jest.Mock).mockImplementation(
                mockGetBlobClient
            );

            const result = await service.getUploadedDocuments(
                "user1",
                "202401",
                true
            );

            expect(axios.post).toHaveBeenCalled();
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith(
                "pharmacy/file1.txt"
            );
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith(
                "pharmacy/file2.txt"
            );
            expect(mockDownloadToBuffer).toHaveBeenCalled();
            expect(result.uploadedDocuments).toBeDefined();
            expect(Array.isArray(result.uploadedDocuments)).toBe(true);
            expect(
                result.uploadedDocuments && result.uploadedDocuments.length
            ).toBe(2);

            if (
                !result.uploadedDocuments ||
                result.uploadedDocuments.length === 0
            ) {
                throw new Error("uploadedDocuments is null or empty");
                return;
            }
            expect(result.uploadedDocuments[0]!.fileName).toBe("file1.txt");
            expect(result.uploadedDocuments[0]!.fileContent).toBe(
                Buffer.from("test-content").toString("base64")
            );
        });

        it("should skip files that do not exist in blob storage", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [{ fileName: "file1.txt" }, { fileName: "file2.txt" }]
                }
            });

            const mockExists = jest
                .fn()
                .mockResolvedValueOnce(false)
                .mockResolvedValueOnce(true);
            const mockDownloadToBuffer = jest
                .fn()
                .mockResolvedValue(Buffer.from("abc"));
            const mockGetBlobClient = jest.fn().mockImplementation(() => ({
                exists: mockExists,
                downloadToBuffer: mockDownloadToBuffer
            }));

            (pharmacyContainerClient.getBlobClient as jest.Mock).mockImplementation(
                mockGetBlobClient
            );

            const result = await service.getUploadedDocuments(
                "user2",
                "202402",
                false
            );

            expect(result.uploadedDocuments).toBeDefined();
            expect(Array.isArray(result.uploadedDocuments)).toBe(true);
            expect(
                result.uploadedDocuments && result.uploadedDocuments.length
            ).toBe(1);

            if (
                !result.uploadedDocuments ||
                result.uploadedDocuments.length === 0
            ) {
                throw new Error("uploadedDocuments is null or empty");
                return;
            }
            expect(result.uploadedDocuments[0]!.fileName).toBe("file2.txt");
        });

        it("should return empty uploadedDocuments if no jobs returned", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: { data: [] }
            });

            const result = await service.getUploadedDocuments(
                "user3",
                "202403",
                false
            );

            expect(result.uploadedDocuments).toEqual([]);
        });

        it("should throw error if axios fails", async () => {
            (axios.post as jest.Mock).mockRejectedValue(new Error("Network error"));

            await expect(
                service.getUploadedDocuments("user4", "202404", false)
            ).rejects.toThrow("Network error");
        });

        it("should handle null response data", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: { data: null }
            });

            const result = await service.getUploadedDocuments(
                "user5",
                "202405",
                true
            );

            expect(result.uploadedDocuments).toEqual([]);
        });

        it("should handle undefined response data", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: { data: undefined }
            });

            const result = await service.getUploadedDocuments(
                "user6",
                "202406",
                false
            );

            expect(result.uploadedDocuments).toEqual([]);
        });

        it("should handle response without data property", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: {}
            });

            const result = await service.getUploadedDocuments(
                "user7",
                "202407",
                true
            );

            expect(result.uploadedDocuments).toEqual([]);
        });

        it("should handle blob download failures gracefully", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [{ fileName: "corrupted.txt" }]
                }
            });

            const mockExists = jest.fn().mockResolvedValue(true);
            const mockDownloadToBuffer = jest
                .fn()
                .mockRejectedValue(new Error("Blob download failed"));
            const mockGetBlobClient = jest.fn().mockImplementation(() => ({
                exists: mockExists,
                downloadToBuffer: mockDownloadToBuffer
            }));

            (pharmacyContainerClient.getBlobClient as jest.Mock).mockImplementation(
                mockGetBlobClient
            );

            const result = await service.getUploadedDocuments(
                "user8",
                "202408",
                true
            );

            // Should skip files that fail to download
            expect(result.uploadedDocuments).toEqual([]);
        });

        it("should handle blob exists check failures", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [{ fileName: "exists-check-fail.txt" }]
                }
            });

            const mockExists = jest.fn().mockRejectedValue(new Error("Exists check failed"));
            const mockGetBlobClient = jest.fn().mockImplementation(() => ({
                exists: mockExists,
                downloadToBuffer: jest.fn()
            }));

            (pharmacyContainerClient.getBlobClient as jest.Mock).mockImplementation(
                mockGetBlobClient
            );

            const result = await service.getUploadedDocuments(
                "user9",
                "202409",
                false
            );

            // Should skip files where exists check fails
            expect(result.uploadedDocuments).toEqual([]);
        });

        it("should handle different fiscal year formats", async () => {
            const testCases = ["2024", "202401", "2024Q1", "24"];

            for (const fiscalYear of testCases) {
                jest.clearAllMocks();

                (axios.post as jest.Mock).mockResolvedValue({
                    data: { data: [] }
                });

                const result = await service.getUploadedDocuments(
                    "test-user",
                    fiscalYear,
                    true
                );

                expect(axios.post).toHaveBeenCalled();
                expect(result.uploadedDocuments).toEqual([]);
            }
        });

        it("should handle special characters in user names", async () => {
            const specialUsers = ["<EMAIL>", "user-name_123", "用户", "joão"];

            for (const user of specialUsers) {
                jest.clearAllMocks();

                (axios.post as jest.Mock).mockResolvedValue({
                    data: { data: [] }
                });

                const result = await service.getUploadedDocuments(
                    user,
                    "202401",
                    false
                );

                expect(axios.post).toHaveBeenCalled();
                expect(result.uploadedDocuments).toEqual([]);
            }
        });

        it("should handle files with special characters in names", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [
                        { fileName: "файл-тест.xlsx" },
                        { fileName: "file with spaces.csv" },
                        { fileName: "file-with-éaccents.pdf" }
                    ]
                }
            });

            const mockDownloadToBuffer = jest
                .fn()
                .mockResolvedValue(Buffer.from("content"));
            const mockExists = jest.fn().mockResolvedValue(true);
            const mockGetBlobClient = jest.fn().mockImplementation(() => ({
                exists: mockExists,
                downloadToBuffer: mockDownloadToBuffer
            }));

            (pharmacyContainerClient.getBlobClient as jest.Mock).mockImplementation(
                mockGetBlobClient
            );

            const result = await service.getUploadedDocuments(
                "user",
                "202401",
                true
            );

            expect(result.uploadedDocuments).toBeDefined();
            expect(result.uploadedDocuments!.length).toBe(3);
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith("pharmacy/файл-тест.xlsx");
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith("pharmacy/file with spaces.csv");
            expect(pharmacyContainerClient.getBlobClient).toHaveBeenCalledWith("pharmacy/file-with-éaccents.pdf");
        });

        it("should handle large file content in base64 encoding", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [{ fileName: "large-file.xlsx" }]
                }
            });

            // Create a large buffer (1MB)
            const largeBuffer = Buffer.alloc(1024 * 1024, 'A');

            const mockDownloadToBuffer = jest
                .fn()
                .mockResolvedValue(largeBuffer);
            const mockExists = jest.fn().mockResolvedValue(true);
            const mockGetBlobClient = jest.fn().mockImplementation(() => ({
                exists: mockExists,
                downloadToBuffer: mockDownloadToBuffer
            }));

            (pharmacyContainerClient.getBlobClient as jest.Mock).mockImplementation(
                mockGetBlobClient
            );

            const result = await service.getUploadedDocuments(
                "user",
                "202401",
                true
            );

            expect(result.uploadedDocuments).toBeDefined();
            expect(result.uploadedDocuments!.length).toBe(1);
            expect(result.uploadedDocuments![0]!.fileContent).toBe(largeBuffer.toString("base64"));
        });

        it("should handle empty buffer downloads", async () => {
            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [{ fileName: "empty-file.txt" }]
                }
            });

            const mockDownloadToBuffer = jest
                .fn()
                .mockResolvedValue(Buffer.alloc(0));
            const mockExists = jest.fn().mockResolvedValue(true);
            const mockGetBlobClient = jest.fn().mockImplementation(() => ({
                exists: mockExists,
                downloadToBuffer: mockDownloadToBuffer
            }));

            (pharmacyContainerClient.getBlobClient as jest.Mock).mockImplementation(
                mockGetBlobClient
            );

            const result = await service.getUploadedDocuments(
                "user",
                "202401",
                true
            );

            expect(result.uploadedDocuments).toBeDefined();
            expect(result.uploadedDocuments!.length).toBe(1);
            expect(result.uploadedDocuments![0]!.fileContent).toBe("");
        });

        it("should handle connection not available", async () => {
            // Override the mock for this test
            (isConnectionAvailable as jest.Mock).mockReturnValue(false);

            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [{ fileName: "test.txt" }]
                }
            });

            await expect(
                service.getUploadedDocuments("user", "202401", true)
            ).rejects.toThrow("Azure Blob Storage connection is not available. Please check environment variables.");

            expect(axios.post).toHaveBeenCalled();

            // Reset the mock back to true for other tests
            (isConnectionAvailable as jest.Mock).mockReturnValue(true);
        }); it("should handle concurrent requests", async () => {
            // Ensure connection is available for this test
            (isConnectionAvailable as jest.Mock).mockReturnValue(true);

            (axios.post as jest.Mock).mockResolvedValue({
                data: {
                    data: [{ fileName: "concurrent-test.txt" }]
                }
            });

            const mockDownloadToBuffer = jest
                .fn()
                .mockResolvedValue(Buffer.from("concurrent-content"));
            const mockExists = jest.fn().mockResolvedValue(true);
            const mockGetBlobClient = jest.fn().mockImplementation(() => ({
                exists: mockExists,
                downloadToBuffer: mockDownloadToBuffer
            }));

            (pharmacyContainerClient.getBlobClient as jest.Mock).mockImplementation(
                mockGetBlobClient
            );

            // Execute multiple concurrent requests
            const promises = [
                service.getUploadedDocuments("user1", "202401", true),
                service.getUploadedDocuments("user2", "202402", false),
                service.getUploadedDocuments("user3", "202403", true)
            ];

            const results = await Promise.all(promises);

            results.forEach(result => {
                expect(result.uploadedDocuments).toBeDefined();
                expect(result.uploadedDocuments!.length).toBe(1);
            });

            expect(axios.post).toHaveBeenCalledTimes(3);
        });
    });
});
