import { ClientSecretCredential } from "@azure/identity";
import { BlobServiceClient } from "@azure/storage-blob";
import * as dotenv from 'dotenv';

// Load environment variables from .env.development file
dotenv.config({ path: '.env.development' });

// const accountName = process.env.AZURE_BLOB_STORAGE_ACCOUNT_NAME;
// const containerName = process.env.AZURE_BLOB_STORAGE_CONTAINER_NAME;

// const clientId = process.env.DATABRICKS_CLIENT_ID;
// const clientSecret = process.env.AZURE_CLIENT_SECRET;
// const tenantId = process.env.DATABRICKS_TENANT_ID;

// if (!accountName || !containerName || !clientId || !clientSecret || !tenantId) {
//     console.warn("Missing required Azure environment variables. Connection will not be available.");
    // console.log("Required variables:", {
    //     AZURE_BLOB_STORAGE_ACCOUNT_NAME: !!accountName,
    //     AZURE_BLOB_STORAGE_CONTAINER_NAME: !!containerName,
    //     AZURE_CLIENT_ID: !!clientId,
    //     AZURE_CLIENT_SECRET: !!clientSecret,
    //     AZURE_TENANT_ID: !!tenantId
    // });
// }



const accountName = process.env.AZURE_BLOB_STORAGE_ACCOUNT_NAME || "menfptdevdevst01";
const containerName = process.env.AZURE_BLOB_STORAGE_CONTAINER_NAME || "menfpt";
// const containerName = process.env.AZURE_STORAGE_PHARMACY_CONTAINER_NAME || "pharmacy";
 
const clientId = process.env.AZURE_CLIENT_ID || "f4a0ffa1-397e-437e-99c5-d65d787ce4ba";
const clientSecret = process.env.AZURE_CLIENT_SECRET || "i6oEa0uUalOxt62FJfuYx8TsprZlMequBx17x7C+iOs=";
const tenantId = process.env.AZURE_TENANT_ID || "b7f604a0-00a9-4188-9248-42f3a5aac2e9";


// Create credential and clients only if all environment variables are available
let pharmacyContainerClient: any = null;

if (accountName && containerName && clientId && clientSecret && tenantId) {
    // Create credential
    const credential = new ClientSecretCredential(tenantId, clientId, clientSecret);

    // Create BlobServiceClient
    const blobServiceClient = new BlobServiceClient(
        `https://${accountName}.blob.core.windows.net`,
        credential
    );
    // Get container client
    pharmacyContainerClient = blobServiceClient.getContainerClient(containerName);
}

export { pharmacyContainerClient };

/**
 * Checks if the pharmacy container client is available
 */
export function isConnectionAvailable(): boolean {
    return pharmacyContainerClient !== null;
}

/**
 * Gets the pharmacy folder prefix used in blob storage
 */
export function getPharmacyFolderPrefix(): string {
    return "pharmacy/";
}

// Example: check connection (optional)
export async function checkConnection() {
    try {
        if (!pharmacyContainerClient) {
            return false;
        }
        const exists = await pharmacyContainerClient.exists();
        return exists;
    } catch (err) {
        console.error("Azure Blob connection error:", err);
        throw err;
    }
}
export async function listPharmacyBlobs() {
    if (!pharmacyContainerClient) {
        return;
    }

    const prefix = "pharmacy/";
    try {
        const blobs = [];
        for await (const blob of pharmacyContainerClient.listBlobsFlat({ prefix })) {
            blobs.push(blob.name);
        }
        console.log("Files in pharmacy blob:", blobs); // <-- Added log
        return blobs;
    } catch (err) {
        console.error("Error listing pharmacy blobs:", err);
        throw err;
    }
}
