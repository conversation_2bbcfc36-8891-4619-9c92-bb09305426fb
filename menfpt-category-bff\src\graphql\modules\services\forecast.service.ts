import { GraphQLError } from "graphql";
import { Injectable } from "graphql-modules";
import { MSP_DATA_GET_FAILURE } from "../../shared/constants/errorCodes";

import {
    Adjustment,
    ForecastChangeLogReq
} from "src/graphql/__generated__/gql-ts-types";
import { MSP_DATA_POST_FAILURE } from "../../shared/constants/errorCodes";
import { BaseAPIService } from "../../shared/classes/BaseAPI.service";

@Injectable()
export class ForecastService extends BaseAPIService {
    async getForecastChangeLog(payload: ForecastChangeLogReq): Promise<any> {
        let response;
        try {
            let res = await this.post(`fetch/auditHistory`, {
                body: payload
            });
            response = res.data;
            return response;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }

    async SaveAdjustment(payload: any): Promise<any> {
        let response;
        try {
            response = await this.post(`saveAdjustment`, {
                body: payload
            });
            return response;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }

    async CalculateAdjustment(payload: any): Promise<any> {
        let response;
        try {
            response = await this.post(`calculate`, {
                body: payload
            });
            return response;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }

    async SaveCategoryAdjustment(payload: any): Promise<any> {
        let response;
        try {
            response = await this.post(`categories/saveAdjustments`, {
                body: payload
            });
            return response;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }

    async UpdateForecastSnapshot(payload: any): Promise<any> {
        let response;
        try {
            response = await this.put(`forecast/update/status`, {
                body: payload
            });
            return response;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
    }


    async getWorksheetData(actualsReq: any): Promise<any> {
        let response;
        try {
            let url =
                actualsReq?.endPoint === "forecast"
                    ? `aggregate/search`
                    : `${actualsReq?.endPoint}/search`;
            response = await this.post(url, {
                body: actualsReq
            });
            response = response?.data?.data;
        } catch (error) {
            throw new GraphQLError("Error while fetching data", {
                extensions: {
                    errorType: MSP_DATA_POST_FAILURE,
                    ...this.createErrorPayload(error)
                }
            });
        }
        return response;
    }
}
