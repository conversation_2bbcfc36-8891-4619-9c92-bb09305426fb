import "reflect-metadata";
import { testkit, gql, MockedApplication } from "graphql-modules";
import { GraphQLError } from "graphql";
import { ForecastService } from "../forecast.service";
jest.mock("axios");
jest.mock("../../../shared/classes/BaseAPI.service");

const SaveAdjustmentResponse = {
    message: "Successfully saved forecast adjustment snapshot!",
    timestamp: "2025-04-02T23:37:42.629Z",
};

describe("Testing positive scenarios for Forecast Service", () => {
    let service: ForecastService;
    beforeEach(() => {
        service = new ForecastService();
    });

    describe("To test saveAdjustment", () => {
        it("should return saveAdjustment response", async () => {
            const response = {
                data: SaveAdjustmentResponse
            };
            (
                service.post as jest.MockedFunction<() => Promise<unknown>>
            ).mockResolvedValueOnce(response);

            const result: any = await service.SaveAdjustment({
                "deptId": "301",
                "divisionIds": [
                    "30"
                ],
                "smicCategoryIds": [
                    3425
                ],
                "updatedBy": "mshai15",
                "weeks": [
                    {
                        "fiscalWeekNbr": 202501,
                        "editedColumns": "salesToPublic|shrink",
                        "reason": "Extreme Weather",
                        "comment": "Reviewed",
                        "newAggregatedData": {
                            "line1PublicToSalesNbr": 550764.923,
                            "line1PublicToSalesPct": 0.34657,
                            "line5BookGrossProfitNbr": 579151.7687,
                            "line5BookGrossProfitPct": 0.2657,
                            "line5MarkDownsNbr": -362170.8836,
                            "line5MarkDownsPct": 0.1662,
                            "line5ShrinkNbr": -82974.9683,
                            "line5ShrinkPct": 0.0381,
                            "line6SuppliesPackagingNbr": 0,
                            "line6SuppliesPackagingPct": 0,
                            "line7RetailsAllowancesNbr": 255097.6657,
                            "line7RetailsAllowancesPct": 0.0381
                        },
                        "previousAggregatedData": {
                            "line1PublicToSalesNbr": 550764.923,
                            "line1PublicToSalesPct": 0.34657,
                            "line5BookGrossProfitNbr": 579151.7687,
                            "line5BookGrossProfitPct": 0.2657,
                            "line5MarkDownsNbr": -362170.8836,
                            "line5MarkDownsPct": 0.1662,
                            "line5ShrinkNbr": -82974.9683,
                            "line5ShrinkPct": 0.0381,
                            "line6SuppliesPackagingNbr": 0,
                            "line6SuppliesPackagingPct": 0,
                            "line7RetailsAllowancesNbr": 255097.6657,
                            "line7RetailsAllowancesPct": 0.0381
                        }
                    }
                ],
                "deskId": null
            });

            expect(result.data).toEqual(SaveAdjustmentResponse);
        });
    });
});
