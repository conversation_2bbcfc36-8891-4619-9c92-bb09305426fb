import { Router, Request, Response } from 'express';
import { PharmaDownloadService } from '../graphql/modules/pharmaDownload/services/pharmaDownload.service';

const router = Router();
const pharmaDownloadService = new PharmaDownloadService();

/**
 * GET /api/pharmacy/files
 * List all available pharmacy files
 */
router.get('/files', async (req: Request, res: Response) => {
    try {
        const files = await pharmaDownloadService.listPharmacyFiles();
        res.json({
            success: true,
            files: files,
            count: files.length
        });
    } catch (error) {
        console.error('Error listing pharmacy files:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        res.status(500).json({
            success: false,
            error: errorMessage
        });
    }
});

/**
 * GET /api/pharmacy/download/:fileName
 * Download a specific pharmacy file
 * Example: GET /api/pharmacy/download/Sample_Pharmacy_File.xlsx
 */
router.get('/download/:fileName', async (req: Request, res: Response) => {
    try {
        const fileName = req.params.fileName;

        if (!fileName) {
            return res.status(400).json({
                success: false,
                error: 'File name is required'
            });
        }

        // Download the file from blob storage
        const result = await pharmaDownloadService.downloadPharmacyFile(fileName);

        if (!result.success) {
            return res.status(404).json({
                success: false,
                error: result.error
            });
        }

        // Set appropriate headers for file download
        res.setHeader('Content-Type', result.contentType || 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${result.fileName}"`);
        res.setHeader('Content-Length', result.data?.length || 0);

        // Send the file data
        res.send(result.data);

    } catch (error) {
        console.error('Error downloading pharmacy file:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        res.status(500).json({
            success: false,
            error: errorMessage
        });
    }
});


export default router;
