import { loadFilesSync } from "@graphql-tools/load-files";
import { join } from "path";
import { createModule } from "graphql-modules";
import { PharmaUploadProvider } from "./providers/pharmaUpload.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));
const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));

const PharmaUploadModule = createModule({
    id: "PharmaUpload",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [PharmaUploadProvider]
});

export default PharmaUploadModule; 