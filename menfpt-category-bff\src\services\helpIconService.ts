const DEFAULT_HELP_PDF = "https://rxsafeway.sharepoint.com/sites/CorpPricingMerchandisingSupport/Shared%20Documents/Strategic%20Projects/Category%20Excellence/HelpDocumentation/helpDocument.pdf";


export const getHelpPdfUrl = (): { url: string; message: string } | null => {
  const pdfUrl = process.env.HELP_PDF_SHAREPOINT || DEFAULT_HELP_PDF;

  if (pdfUrl) {
    console.log("Returning SharePoint PDF URL");
    return { url: pdfUrl, message: "Success" };
  } else {
    console.error("SharePoint PDF URL not configured");
    return null;
  }
};