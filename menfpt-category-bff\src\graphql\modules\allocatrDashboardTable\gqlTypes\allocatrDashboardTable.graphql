input latestWeek {
    name: String
    num: Int
    value: String
    weekNumber: Int
}

input AllocatrDashboardReq {
    currentFiscalYearNbr: Int!
    quarterNbr: Int!
    smicCategoryIds: [String]
    deptIds: [String]
    divisionIds: [String]
    type: String
    latestReleaseWeek: latestWeek
    weekNumbers: [Int]
    periodNumbers: [Int]
    filteredWeekNumbers: [Int]
}

type VsLY {
    value: Float
    percentage: Float
}

type VsProjection {
    value: Float
    percentage: Float
}

type BookGrossProfit {
    projectionValue: Float
    projectionPct: Float
    actualOrForecast: Float
    percentActualOrForecast: Float
    vsProjection: Float
}

type Markdown {
    projectionValue: Float
    projectionPct: Float
    actualOrForecast: Float
    percentActualOrForecast: Float
    vsProjection: Float
}

type Shrink {
    projectionValue: Float
    projectionPct: Float
    actualOrForecast: Float
    percentActualOrForecast: Float
    vsProjection: Float
}

type Line5 {
    actualOrForecast: Float
    percentActualOrForecast: Float
    projectionValue: Float
    projectionPct: Float
    vsProjection: Float
    percentVsProjection: Float
}

type Line6 {
    projection: Float
    actualOrForecast: Float
    vsProjection: Float
}

type Line7 {
    projection: Float
    actualOrForecast: Float
    vsProjection: Float
}

type Line8 {
    actualOrForecast: Float
    percentActualOrForecast: Float
    projectionValue: Float
    projectionPct: Float
    vsProjection: Float
    percentVsProjection: Float
    
}

type QuarterData {
    id: String
    quarterNumber: Int
    line1Projection: Float
    lastYear: Float
    actualOrForecast: Float
    idPercentage: Float
    vsLY: VsLY
    vsProjection: VsProjection
    bookGrossProfit: BookGrossProfit
    markdown: Markdown
    shrink: Shrink
    line5: Line5
    line6: Line6
    line7: Line7
    line8: Line8
}

type PeriodData {
    id: String
    periodNumber: Int
    line1Projection: Float
    lastYear: Float
    actualOrForecast: Float
    idPercentage: Float
    vsLY: VsLY
    vsProjection: VsProjection
    bookGrossProfit: BookGrossProfit
    markdown: Markdown
    shrink: Shrink
    line5: Line5
    line6: Line6
    line7: Line7
    line8: Line8
}

type weekData {
    id: String
    weekNumber: Int
    periodNumber: Int
    line1Projection: Float
    lastYear: Float
    actualOrForecast: Float
    idPercentage: Float
    vsLY: VsLY
    vsProjection: VsProjection
    bookGrossProfit: BookGrossProfit
    markdown: Markdown
    shrink: Shrink
    line5: Line5
    line6: Line6
    line7: Line7
    line8: Line8
    isActualUsed: Boolean
}

type AllocatrDashboardResponseData {
    id: String
    name: String
    quarter: QuarterData
    weeks: [weekData]
    periods: [PeriodData]
}

type AllocatrDashboardRes {
    allocatrDashboardTableData: [AllocatrDashboardResponseData]
}

type Query {
    getAllocatrDashboardTableData(
        allocatrDashboardReq: AllocatrDashboardReq
    ): AllocatrDashboardRes
}
