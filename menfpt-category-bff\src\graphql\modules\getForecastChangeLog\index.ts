import { loadFilesSync } from "@graphql-tools/load-files";
import { createModule } from "graphql-modules";
import { join } from "path";
import { ForecastChangeLogProvider } from "./providers/forecastChangeLog.provider";

const typeDefs = loadFilesSync(join(__dirname, "./gqlTypes/*.(graphql)"));

const resolvers = loadFilesSync(join(__dirname, "./resolvers/index.(ts|js)"));
const ForecastChangeLogModule = createModule({
    id: "GetForecastChangeLog",
    dirname: __dirname,
    typeDefs: typeDefs,
    resolvers: resolvers,
    providers: [ForecastChangeLogProvider]
});
export default ForecastChangeLogModule;
