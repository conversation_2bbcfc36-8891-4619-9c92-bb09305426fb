module.exports = {
    parser: "@typescript-eslint/parser",
    plugins: ["prettier"],
    extends: ["prettier"],
    parserOptions: {
        ecmaVersion: "latest", // Allows the use of modern ECMAScript features
        sourceType: "module" // Allows for the use of imports
    },
    extends: ["plugin:@typescript-eslint/recommended"], // Uses the linting rules from @typescript-eslint/eslint-plugin
    rules: { "prettier/prettier": 2 },
    env: {
        node: true // Enable Node.js global variables
    },
    endOfLine: "off"
};
