import express, { Request, Response, NextFunction } from "express";
import request from "supertest";
import * as MenuItemController from "../controllers/MenuItemController";
import menuItems from "../resource/menuItem.json";

const app = express();
app.get("/menu-items", MenuItemController.getMenuItems);

// Error handler registration will be moved into the test block after route registration

describe("MenuItemRouter", () => {
    const originalPharmacyGroup = process.env.PHARMACY_AD_GROUP;
    afterEach(() => {
        jest.restoreAllMocks();
        // Remove all routes after each test to avoid duplicate route registration
        (app as any)._router.stack = (app as any)._router.stack.filter(
            (layer: any) => {
                return !(layer.route && layer.route.path === "/menu-items");
            }
        );
        // Re-register the default route for other tests
        app.get("/menu-items", MenuItemController.getMenuItems);
        // Remove any error handlers
        (app as any)._router.stack = (app as any)._router.stack.filter(
            (layer: any) => {
                return !(
                    layer.name === "bound dispatch" && layer.handle.length === 4
                );
            }
        );
    });

    afterAll(() => {
        if (originalPharmacyGroup !== undefined) {
            process.env.PHARMACY_AD_GROUP = originalPharmacyGroup;
        } else {
            delete process.env.PHARMACY_AD_GROUP;
        }
    });

    it("should return filtered menu items for non-pharmacy users (integration)", async () => {
        // Register error handler after route
        (app as any)._router.stack = (app as any)._router.stack.filter(
            (layer: any) => {
                return !(
                    layer.name === "bound dispatch" && layer.handle.length === 4
                );
            }
        );
        app.use(
            (err: Error, req: Request, res: Response, next: NextFunction) => {
                res.status(500).send("Internal Server Issue");
            }
        );

        const res = await request(app).get("/menu-items");
        expect(res.status).toBe(200);

        // Verify the response structure
        expect(res.body.menuItems).toBeDefined();
        expect(res.body.menuItems[0].contextId).toBe("category_excellence");

        // For non-pharmacy users (no JWT token), should see only Allocatr Insights at top level
        const subAppIds = res.body.menuItems[0].subApp.map((sa: any) => sa.id);
        expect(subAppIds).toContain("national_forecast_projection_tool");
        expect(subAppIds).not.toContain("nfpt_rx_forecast");
        const allocatrInsights = res.body.menuItems[0].subApp.find(
            (sa: any) => sa.id === "national_forecast_projection_tool"
        );
        const subMenuIds = allocatrInsights.subMenu.map((item: any) => item.id);
        expect(subMenuIds).toContain("nfpt_dashboard");
        expect(subMenuIds).toContain("nfpt_adjustment_worksheet");
        expect(subMenuIds).not.toContain("nfpt_rx_forecast");
    });

    it("should return pharmacy-only menu items when pharmacy token is provided", async () => {
        // Register error handler after route
        (app as any)._router.stack = (app as any)._router.stack.filter(
            (layer: any) => {
                return !(
                    layer.name === "bound dispatch" && layer.handle.length === 4
                );
            }
        );
        app.use(
            (err: Error, req: Request, res: Response, next: NextFunction) => {
                res.status(500).send("Internal Server Issue");
            }
        );

        // Mock JWT token for pharmacy user
        process.env.PHARMACY_AD_GROUP = "az-menfpt-prod-rxfinance";
        const pharmacyToken =
            "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJncm91cHMiOlsiYXotbWVuZnB0LXByb2QtcnhmaW5hbmNlIl19.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8";

        const res = await request(app)
            .get("/menu-items")
            .set("Authorization", pharmacyToken);

        expect(res.status).toBe(200);

        // Verify the response structure
        expect(res.body.menuItems).toBeDefined();
        expect(res.body.menuItems[0].contextId).toBe("category_excellence");

        // For pharmacy users, should only see Rx Forecast as a top-level subApp
        const subAppIds2 = res.body.menuItems[0].subApp.map((sa: any) => sa.id);
        expect(subAppIds2).toContain("nfpt_rx_forecast");
        expect(subAppIds2).not.toContain("national_forecast_projection_tool");
    });

    it("should handle errors and return 500", async () => {
        // Remove the default route and add a 3-arg handler for this test
        (app as any)._router.stack = (app as any)._router.stack.filter(
            (layer: any) => {
                return !(layer.route && layer.route.path === "/menu-items");
            }
        );
        app.get(
            "/menu-items",
            function (req: Request, res: Response, next: NextFunction) {
                next(new Error("fail"));
            }
        );
        // Register error handler after route
        (app as any)._router.stack = (app as any)._router.stack.filter(
            (layer: any) => {
                return !(
                    layer.name === "bound dispatch" && layer.handle.length === 4
                );
            }
        );
        app.use(
            (err: Error, req: Request, res: Response, next: NextFunction) => {
                res.status(500).send("Internal Server Issue");
            }
        );
        const res = await request(app).get("/menu-items");
        expect(res.status).toBe(500);
        expect(res.text).toBe("Internal Server Issue");
    });
});
