import { Injectable } from 'graphql-modules';
import { PharmaFileInfo, DownloadResult } from '../types/pharmaTypes';
import { listBlobFiles, findSimilarFileNames } from '../utils/blobUtils';
import { FileDownloadService } from './fileDownload.service';
import { UrlGenerationService } from './urlGeneration.service';

@Injectable()
export class PharmaDownloadService {
    private fileDownloadService: FileDownloadService;
    private urlGenerationService: UrlGenerationService;

    constructor() {
        this.fileDownloadService = new FileDownloadService();
        this.urlGenerationService = new UrlGenerationService();
    }

    /**
     * Lists all available pharmacy files in the blob storage
     */
    async listPharmacyFiles(): Promise<PharmaFileInfo[]> {
        return await listBlobFiles();
    }

    /**
     * Downloads a specific pharmacy file from blob storage
     */
    async downloadPharmacyFile(fileName: string): Promise<DownloadResult> {
        return await this.fileDownloadService.downloadFile(fileName);
    }

    /**
     * Generates a temporary download URL for a pharmacy file (SAS URL)
     * Falls back to BFF-proxied download URL when using Azure AD auth
     */
    async generateDownloadUrl(fileName: string, expiryMinutes: number = 60): Promise<string> {
        return await this.urlGenerationService.generateDownloadUrl(fileName, expiryMinutes);
    }

    /**
     * Find similar filenames for troubleshooting (public method for external use)
     */
    async findSimilarFiles(targetFileName: string): Promise<string[]> {
        return await findSimilarFileNames(targetFileName);
    }
}
