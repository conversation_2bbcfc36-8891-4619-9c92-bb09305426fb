enum PharmaUploadDay {
    MONDAY
    TUESDAY
    WEDNESDAY
    THURSDAY
    FRIDAY
    SATURDAY
    SUNDAY
}

scalar Upload

input PharmaUploadDocumentsReq {
  user: String
  fiscalWeekNbr: Int
  fetchAll: Boolean
}

type UploadedDocument {
  fileName: String
  fileContent: String
  files: String
  updatedTs: String
}

type GetUploadedDocumentsRes {
  uploadedDocuments: [UploadedDocument]
}

type Query {
  getUploadedDocuments(
    getUploadedDocuments: PharmaUploadDocumentsReq
  ): GetUploadedDocumentsRes
}
