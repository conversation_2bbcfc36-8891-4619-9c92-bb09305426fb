import { Request, Response } from "express";
import MenuItemService from "../services/MenuItemService";
import { extractJwtToken } from "../util/menuFilterUtils";

export const getMenuItems =  async (req: Request, res: Response) => {
    try {
        // Extract JWT token from request headers
        const token = extractJwtToken(req.headers);
        
        const menuItems = await MenuItemService.getMenuItems(token);
        res.json(menuItems);
    } catch (error) {
        res.status(500).send("Internal Server Issue");
    }
};
