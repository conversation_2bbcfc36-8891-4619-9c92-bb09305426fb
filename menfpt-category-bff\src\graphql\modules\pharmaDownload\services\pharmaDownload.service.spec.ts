import { PharmaDownloadService } from './pharmaDownload.service';
import { FileDownloadService } from './fileDownload.service';
import { UrlGenerationService } from './urlGeneration.service';
import { PharmaFileInfo, DownloadResult } from '../types/pharmaTypes';

// Mock the dependency modules
jest.mock('../utils/blobUtils', () => ({
    listBlobFiles: jest.fn(),
    findSimilarFileNames: jest.fn()
}));

jest.mock('./fileDownload.service');
jest.mock('./urlGeneration.service');

describe('PharmaDownloadService', () => {
    let service: PharmaDownloadService;
    let mockFileDownloadService: jest.Mocked<FileDownloadService>;
    let mockUrlGenerationService: jest.Mocked<UrlGenerationService>;
    let mockListBlobFiles: jest.Mock;
    let mockFindSimilarFileNames: jest.Mock;

    beforeEach(() => {
        // Get mocked functions
        const { listBlobFiles, findSimilarFileNames } = require('../utils/blobUtils');
        mockListBlobFiles = listBlobFiles as jest.Mock;
        mockFindSimilarFileNames = findSimilarFileNames as jest.Mock;

        // Create service instance
        service = new PharmaDownloadService();

        // Get the mocked service instances
        mockFileDownloadService = (service as any).fileDownloadService;
        mockUrlGenerationService = (service as any).urlGenerationService;

        // Reset mocks
        jest.clearAllMocks();
    });

    describe('constructor', () => {
        it('should initialize service dependencies', () => {
            expect(service).toBeInstanceOf(PharmaDownloadService);
            expect(mockFileDownloadService).toBeInstanceOf(FileDownloadService);
            expect(mockUrlGenerationService).toBeInstanceOf(UrlGenerationService);
        });
    });

    describe('listPharmacyFiles', () => {
        it('should return list of pharmacy files successfully', async () => {
            const mockFiles: PharmaFileInfo[] = [
                {
                    fileName: 'pharmacy_file_1.xlsx',
                    fullPath: 'pharmacy/pharmacy_file_1.xlsx',
                    size: 1024,
                    lastModified: new Date('2025-08-05T10:00:00Z')
                },
                {
                    fileName: 'pharmacy_file_2.xlsx',
                    fullPath: 'pharmacy/pharmacy_file_2.xlsx',
                    size: 2048,
                    lastModified: new Date('2025-08-05T11:00:00Z')
                }
            ];

            mockListBlobFiles.mockResolvedValue(mockFiles);

            const result = await service.listPharmacyFiles();

            expect(result).toEqual(mockFiles);
            expect(mockListBlobFiles).toHaveBeenCalledTimes(1);
        });

        it('should throw error when listBlobFiles fails', async () => {
            const errorMessage = 'Azure Blob Storage connection not available';
            mockListBlobFiles.mockRejectedValue(new Error(errorMessage));

            await expect(service.listPharmacyFiles()).rejects.toThrow(errorMessage);
            expect(mockListBlobFiles).toHaveBeenCalledTimes(1);
        });

        it('should return empty array when no files found', async () => {
            mockListBlobFiles.mockResolvedValue([]);

            const result = await service.listPharmacyFiles();

            expect(result).toEqual([]);
            expect(mockListBlobFiles).toHaveBeenCalledTimes(1);
        });
    });

    describe('downloadPharmacyFile', () => {
        const fileName = 'test_pharmacy_file.xlsx';

        it('should download file successfully', async () => {
            const mockDownloadResult: DownloadResult = {
                success: true,
                data: Buffer.from('file content'),
                fileName: fileName,
                contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };

            mockFileDownloadService.downloadFile.mockResolvedValue(mockDownloadResult);

            const result = await service.downloadPharmacyFile(fileName);

            expect(result).toEqual(mockDownloadResult);
            expect(mockFileDownloadService.downloadFile).toHaveBeenCalledWith(fileName);
            expect(mockFileDownloadService.downloadFile).toHaveBeenCalledTimes(1);
        });

        it('should return error when download fails', async () => {
            const mockDownloadResult: DownloadResult = {
                success: false,
                error: 'File not found in pharmacy folder'
            };

            mockFileDownloadService.downloadFile.mockResolvedValue(mockDownloadResult);

            const result = await service.downloadPharmacyFile(fileName);

            expect(result).toEqual(mockDownloadResult);
            expect(mockFileDownloadService.downloadFile).toHaveBeenCalledWith(fileName);
            expect(mockFileDownloadService.downloadFile).toHaveBeenCalledTimes(1);
        });

        it('should handle service throwing error', async () => {
            const errorMessage = 'Connection failed';
            mockFileDownloadService.downloadFile.mockRejectedValue(new Error(errorMessage));

            await expect(service.downloadPharmacyFile(fileName)).rejects.toThrow(errorMessage);
            expect(mockFileDownloadService.downloadFile).toHaveBeenCalledWith(fileName);
        });
    });

    describe('generateDownloadUrl', () => {
        const fileName = 'test_pharmacy_file.xlsx';

        it('should generate download URL with default expiry', async () => {
            const expectedUrl = 'https://example.blob.core.windows.net/container/pharmacy/test_pharmacy_file.xlsx?sv=...';
            mockUrlGenerationService.generateDownloadUrl.mockResolvedValue(expectedUrl);

            const result = await service.generateDownloadUrl(fileName);

            expect(result).toBe(expectedUrl);
            expect(mockUrlGenerationService.generateDownloadUrl).toHaveBeenCalledWith(fileName, 60);
            expect(mockUrlGenerationService.generateDownloadUrl).toHaveBeenCalledTimes(1);
        });

        it('should generate download URL with custom expiry', async () => {
            const customExpiry = 120;
            const expectedUrl = 'https://example.blob.core.windows.net/container/pharmacy/test_pharmacy_file.xlsx?sv=...';
            mockUrlGenerationService.generateDownloadUrl.mockResolvedValue(expectedUrl);

            const result = await service.generateDownloadUrl(fileName, customExpiry);

            expect(result).toBe(expectedUrl);
            expect(mockUrlGenerationService.generateDownloadUrl).toHaveBeenCalledWith(fileName, customExpiry);
            expect(mockUrlGenerationService.generateDownloadUrl).toHaveBeenCalledTimes(1);
        });

        it('should handle URL generation failure', async () => {
            const errorMessage = 'File not found in pharmacy folder';
            mockUrlGenerationService.generateDownloadUrl.mockRejectedValue(new Error(errorMessage));

            await expect(service.generateDownloadUrl(fileName)).rejects.toThrow(errorMessage);
            expect(mockUrlGenerationService.generateDownloadUrl).toHaveBeenCalledWith(fileName, 60);
        });

        it('should handle SAS URL fallback to BFF URL', async () => {
            const bffUrl = '/api/pharmacy/download/test_pharmacy_file.xlsx';
            mockUrlGenerationService.generateDownloadUrl.mockResolvedValue(bffUrl);

            const result = await service.generateDownloadUrl(fileName);

            expect(result).toBe(bffUrl);
            expect(mockUrlGenerationService.generateDownloadUrl).toHaveBeenCalledWith(fileName, 60);
        });
    });

    describe('findSimilarFiles', () => {
        const targetFileName = 'pharmacy_file_typo.xlsx';

        it('should find similar files successfully', async () => {
            const similarFiles = [
                'pharmacy_file_1.xlsx',
                'pharmacy_file_2.xlsx',
                'pharmacy_file_v2.xlsx'
            ];

            mockFindSimilarFileNames.mockResolvedValue(similarFiles);

            const result = await service.findSimilarFiles(targetFileName);

            expect(result).toEqual(similarFiles);
            expect(mockFindSimilarFileNames).toHaveBeenCalledWith(targetFileName);
            expect(mockFindSimilarFileNames).toHaveBeenCalledTimes(1);
        });

        it('should return empty array when no similar files found', async () => {
            mockFindSimilarFileNames.mockResolvedValue([]);

            const result = await service.findSimilarFiles(targetFileName);

            expect(result).toEqual([]);
            expect(mockFindSimilarFileNames).toHaveBeenCalledWith(targetFileName);
        });

        it('should handle find similar files error', async () => {
            const errorMessage = 'Connection error';
            mockFindSimilarFileNames.mockRejectedValue(new Error(errorMessage));

            await expect(service.findSimilarFiles(targetFileName)).rejects.toThrow(errorMessage);
            expect(mockFindSimilarFileNames).toHaveBeenCalledWith(targetFileName);
        });

        it('should return similar files when connection is not available', async () => {
            // findSimilarFileNames returns empty array when connection is not available
            mockFindSimilarFileNames.mockResolvedValue([]);

            const result = await service.findSimilarFiles(targetFileName);

            expect(result).toEqual([]);
            expect(mockFindSimilarFileNames).toHaveBeenCalledWith(targetFileName);
        });
    });

    describe('integration scenarios', () => {
        it('should handle multiple concurrent operations', async () => {
            const fileName1 = 'file1.xlsx';
            const fileName2 = 'file2.xlsx';

            const mockFiles: PharmaFileInfo[] = [
                {
                    fileName: fileName1,
                    fullPath: `pharmacy/${fileName1}`,
                    size: 1024,
                    lastModified: new Date()
                },
                {
                    fileName: fileName2,
                    fullPath: `pharmacy/${fileName2}`,
                    size: 2048,
                    lastModified: new Date()
                }
            ];

            const mockDownloadResult: DownloadResult = {
                success: true,
                data: Buffer.from('content'),
                fileName: fileName1,
                contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };

            const mockUrl = 'https://example.com/download-url';

            mockListBlobFiles.mockResolvedValue(mockFiles);
            mockFileDownloadService.downloadFile.mockResolvedValue(mockDownloadResult);
            mockUrlGenerationService.generateDownloadUrl.mockResolvedValue(mockUrl);

            // Execute multiple operations concurrently
            const [files, download, url] = await Promise.all([
                service.listPharmacyFiles(),
                service.downloadPharmacyFile(fileName1),
                service.generateDownloadUrl(fileName2)
            ]);

            expect(files).toEqual(mockFiles);
            expect(download).toEqual(mockDownloadResult);
            expect(url).toBe(mockUrl);

            expect(mockListBlobFiles).toHaveBeenCalledTimes(1);
            expect(mockFileDownloadService.downloadFile).toHaveBeenCalledWith(fileName1);
            expect(mockUrlGenerationService.generateDownloadUrl).toHaveBeenCalledWith(fileName2, 60);
        });

        it('should handle mixed success and failure scenarios', async () => {
            const fileName = 'test.xlsx';

            mockListBlobFiles.mockResolvedValue([]);
            mockFileDownloadService.downloadFile.mockResolvedValue({
                success: false,
                error: 'File not found'
            });

            const [files, download] = await Promise.all([
                service.listPharmacyFiles(),
                service.downloadPharmacyFile(fileName)
            ]);

            expect(files).toEqual([]);
            expect(download.success).toBe(false);
            expect(download.error).toBe('File not found');
        });
    });
});
