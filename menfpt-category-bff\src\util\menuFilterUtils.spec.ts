import {
    isPharmacyUser,
    filterMenuItemsByUserRole,
    extractJwtToken
} from "./menuFilterUtils";
import menuItems from "../resource/menuItem.json";

describe("menuFilterUtils", () => {
    const originalPharmacyGroup = process.env.PHARMACY_AD_GROUP;
    afterAll(() => {
        if (originalPharmacyGroup !== undefined) {
            process.env.PHARMACY_AD_GROUP = originalPharmacyGroup;
        } else {
            delete process.env.PHARMACY_AD_GROUP;
        }
    });

    describe("isPharmacyUser", () => {
        it("should return true when token contains the configured pharmacy group (prod)", () => {
            process.env.PHARMACY_AD_GROUP = "az-menfpt-prod-rxfinance";
            const pharmacyToken =
                "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJncm91cHMiOlsiYXotbWVuZnB0LXByb2QtcnhmaW5hbmNlIl19.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8";
            expect(isPharmacyUser(pharmacyToken)).toBe(true);
        });

        it("should return true when token contains the configured pharmacy group (non-prod)", () => {
            process.env.PHARMACY_AD_GROUP = "az-menfpt-nonprod-rxfinance";
            const nonProdPharmacyToken =
                "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJncm91cHMiOlsiYXotbWVuZnB0LW5vbnByb2QtcnhmaW5hbmNlIl19.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8";
            expect(isPharmacyUser(nonProdPharmacyToken)).toBe(true);
        });

        it("should return false when token does not contain the configured pharmacy group", () => {
            process.env.PHARMACY_AD_GROUP = "az-menfpt-prod-rxfinance";
            const nonPharmacyToken =
                "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJncm91cHMiOlsiYXotbWVtc3AtcHJvZC1vdGhlciJdfQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8";
            expect(isPharmacyUser(nonPharmacyToken)).toBe(false);
        });

        it("should return false for empty token", () => {
            process.env.PHARMACY_AD_GROUP = "az-menfpt-prod-rxfinance";
            expect(isPharmacyUser("")).toBe(false);
        });

        it("should return false for null/undefined token", () => {
            process.env.PHARMACY_AD_GROUP = "az-menfpt-prod-rxfinance";
            expect(isPharmacyUser(null as any)).toBe(false);
            expect(isPharmacyUser(undefined as any)).toBe(false);
        });

        it("should return false for invalid JWT token", () => {
            process.env.PHARMACY_AD_GROUP = "az-menfpt-prod-rxfinance";
            const invalidToken = "Bearer invalid.token.here";
            expect(isPharmacyUser(invalidToken)).toBe(false);
        });
    });

    describe("filterMenuItemsByUserRole", () => {
        it("should filter menu items for pharmacy users (both prod and non-prod)", () => {
            const result = filterMenuItemsByUserRole(menuItems, true);
            const subAppIds = result.menuItems[0].subApp.map(
                (sa: any) => sa.id
            );
            expect(subAppIds).toContain("nfpt_rx_forecast");
            expect(subAppIds).not.toContain(
                "national_forecast_projection_tool"
            );
        });

        it("should filter menu items for non-pharmacy users", () => {
            const result = filterMenuItemsByUserRole(menuItems, false);
            const subAppIds = result.menuItems[0].subApp.map(
                (sa: any) => sa.id
            );
            expect(subAppIds).toContain("national_forecast_projection_tool");
            expect(subAppIds).not.toContain("nfpt_rx_forecast");
            const allocatrInsights = result.menuItems[0].subApp.find(
                (sa: any) => sa.id === "national_forecast_projection_tool"
            );
            const subMenuIds = allocatrInsights.subMenu.map(
                (item: any) => item.id
            );
            expect(subMenuIds).toContain("nfpt_dashboard");
            expect(subMenuIds).toContain("nfpt_adjustment_worksheet");
            expect(subMenuIds).not.toContain("nfpt_rx_forecast");
        });

        it("should not modify original menu items data", () => {
            const originalMenuItems = JSON.parse(JSON.stringify(menuItems));
            filterMenuItemsByUserRole(menuItems, true);

            // Original menu items should remain unchanged
            expect(menuItems).toEqual(originalMenuItems);
        });
    });

    describe("extractJwtToken", () => {
        it("should extract token from authorization header", () => {
            const headers = { authorization: "Bearer test.token.here" };
            expect(extractJwtToken(headers)).toBe("Bearer test.token.here");
        });

        it("should extract token from Authorization header (capital A)", () => {
            const headers = { Authorization: "Bearer test.token.here" };
            expect(extractJwtToken(headers)).toBe("Bearer test.token.here");
        });

        it("should handle array authorization header", () => {
            const headers = { authorization: ["Bearer test.token.here"] };
            expect(extractJwtToken(headers)).toBe("Bearer test.token.here");
        });

        it("should return undefined when no authorization header", () => {
            const headers = {};
            expect(extractJwtToken(headers)).toBeUndefined();
        });

        it("should return empty string when authorization header is empty", () => {
            const headers = { authorization: "" };
            expect(extractJwtToken(headers)).toBe("");
        });
    });
});
