import { DataSource } from "apollo-datasource";
import * as msal from "@azure/msal-node";
import { Client } from "@microsoft/microsoft-graph-client";
import "isomorphic-fetch"; 
import { Injectable } from "graphql-modules";

@Injectable()
export class AdminProvider extends DataSource {
  /**
   * Recursively fetches all pages of data using the nextLink field
   * @param graphClient - Microsoft Graph client instance
   * @param initialUrl - The initial API endpoint URL
   * @param entityName - Name of the entity being fetched (for logging)
   * @returns Promise<any[]> - Array containing all items from all pages
   */
  private async fetchAllPages(
    graphClient: Client,
    initialUrl: string,
    entityName: string = 'items'
  ): Promise<any[]> {
    let allItems: any[] = [];
    let currentUrl = initialUrl;
    let pageCount = 0;

    try {
      while (currentUrl) {
        pageCount++;
        console.log(`Fetching ${entityName} page ${pageCount}...`);
        
        const response = await graphClient.api(currentUrl).get();
        
        if (response.value && Array.isArray(response.value)) {
          allItems = allItems.concat(response.value);
          // console.log(`Fetched ${response.value.length} ${entityName} (page ${pageCount})`);
        }
        
        // Check for nextLink to continue pagination
        currentUrl = response['@odata.nextLink'] || null;
      }
      
      // console.log(`Successfully fetched a total of ${allItems.length} ${entityName} across ${pageCount} pages`);
      return allItems;
      
    } catch (error) {
      console.error(`Error fetching ${entityName} on page ${pageCount}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to fetch all pages of ${entityName}: ${errorMessage}`);
    }
  }

  async checkADGroupConnection() {
    console.log("Starting checkADGroupConnection...");
    if (
      !process.env.DATABRICKS_CLIENT_ID ||
      !process.env.DATABRICKS_TENANT_ID ||
      !process.env.DATABRICKS_CLIENT_SECRET
    ) {
      throw new Error(
        "Missing required environment variables: DATABRICKS_CLIENT_ID, DATABRICKS_TENANT_ID, or DATABRICKS_CLIENT_SECRET."
      );
    }

    const msalConfig = {
      auth: {
        clientId: process.env.DATABRICKS_CLIENT_ID as string,
        authority: `https://login.microsoftonline.com/${process.env.DATABRICKS_TENANT_ID as string}`,
        clientSecret: process.env.DATABRICKS_CLIENT_SECRET as string,
      },
    };

    const cca = new msal.ConfidentialClientApplication(msalConfig);

    const clientCredentialRequest = {
      scopes: ["https://graph.microsoft.com/.default"],
    };

    try {
      const response = await cca.acquireTokenByClientCredential(
        clientCredentialRequest
      );

      if (response && response.accessToken) {
        console.log("Successfully acquired access token.");

        const graphClient = Client.init({
          authProvider: (
            done: (error: any, accessToken: string | null) => void
          ) => {
            done(null, response.accessToken);
          },
        });

        const groupName = process.env.AD_GROUP_NAME;
        const groupResponse = await graphClient
          .api("/groups")
          .filter(`displayName eq '${groupName}'`)
          .select("id,displayName")
          .get();

        if (groupResponse.value && groupResponse.value.length > 0) {
          const group = groupResponse.value[0];
          console.log(`Group '${group.displayName}' found with ID: ${group.id}. Fetching members...`);

          // Use the generic pagination method to fetch all members
          const allMembers = await this.fetchAllPages(
            graphClient,
            `/groups/${group.id}/members/microsoft.graph.user?$select=displayName,userPrincipalName&$expand=manager($select=displayName)`,
            'group members'
          );
          
          console.log(`Found a total of ${allMembers.length} members in group '${group.displayName}'`);
          return allMembers;
        } else {
          const notFoundMessage = `Connection established, but group '${groupName}' not found.`;
          console.log(notFoundMessage);
          throw new Error(notFoundMessage);
        }
      }
      throw new Error("Failed to acquire access token.");
    } catch (error) {
      console.error("Error during authentication or Graph API call:", error);
      throw new Error("Failed to connect to Azure AD group.");
    }
  }
}
